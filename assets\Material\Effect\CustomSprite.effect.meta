{"ver": "1.0.25", "uuid": "1f64dded-55a7-44ca-b28b-bf3273c0d79a", "compiledShaders": [{"glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matViewProj;\nuniform mat4 cc_matWorld;\nvec4 decodeDataTextrue(sampler2D dataTexture);\nvec4 loadTextrue(sampler2D dataTexture, ivec2 uv);\nivec2 getUVFromOffset(int offset, int lineLength);\nint getOffset(int index);\nint modInt(int a, int b);\nfloat decodeToFloat(vec4 color);\nfloat decodeCustomFloat(vec4 color);\nfloat decodeFloatFromRGBA_Simple(vec4 color);\nattribute vec3 a_position;\nattribute vec4 a_color;\nattribute vec4 a_color0;\nattribute vec4 a_color1;\nvarying vec4 v_color;\nvarying vec4 v_color0;\nvarying vec4 v_color1;\n#if USE_TINT\n#endif\nuniform sampler2D dataTexture;\nattribute vec4 a_texCoord1;\nvarying vec4 v_texCoord1;\n#if USE_TEXTURE\nattribute vec2 a_uv0;\nvarying vec2 v_uv0;\n#endif\nvarying vec3 v_modelPos;\nvarying vec4 v_lightColor;\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  #if CC_USE_MODEL\n  pos = cc_matViewProj * cc_matWorld * pos;\n  #else\n  pos = cc_matViewProj * pos;\n  #endif\n  #if USE_TEXTURE\n  v_uv0 = a_uv0;\n  #endif\n  #if CC_USE_MODEL\n  v_modelPos = a_position;\n  #endif\n  v_color = a_color;\n  v_color0 = a_color0;\n  v_color1 = a_color1;\n  v_texCoord1 = a_texCoord1;\n  v_lightColor = decodeDataTextrue(dataTexture);\n  gl_Position = pos;\n}\nvec4 decodeDataTextrue(sampler2D dataTexture) {\n  vec4 ambientColor = loadTextrue(dataTexture, getUVFromOffset(getOffset(0), 64));\n  vec4 lightColor = ambientColor;\n  bool pointLigthsIsOn[32];\n  ivec2 pointLigthsIsOnUV = getUVFromOffset(getOffset(1), 64);\n  vec4 pointLigthsIsOnOrigData = loadTextrue(dataTexture, pointLigthsIsOnUV);\n  for(int i = 0; i < 4; i++) {\n    float leftData;\n    if(i == 0) {\n      leftData = pointLigthsIsOnOrigData.a * 255.0;\n    } else if(i == 1) {\n      leftData = pointLigthsIsOnOrigData.b * 255.0;\n    } else if(i == 2) {\n      leftData = pointLigthsIsOnOrigData.g * 255.0;\n    } else if(i == 3) {\n      leftData = pointLigthsIsOnOrigData.r * 255.0;\n    }\n    bool boolDatas[8];\n    float modNum = 128.0;\n    for(int j = 0; j < 8; j++) {\n      boolDatas[j] = (leftData / modNum) >= 1.0;\n      leftData = mod(leftData, modNum);\n      modNum /= 2.0;\n      pointLigthsIsOn[i *8 + j] = boolDatas[j];\n    }\n  }\n  vec3 pointLightPositions[32];\n  vec4 pointLightColors[32];\n  float pointLightIntensities[32];\n  float pointLightYScales[32];\n  float pointLightInnerRadiuss[32];\n  float pointLightOuterRadiuss[32];\n  float pointLightFalloffExponents[32];\n  vec4 tempLightColor = vec4(0.0, 0.0, 0.0, 0.0);\n  for(int i = 0; i < 32; i++) {\n    if(pointLigthsIsOn[i]) {\n      vec3 pointLightPosition;\n      vec4 pointLightColor;\n      float pointLightIntensity;\n      float pointLightYScale;\n      float pointLightInnerRadius;\n      float pointLightOuterRadius;\n      float pointLightFalloffExponent;\n      for(int j = 0; j < 3; j++) {\n        ivec2 pointLightPositionUV = getUVFromOffset(getOffset(2) + i * 3 + j, 64);\n        vec4 pointLightPositionOrigData = loadTextrue(dataTexture, pointLightPositionUV);\n        float decodedFloat = decodeCustomFloat(pointLightPositionOrigData);\n        if(j == 0) {\n          pointLightPosition.x = decodedFloat;\n        } else if(j == 1) {\n          pointLightPosition.y = decodedFloat;\n        } else if(j == 2) {\n          pointLightPosition.z = decodedFloat;\n        }\n      }\n      pointLightPositions[i] = pointLightPosition;\n      ivec2 pointLightColorUV = getUVFromOffset(getOffset(3) + i, 64);\n      pointLightColor = loadTextrue(dataTexture, pointLightColorUV);\n      pointLightColors[i] = pointLightColor;\n      ivec2 pointLightIntensityUV = getUVFromOffset(getOffset(4) + i, 64);\n      vec4 pointLightIntensityOrigData = loadTextrue(dataTexture, pointLightIntensityUV);\n      pointLightIntensity = decodeToFloat(pointLightIntensityOrigData);\n      pointLightIntensities[i] = pointLightIntensity;\n      ivec2 pointLightYScaleUV = getUVFromOffset(getOffset(5) + i, 64);\n      vec4 pointLightYScaleOrigData = loadTextrue(dataTexture, pointLightYScaleUV);\n      pointLightYScale = decodeToFloat(pointLightYScaleOrigData);\n      pointLightYScales[i] = pointLightYScale;\n      ivec2 pointLightInnerRadiusUV = getUVFromOffset(getOffset(6) + i, 64);\n      vec4 pointLightInnerRadiusOrigData = loadTextrue(dataTexture, pointLightInnerRadiusUV);\n      pointLightInnerRadius = decodeToFloat(pointLightInnerRadiusOrigData);\n      pointLightInnerRadiuss[i] = pointLightInnerRadius;\n      ivec2 pointLightOuterRadiusUV = getUVFromOffset(getOffset(7) + i, 64);\n      vec4 pointLightOuterRadiusOrigData = loadTextrue(dataTexture, pointLightOuterRadiusUV);\n      pointLightOuterRadius = decodeToFloat(pointLightOuterRadiusOrigData);\n      pointLightOuterRadiuss[i] = pointLightOuterRadius;\n      ivec2 pointLightFalloffExponentUV = getUVFromOffset(getOffset(8) + i, 64);\n      vec4 pointLightFalloffExponentOrigData = loadTextrue(dataTexture, pointLightFalloffExponentUV);\n      pointLightFalloffExponent = decodeToFloat(pointLightFalloffExponentOrigData);\n      pointLightFalloffExponents[i] = pointLightFalloffExponent;\n      vec3 posSub = pointLightPositions[i] - a_position;\n      float distE = length(vec3(posSub.x, posSub.y * pointLightYScales[i], posSub.z));\n      if(distE < pointLightOuterRadiuss[i]) {\n        if(distE < pointLightInnerRadiuss[i]) {\n          lightColor += pointLightColors[i] * pointLightIntensities[i];\n        } else {\n          float t = (distE - pointLightInnerRadiuss[i]) / (pointLightOuterRadiuss[i] - pointLightInnerRadiuss[i]);\n          float falloff = pow(1.0 - t, pointLightFalloffExponents[i]);\n          lightColor += pointLightColors[i] * pointLightIntensities[i] * falloff;\n        }\n      }\n    }\n  }\n  return lightColor;\n}\nivec2 getUVFromOffset(int offset, int lineLength) {\n  int x = modInt(offset, lineLength);\n  int y = offset / lineLength;\n  return ivec2(x, y);\n}\nint modInt(int a, int b) {\n  return a - (a / b) * b;\n}\nint getOffset(int index) {\n  if (index == 0) return 0;\n  else if (index == 1) return 32;\n  else if (index == 2) return 33;\n  else if (index == 3) return 129;\n  else if (index == 4) return 161;\n  else if (index == 5) return 193;\n  else if (index == 6) return 225;\n  else if (index == 7) return 257;\n  else if (index == 8) return 289;\n  else return 0;\n}\nvec4 loadTextrue(sampler2D dataTexture, ivec2 uv) {\n    float x = float(uv.x) / 64.0;\n    float y = float(uv.y) / 5.0;\n    vec4 color = texture2D(dataTexture, vec2(x, y));\n  return color;\n}\nfloat decodeToFloat(vec4 color) {\n  return decodeFloatFromRGBA_Simple(color);\n}\nfloat decodeCustomFloat(vec4 color) {\n  vec4 bytes = color * 255.0;\n  float byte0 = bytes.r;\n  float byte1 = bytes.g;\n  float byte2 = bytes.b;\n  float byte3 = bytes.a;\n  float sign = (byte0 >= 128.0) ? -1.0 : 1.0;\n  float intPart = byte3 * 256.0 * 256.0 + byte2 * 256.0 + byte1;\n  float fracPart = mod(byte0, 128.0) / 128.0;\n  return sign * (intPart + fracPart);\n}\nfloat decodeFloatFromRGBA_Simple(vec4 color) {\n  vec4 bytes = floor(color * 255.0 + 0.5);\n  float sign = (bytes.a >= 128.0) ? -1.0 : 1.0;\n  float exp_high = mod(bytes.a, 128.0);\n  float exp_low = floor(bytes.b / 128.0);\n  float exponent = exp_high * 2.0 + exp_low - 127.0;\n  float mantissa_high = mod(bytes.b, 128.0);\n  float mantissa = (mantissa_high * 65536.0 + bytes.g * 256.0 + bytes.r) / 8388608.0;\n  if (exp_high == 0.0 && exp_low == 0.0 && mantissa == 0.0) {\n    return 0.0;\n  }\n  return sign * pow(2.0, exponent) * (1.0 + mantissa);\n}", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform float alphaThreshold;\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n      if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n      if (alpha < alphaThreshold) discard;\n  #endif\n}\nstruct hsv { float h; float s; float v; };\nvec3 fromHSV (hsv hsvInput);\nhsv toHsv(vec3 color);\nfloat ArrayMaxLen3(float array[3]);\nfloat ArrayMinLen3(float array[3]);\nvarying vec4 v_color;\nvarying vec4 v_color0;\nvarying vec4 v_color1;\nvarying vec3 v_modelPos;\nvarying vec4 v_texCoord1;\nvarying vec4 v_lightColor;\n#if USE_TEXTURE\nvarying vec2 v_uv0;\nuniform sampler2D texture;\n#endif\nuniform sampler2D dataTexture;\nvoid main () {\n  float mixRatio = v_texCoord1.w;\n  vec4 mixColor = v_color0;\n  hsv hsvValue = hsv(0.0, 0.0, 0.0);\n  float ratio = 0.0;\n    ratio = mixRatio;\n  vec4 o = vec4(1, 1, 1, 1);\n  int H = int(v_texCoord1.x);\n  int S = int(v_texCoord1.y);\n  int V = int(v_texCoord1.z);\n  #if USE_TEXTURE\n  vec4 texture_tmp = texture2D(texture, v_uv0);\n  #if CC_USE_ALPHA_ATLAS_texture\n      texture_tmp.a *= texture2D(texture, v_uv0 + vec2(0, 0.5)).r;\n  #endif\n  #if INPUT_IS_GAMMA\n    o.rgb *= (texture_tmp.rgb * texture_tmp.rgb);\n    o.a *= texture_tmp.a;\n  #else\n    o *= texture_tmp;\n  #endif\n  #endif\n  o *= v_color;\n  ALPHA_TEST(o);\n  hsvValue = toHsv(o.rgb);\n  if(H != 0) {\n    hsvValue.h += float(H) / 360.0;\n  }\n  if(S != 0) {\n    hsvValue.s *= float(100 + S) / 100.0;\n  }\n  if(V != 0) {\n    hsvValue.v *= float(100 + V) / 100.0;\n  }\n  o = vec4(fromHSV(hsvValue), o.a);\n  float ax = 1.0;\n  #if CC_USE_MODEL\n  float y = v_modelPos.y;\n  #endif\n  float a = o.a * ax;\n  float data = v_color1.r * 255.0;\n  float leftData = data;\n  bool isLightReceiver = (leftData / 128.0) >= 1.0;\n  leftData = mod(leftData, 128.0);\n  bool isChangeOpacity = (leftData / 64.0) >= 1.0;\n  leftData = mod(leftData, 64.0);\n  bool isToFrag = (leftData / 32.0) >= 1.0;\n  vec4 ambient = v_lightColor;\n  vec3 mixedColorRgb = (o.rgb * (1.0 - ratio) + mixColor.rgb * ratio);\n  if(isLightReceiver) {\n    vec3 outRgb = mixedColorRgb * (ambient.rgb * (isChangeOpacity? 1.0 : ambient.a));\n    o = vec4(outRgb, a * (isChangeOpacity? ambient.a : 1.0));\n  } else {\n    o = vec4(mixedColorRgb, a);\n  }\n  ALPHA_TEST(o);\n  gl_FragColor = o;\n}\nvec3 fromHSV (hsv hsvInput) {\n  float h = hsvInput.h;\n  float s = hsvInput.s;\n  float v = hsvInput.v;\n  float r = 0.0;\n  float g = 0.0;\n  float b = 0.0;\n  float f;\n  float p;\n  float q;\n  float t;\n  int i = 0;\n  if(s == 0.0) {\n      r = g = b = v;\n  } else {\n    if (v == 0.0) {\n      r = g = b = 0.0;\n    } else {\n      if(h == 1.0) h = 0.0;\n      if(h > 1.0) h -= 1.0;\n      if(h < 0.0) h += 1.0;\n      h *= 6.0;\n      s = s;\n      v = v;\n      i = int(h);\n      f = h - float(i);\n      p = v * (1.0 - s);\n      q = v * (1.0 - (s * f));\n      t = v * (1.0 - (s * (1.0 - f)));\n      if(i == 0) {\n        r = v;\n        g = t;\n        b = p;\n      } else if(i == 1) {\n        r = q;\n        g = v;\n        b = p;\n      } else if(i == 2) {\n        r = p;\n        g = v;\n        b = t;\n      } else if(i == 3) {\n        r = p;\n        g = q;\n        b = v;\n      } else if(i == 4) {\n        r = t;\n        g = p;\n        b = v;\n      } else if(i == 5) {\n        r = v;\n        g = p;\n        b = q;\n      }\n    }\n  }\n  return vec3(r, g, b);\n}\nhsv toHsv(vec3 color) {\n  hsv hsvResult = hsv(0.0, 0.0, 0.0);\n  float r = color.r;\n  float g = color.g;\n  float b = color.b;\n  float rgbArray[3];\n  rgbArray[0] = r;\n  rgbArray[1] = g;\n  rgbArray[2] = b;\n  float maxV = ArrayMaxLen3(rgbArray);\n  float minV = ArrayMinLen3(rgbArray);\n  float delta = 0.0;\n  hsvResult.v = maxV;\n  hsvResult.s = maxV == 0.0 ? 0.0 : ((maxV - minV) / maxV);\n  if (hsvResult.s == 0.0) hsvResult.h = 0.0;\n  else {\n    delta = maxV - minV;\n    if (r == maxV) hsvResult.h = (g - b) / delta;\n    else if (g == maxV) hsvResult.h = 2.0 + (b - r) / delta;\n    else hsvResult.h = 4.0 + (r - g) / delta;\n    hsvResult.h /= 6.0;\n    if (hsvResult.h < 0.0) hsvResult.h += 1.0;\n  }\n  return hsvResult;\n}\nfloat ArrayMaxLen3(float array[3]) {\n  return array[0] > array[1] ? (array[0] > array[2] ? array[0] : array[2]) : (array[1] > array[2] ? array[1] : array[2]);\n}\nfloat ArrayMinLen3(float array[3]) {\n  return array[0] < array[1] ? (array[0] < array[2] ? array[0] : array[2]) : (array[1] < array[2] ? array[1] : array[2]);\n}"}, "glsl3": {"vert": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nvec4 decodeDataTextrue(sampler2D dataTexture);\nvec4 loadTextrue(sampler2D dataTexture, ivec2 uv);\nivec2 getUVFromOffset(int offset, int lineLength);\nint getOffset(int index);\nint modInt(int a, int b);\nfloat decodeToFloat(vec4 color);\nfloat decodeCustomFloat(vec4 color);\nfloat decodeFloatFromRGBA_Simple(vec4 color);\nin vec3 a_position;\nin vec4 a_color;\nin vec4 a_color0;\nin vec4 a_color1;\nout vec4 v_color;\nout vec4 v_color0;\nout vec4 v_color1;\n#if USE_TINT\n#endif\nuniform sampler2D dataTexture;\nin vec4 a_texCoord1;\nout vec4 v_texCoord1;\n#if USE_TEXTURE\nin vec2 a_uv0;\nout vec2 v_uv0;\n#endif\nout vec3 v_modelPos;\nout vec4 v_lightColor;\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  #if CC_USE_MODEL\n  pos = cc_matViewProj * cc_matWorld * pos;\n  #else\n  pos = cc_matViewProj * pos;\n  #endif\n  #if USE_TEXTURE\n  v_uv0 = a_uv0;\n  #endif\n  #if CC_USE_MODEL\n  v_modelPos = a_position;\n  #endif\n  v_color = a_color;\n  v_color0 = a_color0;\n  v_color1 = a_color1;\n  v_texCoord1 = a_texCoord1;\n  v_lightColor = decodeDataTextrue(dataTexture);\n  gl_Position = pos;\n}\nvec4 decodeDataTextrue(sampler2D dataTexture) {\n  vec4 ambientColor = loadTextrue(dataTexture, getUVFromOffset(getOffset(0), 64));\n  vec4 lightColor = ambientColor;\n  bool pointLigthsIsOn[32];\n  ivec2 pointLigthsIsOnUV = getUVFromOffset(getOffset(1), 64);\n  vec4 pointLigthsIsOnOrigData = loadTextrue(dataTexture, pointLigthsIsOnUV);\n  for(int i = 0; i < 4; i++) {\n    float leftData;\n    if(i == 0) {\n      leftData = pointLigthsIsOnOrigData.a * 255.0;\n    } else if(i == 1) {\n      leftData = pointLigthsIsOnOrigData.b * 255.0;\n    } else if(i == 2) {\n      leftData = pointLigthsIsOnOrigData.g * 255.0;\n    } else if(i == 3) {\n      leftData = pointLigthsIsOnOrigData.r * 255.0;\n    }\n    bool boolDatas[8];\n    float modNum = 128.0;\n    for(int j = 0; j < 8; j++) {\n      boolDatas[j] = (leftData / modNum) >= 1.0;\n      leftData = mod(leftData, modNum);\n      modNum /= 2.0;\n      pointLigthsIsOn[i *8 + j] = boolDatas[j];\n    }\n  }\n  vec3 pointLightPositions[32];\n  vec4 pointLightColors[32];\n  float pointLightIntensities[32];\n  float pointLightYScales[32];\n  float pointLightInnerRadiuss[32];\n  float pointLightOuterRadiuss[32];\n  float pointLightFalloffExponents[32];\n  vec4 tempLightColor = vec4(0.0, 0.0, 0.0, 0.0);\n  for(int i = 0; i < 32; i++) {\n    if(pointLigthsIsOn[i]) {\n      vec3 pointLightPosition;\n      vec4 pointLightColor;\n      float pointLightIntensity;\n      float pointLightYScale;\n      float pointLightInnerRadius;\n      float pointLightOuterRadius;\n      float pointLightFalloffExponent;\n      for(int j = 0; j < 3; j++) {\n        ivec2 pointLightPositionUV = getUVFromOffset(getOffset(2) + i * 3 + j, 64);\n        vec4 pointLightPositionOrigData = loadTextrue(dataTexture, pointLightPositionUV);\n        float decodedFloat = decodeCustomFloat(pointLightPositionOrigData);\n        if(j == 0) {\n          pointLightPosition.x = decodedFloat;\n        } else if(j == 1) {\n          pointLightPosition.y = decodedFloat;\n        } else if(j == 2) {\n          pointLightPosition.z = decodedFloat;\n        }\n      }\n      pointLightPositions[i] = pointLightPosition;\n      ivec2 pointLightColorUV = getUVFromOffset(getOffset(3) + i, 64);\n      pointLightColor = loadTextrue(dataTexture, pointLightColorUV);\n      pointLightColors[i] = pointLightColor;\n      ivec2 pointLightIntensityUV = getUVFromOffset(getOffset(4) + i, 64);\n      vec4 pointLightIntensityOrigData = loadTextrue(dataTexture, pointLightIntensityUV);\n      pointLightIntensity = decodeToFloat(pointLightIntensityOrigData);\n      pointLightIntensities[i] = pointLightIntensity;\n      ivec2 pointLightYScaleUV = getUVFromOffset(getOffset(5) + i, 64);\n      vec4 pointLightYScaleOrigData = loadTextrue(dataTexture, pointLightYScaleUV);\n      pointLightYScale = decodeToFloat(pointLightYScaleOrigData);\n      pointLightYScales[i] = pointLightYScale;\n      ivec2 pointLightInnerRadiusUV = getUVFromOffset(getOffset(6) + i, 64);\n      vec4 pointLightInnerRadiusOrigData = loadTextrue(dataTexture, pointLightInnerRadiusUV);\n      pointLightInnerRadius = decodeToFloat(pointLightInnerRadiusOrigData);\n      pointLightInnerRadiuss[i] = pointLightInnerRadius;\n      ivec2 pointLightOuterRadiusUV = getUVFromOffset(getOffset(7) + i, 64);\n      vec4 pointLightOuterRadiusOrigData = loadTextrue(dataTexture, pointLightOuterRadiusUV);\n      pointLightOuterRadius = decodeToFloat(pointLightOuterRadiusOrigData);\n      pointLightOuterRadiuss[i] = pointLightOuterRadius;\n      ivec2 pointLightFalloffExponentUV = getUVFromOffset(getOffset(8) + i, 64);\n      vec4 pointLightFalloffExponentOrigData = loadTextrue(dataTexture, pointLightFalloffExponentUV);\n      pointLightFalloffExponent = decodeToFloat(pointLightFalloffExponentOrigData);\n      pointLightFalloffExponents[i] = pointLightFalloffExponent;\n      vec3 posSub = pointLightPositions[i] - a_position;\n      float distE = length(vec3(posSub.x, posSub.y * pointLightYScales[i], posSub.z));\n      if(distE < pointLightOuterRadiuss[i]) {\n        if(distE < pointLightInnerRadiuss[i]) {\n          lightColor += pointLightColors[i] * pointLightIntensities[i];\n        } else {\n          float t = (distE - pointLightInnerRadiuss[i]) / (pointLightOuterRadiuss[i] - pointLightInnerRadiuss[i]);\n          float falloff = pow(1.0 - t, pointLightFalloffExponents[i]);\n          lightColor += pointLightColors[i] * pointLightIntensities[i] * falloff;\n        }\n      }\n    }\n  }\n  return lightColor;\n}\nivec2 getUVFromOffset(int offset, int lineLength) {\n  int x = modInt(offset, lineLength);\n  int y = offset / lineLength;\n  return ivec2(x, y);\n}\nint modInt(int a, int b) {\n  return a - (a / b) * b;\n}\nint getOffset(int index) {\n  if (index == 0) return 0;\n  else if (index == 1) return 32;\n  else if (index == 2) return 33;\n  else if (index == 3) return 129;\n  else if (index == 4) return 161;\n  else if (index == 5) return 193;\n  else if (index == 6) return 225;\n  else if (index == 7) return 257;\n  else if (index == 8) return 289;\n  else return 0;\n}\nvec4 loadTextrue(sampler2D dataTexture, ivec2 uv) {\n    vec4 color = texelFetch(dataTexture, ivec2(uv.x, uv.y), 0);\n  return color;\n}\nfloat decodeToFloat(vec4 color) {\n  return decodeFloatFromRGBA_Simple(color);\n}\nfloat decodeCustomFloat(vec4 color) {\n  vec4 bytes = color * 255.0;\n  float byte0 = bytes.r;\n  float byte1 = bytes.g;\n  float byte2 = bytes.b;\n  float byte3 = bytes.a;\n  float sign = (byte0 >= 128.0) ? -1.0 : 1.0;\n  float intPart = byte3 * 256.0 * 256.0 + byte2 * 256.0 + byte1;\n  float fracPart = mod(byte0, 128.0) / 128.0;\n  return sign * (intPart + fracPart);\n}\nfloat decodeFloatFromRGBA_Simple(vec4 color) {\n  vec4 bytes = floor(color * 255.0 + 0.5);\n  float sign = (bytes.a >= 128.0) ? -1.0 : 1.0;\n  float exp_high = mod(bytes.a, 128.0);\n  float exp_low = floor(bytes.b / 128.0);\n  float exponent = exp_high * 2.0 + exp_low - 127.0;\n  float mantissa_high = mod(bytes.b, 128.0);\n  float mantissa = (mantissa_high * 65536.0 + bytes.g * 256.0 + bytes.r) / 8388608.0;\n  if (exp_high == 0.0 && exp_low == 0.0 && mantissa == 0.0) {\n    return 0.0;\n  }\n  return sign * pow(2.0, exponent) * (1.0 + mantissa);\n}", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform ALPHA_TEST {\n    float alphaThreshold;\n  };\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n      if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n      if (alpha < alphaThreshold) discard;\n  #endif\n}\nstruct hsv { float h; float s; float v; };\nvec3 fromHSV (hsv hsvInput);\nhsv toHsv(vec3 color);\nfloat ArrayMaxLen3(float array[3]);\nfloat ArrayMinLen3(float array[3]);\nin vec4 v_color;\nin vec4 v_color0;\nin vec4 v_color1;\nin vec3 v_modelPos;\nin vec4 v_texCoord1;\nin vec4 v_lightColor;\n#if USE_TEXTURE\nin vec2 v_uv0;\nuniform sampler2D texture;\n#endif\nuniform sampler2D dataTexture;\nvoid main () {\n  float mixRatio = v_texCoord1.w;\n  vec4 mixColor = v_color0;\n  hsv hsvValue = hsv(0.0, 0.0, 0.0);\n  float ratio = 0.0;\n    ratio = mixRatio;\n  vec4 o = vec4(1, 1, 1, 1);\n  int H = int(v_texCoord1.x);\n  int S = int(v_texCoord1.y);\n  int V = int(v_texCoord1.z);\n  #if USE_TEXTURE\n  vec4 texture_tmp = texture(texture, v_uv0);\n  #if CC_USE_ALPHA_ATLAS_texture\n      texture_tmp.a *= texture(texture, v_uv0 + vec2(0, 0.5)).r;\n  #endif\n  #if INPUT_IS_GAMMA\n    o.rgb *= (texture_tmp.rgb * texture_tmp.rgb);\n    o.a *= texture_tmp.a;\n  #else\n    o *= texture_tmp;\n  #endif\n  #endif\n  o *= v_color;\n  ALPHA_TEST(o);\n  hsvValue = toHsv(o.rgb);\n  if(H != 0) {\n    hsvValue.h += float(H) / 360.0;\n  }\n  if(S != 0) {\n    hsvValue.s *= float(100 + S) / 100.0;\n  }\n  if(V != 0) {\n    hsvValue.v *= float(100 + V) / 100.0;\n  }\n  o = vec4(fromHSV(hsvValue), o.a);\n  float ax = 1.0;\n  #if CC_USE_MODEL\n  float y = v_modelPos.y;\n  #endif\n  float a = o.a * ax;\n  float data = v_color1.r * 255.0;\n  float leftData = data;\n  bool isLightReceiver = (leftData / 128.0) >= 1.0;\n  leftData = mod(leftData, 128.0);\n  bool isChangeOpacity = (leftData / 64.0) >= 1.0;\n  leftData = mod(leftData, 64.0);\n  bool isToFrag = (leftData / 32.0) >= 1.0;\n  vec4 ambient = v_lightColor;\n  vec3 mixedColorRgb = (o.rgb * (1.0 - ratio) + mixColor.rgb * ratio);\n  if(isLightReceiver) {\n    vec3 outRgb = mixedColorRgb * (ambient.rgb * (isChangeOpacity? 1.0 : ambient.a));\n    o = vec4(outRgb, a * (isChangeOpacity? ambient.a : 1.0));\n  } else {\n    o = vec4(mixedColorRgb, a);\n  }\n  ALPHA_TEST(o);\n  gl_FragColor = o;\n}\nvec3 fromHSV (hsv hsvInput) {\n  float h = hsvInput.h;\n  float s = hsvInput.s;\n  float v = hsvInput.v;\n  float r = 0.0;\n  float g = 0.0;\n  float b = 0.0;\n  float f;\n  float p;\n  float q;\n  float t;\n  int i = 0;\n  if(s == 0.0) {\n      r = g = b = v;\n  } else {\n    if (v == 0.0) {\n      r = g = b = 0.0;\n    } else {\n      if(h == 1.0) h = 0.0;\n      if(h > 1.0) h -= 1.0;\n      if(h < 0.0) h += 1.0;\n      h *= 6.0;\n      s = s;\n      v = v;\n      i = int(h);\n      f = h - float(i);\n      p = v * (1.0 - s);\n      q = v * (1.0 - (s * f));\n      t = v * (1.0 - (s * (1.0 - f)));\n      if(i == 0) {\n        r = v;\n        g = t;\n        b = p;\n      } else if(i == 1) {\n        r = q;\n        g = v;\n        b = p;\n      } else if(i == 2) {\n        r = p;\n        g = v;\n        b = t;\n      } else if(i == 3) {\n        r = p;\n        g = q;\n        b = v;\n      } else if(i == 4) {\n        r = t;\n        g = p;\n        b = v;\n      } else if(i == 5) {\n        r = v;\n        g = p;\n        b = q;\n      }\n    }\n  }\n  return vec3(r, g, b);\n}\nhsv toHsv(vec3 color) {\n  hsv hsvResult = hsv(0.0, 0.0, 0.0);\n  float r = color.r;\n  float g = color.g;\n  float b = color.b;\n  float rgbArray[3];\n  rgbArray[0] = r;\n  rgbArray[1] = g;\n  rgbArray[2] = b;\n  float maxV = ArrayMaxLen3(rgbArray);\n  float minV = ArrayMinLen3(rgbArray);\n  float delta = 0.0;\n  hsvResult.v = maxV;\n  hsvResult.s = maxV == 0.0 ? 0.0 : ((maxV - minV) / maxV);\n  if (hsvResult.s == 0.0) hsvResult.h = 0.0;\n  else {\n    delta = maxV - minV;\n    if (r == maxV) hsvResult.h = (g - b) / delta;\n    else if (g == maxV) hsvResult.h = 2.0 + (b - r) / delta;\n    else hsvResult.h = 4.0 + (r - g) / delta;\n    hsvResult.h /= 6.0;\n    if (hsvResult.h < 0.0) hsvResult.h += 1.0;\n  }\n  return hsvResult;\n}\nfloat ArrayMaxLen3(float array[3]) {\n  return array[0] > array[1] ? (array[0] > array[2] ? array[0] : array[2]) : (array[1] > array[2] ? array[1] : array[2]);\n}\nfloat ArrayMinLen3(float array[3]) {\n  return array[0] < array[1] ? (array[0] < array[2] ? array[0] : array[2]) : (array[1] < array[2] ? array[1] : array[2]);\n}"}}], "subMetas": {}}