// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import GameDirector from "../../Game/GameDirector";
import GameStuffManager from "../../Game/GameStuffManager";
import GameUtils from "../../Game/GameUtils";
import GameManager from "../../GameManager";
import { ResourceType } from "../../GameStuffAndComps/Resource";
import UnitBehavior from "../UnitBehavior";


/** npc 食客行为
 * 
 */
export default class NpcDiner extends UnitBehavior {

    static gotoSeatTrackRef: {[seatRef: number]: number} = {
        1: 11,
        2: 11,
        3: 12,
        4: 12,
        5: 11,
        6: 14,
        7: 13,
        8: 15,

        9: 31,
        10: 32,
        11: 33,
        12: 31,
        13: 34,
        14: 33,

        15: 31,
        16: 35,
        17: 33,
        18: 36,
        19: 34,
        20: 37,
    };

    static seatToBedTrackRef: {[seatRef: number]: number} = {
        1: 52,
        2: 53,
        3: 53,
        4: 54,
        5: 52,
        6: 55,
        7: 53,
        8: 56,
        9: 51, // 座位 2 区的需先返回, 9 - 20 都是 51
    }

    static bedWaitingTrackRef: number = 3;

    static gotoBedTrackRefGroup: {[bedRef: number]: number[]} = {
        1: [61],
        2: [62],
        3: [63],
        4: [64],
        5: [65],
        6: [66],

        7: [4, 71],
        8: [4, 72],
        9: [4, 73],
        10: [4, 74],
        11: [4, 75],
        12: [4, 76],
    }

    // 座位
    isFindingSeat = false;
    isGotoSeat = false;
    targetSeatIndex = -1;
    isOnSeat = false;
    onSeatTime = 0;
    onSeatAllTime = 2; // 12
    isEating = false;
    eatTime = 0;
    eatAllTime = 5;
    isSeatOver = false;

    // 睡床铺
    // isCanGotoBed = true;
    get isCanGotoBed(): boolean {
        // return false;
        let isUnlockAreaBed = GameDirector.instance.GetGameState('unlock_expand_1');
        if(!isUnlockAreaBed) {
            return false;
        }
        let unlockedBedNum = 0;
        for(let i = 1; i <= 12; i++) {
            if(GameStuffManager.instance.isBedUnlocked) {
                unlockedBedNum += 1;
            }
        }
        let toBedQueue = GameStuffManager.instance.inQueueUnitList[2];
        let queueLengthLimit = Math.floor(unlockedBedNum / 3);
        return !toBedQueue || toBedQueue.length < ((queueLengthLimit <= 0) ? 1 : ((queueLengthLimit > 3) ? 3 : queueLengthLimit));
    }

    isGotoWaitingBed = false;
    isFindingBed = false;
    isGotoBed = false;
    targetBedIndex = -1;
    isOnBed = false;
    onBedTime = 0;
    onBedAllTime = 20;
    isBedOver = false;
    
    isBackFromBed = false;

    isGoback = false;
    isGobackEnd = false;
    isGone = false;

    Init() {
        super.Init();
    }

    override gameUpdate(dt: number): void {
        if(this.owner.isAlive) {
        }
        super.gameUpdate(dt);
    }

    override BehaviorUpdate(dt: number) {
        if(this.owner.isAlive) {
            if(!this.isSeatOver) {
                if(this.isFindingSeat) {
                    this.FindAEmptySeat();
                }
                if(this.isFindingSeat && this.targetSeatIndex >= 0) {
                    this.isFindingSeat = false;

                    this.owner.QueueOut();
                    this.GotoSeat(this.targetSeatIndex);
                }
                if(this.isGotoSeat) {
                    this.MoveToSeat(dt);
                }
                if(this.isOnSeat) {
                    // 只是坐着
                    this.onSeatTime += dt;
                    // if(this.onSeatTime >= this.onSeatAllTime) {
                    if(this.isEating) {
                        // 坐着吃饭
                        this.eatTime += dt;
                        if(this.eatTime >= this.eatAllTime) {
                            this.isSeatOver = true;
                            this.onSeatTime = 0;
                            this.eatTime = 0;

                            if(this.isCanGotoBed) {
                                // 去睡床铺
                                this.isGotoWaitingBed = true;
                                this.ReleaseSeat(this.targetSeatIndex);
                                this.GotoWaitingBed();
                            } else {
                                // 直接回
                                this.isBedOver = true;
                                this.isGoback = true;
                                this.ReleaseSeat(this.targetSeatIndex);
                                this.GobackFromSeat();
                            }
                        }
                    }
                }
            } else {
                if(!this.isBedOver) {
                    if(this.isGotoWaitingBed) {
                    }
                    if(this.isFindingBed) {
                        this.FindAEmptyBed();
                    }
                    if(this.isFindingBed && this.targetBedIndex >= 0) {
                        this.isFindingBed = false;

                        this.owner.QueueOut();
                        this.GotoBed(this.targetBedIndex);
                    }
                    if(this.isGotoBed) {
                        this.MoveToBed(dt);
                    }
                    if(this.isOnBed) {
                        this.onBedTime += dt;
                        if(this.onBedTime >= this.onBedAllTime) {
                            this.isBedOver = true;
                            this.onBedTime = 0;
                            // 直接回
                            this.isBedOver = true;
                            this.isBackFromBed = true;
                            this.ReleaseBed(this.targetBedIndex);
                            this.GobackFromBed();

                            // 测试
                            // this.owner.DestroySelf();
                        }
                    }
                } else {
                    if(this.isGoback) {
                    }
                    if(this.isGobackEnd) {
                        this.isGobackEnd = false;
                        this.isGoback = false;
                        this.isGone = true;
                        this.targetSeatIndex = -1;
                        this.owner.DestroySelf();
                    }
                }
            }
        }
    }

    // 座位
    GotoSeat(seatIndex: number) {
        this.SetMoveToSeatTrack();
        GameStuffManager.instance.isSeatUsed[seatIndex] = true;
        GameStuffManager.instance.onSeatNpc[seatIndex] = this.owner.info;
    }

    SetMoveToSeatTrack() {
        // this.owner.MoveToTrack(11);
        let trackRef = NpcDiner.gotoSeatTrackRef[this.targetSeatIndex + 1];
        this.owner.MoveToTrack(trackRef);
        this.owner.SetOnArriveEndCallback(()=>{
            this.isGotoSeat = true;
        });
    }


    MoveToSeat(dt: number) {
        let trgPos = cc.v2(GameUtils.rootPathPoints.seatPathPoints[this.targetSeatIndex].position);
        this.owner.MoveToTargetPos(dt, trgPos);
        let distance = GameUtils.Fake3dDistanceExpand(trgPos, this.owner.rootPosition);
        if(distance < 10) {
            this.isGotoSeat = false;
            this.SitOnSeat(this.targetSeatIndex);
            this.isOnSeat = true;
        }
    }

    SitOnSeat(seatIndex: number) {
        this.owner.unitStateMachine.Stop();
        this.owner.unitStateMachine.SpecialIdle();
        let isTurnFaceLeft = this.targetSeatIndex % 2 == 1;
        this.owner.TurnFaceDir(isTurnFaceLeft);

        this.owner.isWaitingFood = true;
        // 测试：坐一段时间后开始吃
        // GameManager.instance.LateTimeCallOnce(()=>{
        //     this.StartEat();
        // }, 3);
    }

    StartEat() {
        this.owner.isEating = true;
        this.owner.unitStateMachine.Stop();
        this.owner.unitStateMachine.SpecialIdle();
        this.isEating = true;
    }

    FindAEmptySeat() {
        this.targetSeatIndex = GameStuffManager.instance.FindAEmptySeat(this.owner.rootPosition);
    }

    ReleaseSeat(seatIndex: number) {
        this.owner.isEating = false;
        GameStuffManager.instance.isSeatUsed[seatIndex] = false;
        GameStuffManager.instance.onSeatNpc[seatIndex] = null;
        GameStuffManager.instance.PutSeatPlate(seatIndex);
    }

    // 床铺
    GotoWaitingBed() {
        let seatToBedTrackRef = -1;
        if(this.targetSeatIndex >= 8) {
            seatToBedTrackRef = NpcDiner.seatToBedTrackRef[9];
            // 先返回再去
            let trackRef = NpcDiner.gotoSeatTrackRef[this.targetSeatIndex + 1];
            this.owner.MoveToTrack(trackRef, true);
            this.owner.SetOnArriveEndCallback(()=>{
                this.GotoWaitingBedPath2(seatToBedTrackRef);
            });
        } else {
            seatToBedTrackRef = NpcDiner.seatToBedTrackRef[this.targetSeatIndex + 1];
            this.GotoWaitingBedPath2(seatToBedTrackRef);
        }
    }

    GotoWaitingBedPath2(seatToBedTrackRef: number) {
        // console.log('要去排队！');
        this.owner.MoveToTrack(seatToBedTrackRef);
        this.owner.SetOnArriveEndCallback(()=>{
            // console.log('准备去排队！');
            this.isGotoWaitingBed = false;
            this.QueueUpForFindBed();
        });
    }

    // 排队等床铺
    QueueUpForFindBed() {
        this.owner.MoveToTrack(NpcDiner.bedWaitingTrackRef, false, ()=>{
            this.owner.ReadyToQueueUp(2, 0, 1);
            this.owner.SetOnArriveQueueHeadCallback(()=>{
                if(!this.isBedOver) {
                    this.isFindingBed = true;
                }
            });
            this.owner.SetOnArriveEndCallback(()=>{});
        });
    }

    GotoBed(bedIndex: number) {
        // console.log(`去床铺！${bedIndex}`);
        this.SetMoveToBedTrack();
        GameStuffManager.instance.isBedUsed[bedIndex] = true;
        GameStuffManager.instance.onBedNpc[bedIndex] = this.owner.info;
    }

    // 走组内的全部路线
    SetMoveToBedTrack() {
        // let trackRef1 = NpcDiner.gotoBedTrackRef1;
        let trackRefGroup = NpcDiner.gotoBedTrackRefGroup[this.targetBedIndex + 1];
        let calls: ((func: Function)=>void)[] = [];
        for(let i = 0; i < trackRefGroup.length; i++) {
            let trackRef = trackRefGroup[i];
            let call = (func: Function)=>{
                this.owner.MoveToTrack(trackRef);
                this.owner.SetOnArriveEndCallback(()=>{
                    func && func();
                });
            }
            calls.push(call);
        }
        NpcDiner.SequentiallyCall(calls, ()=>{
            this.isGotoBed = true;
        });
    }

    MoveToBed(dt: number) {
        let trgPos = cc.v2(GameUtils.rootPathPoints.bedPathPoints[this.targetBedIndex].position);
        this.owner.MoveToTargetPos(dt, trgPos);
        let distance = GameUtils.Fake3dDistanceExpand(trgPos, this.owner.rootPosition);
        if(distance < 80) {
            this.isGotoBed = false;
            this.SleepOnBed(this.targetBedIndex);
            this.isOnBed = true;
        }
    }

    SleepOnBed(bedIndex: number) {
        this.owner.node.parent = GameUtils.rootGameWorld.beds[this.targetBedIndex].mountParent;
        this.owner.rootPosition = cc.v2(GameUtils.rootPathPoints.bedPathPoints[bedIndex].position);
        this.owner.isSleeping = true;
        if(bedIndex < 6 && GameDirector.instance.GetGameState('upgrade_3')) {
            this.owner.isSleepingBedUpgrade = true;
        }
        if(GameDirector.instance.GetGameState('upgrade_4')) {
            this.owner.isSleepingBedUpgrade = true;
        }
        this.owner.unitStateMachine.Stop();
        this.owner.unitStateMachine.SpecialIdle();
        this.owner.TurnFaceDir(true);
    }

    FindAEmptyBed() {
        this.targetBedIndex = GameStuffManager.instance.FindAEmptyBed(this.owner.rootPosition);
    }

    ReleaseBed(bedIndex: number) {
        this.owner.isSleeping = false;
        this.owner.isSleepingBedUpgrade = false;
        GameStuffManager.instance.isBedUsed[bedIndex] = false;
        GameStuffManager.instance.onBedNpc[bedIndex] = null;
    }

    GobackFromBed() {
        GameManager.instance.LateTimeCallOnce(()=>{
            this.owner.node.parent = GameUtils.rootGameWorld.MiddleNode;
        }, 0.3);
        if(this.targetBedIndex >= 0) {
            let bedTrackRefGroup = NpcDiner.gotoBedTrackRefGroup[this.targetBedIndex + 1];
            
            let calls: ((func: Function)=>void)[] = [];
            for(let i = bedTrackRefGroup.length - 1; i >= 0; i--) {
                let trackRef = bedTrackRefGroup[i];
                let call = (func: Function)=>{
                    this.owner.MoveToTrack(trackRef, true);
                    this.owner.SetOnArriveEndCallback(()=>{
                        func && func();
                    });
                }
                calls.push(call);
            }
            NpcDiner.SequentiallyCall(calls, ()=>{
                this.isGoback = true;
                this.owner.MoveToTrack(5);
                this.owner.SetOnArriveEndCallback(()=>{
                    // this.Goback();
                    this.isGobackEnd = true;
                });
                let payCoinNum = 5;
                if(GameDirector.instance.GetGameState('upgrade_2')) {
                    payCoinNum = 7;
                }
                if(GameDirector.instance.GetGameState('upgrade_3')) {
                    payCoinNum = 9;
                }
                if(GameDirector.instance.GetGameState('upgrade_4')) {
                    payCoinNum = 12;
                }
                GameManager.instance.LateTimeCallOnce(()=>{
                    this.PayCoin(payCoinNum, 2);
                }, 1.2);
            });
        }
    }

    GobackFromSeat() {
        let seatTrackRef = NpcDiner.gotoSeatTrackRef[this.targetSeatIndex + 1];
        this.owner.MoveToTrack(seatTrackRef, true);
        this.owner.SetOnArriveEndCallback(()=>{
            this.isGoback = true;
            this.Goback();
            let payCoinNum = 2;
            if(this.targetSeatIndex < 8 && GameDirector.instance.GetGameState('upgrade_2')) {
                payCoinNum = 4;
            }
            if(this.targetSeatIndex >= 8 && GameDirector.instance.GetGameState('upgrade_1')) {
                payCoinNum = 4;
            }
            GameManager.instance.LateTimeCallOnce(()=>{
                this.PayCoin(payCoinNum, 1);
            }, 1);
        });
    }

    Goback() {
        this.owner.MoveToTrack(2);
        this.owner.SetOnArriveEndCallback(()=>{
            this.isGobackEnd = true;
        });
    }

    
    PayCoin(num: number, coinStorageRef: number) {
        let coinStorageArea = GameUtils.rootGameWorld.coinStorageArea;
        if(coinStorageRef == 2) {
            coinStorageArea = GameUtils.rootGameWorld.coinInStorageArea;
        }
        // GameStuffManager.instance.CreateDropResource(this.centerPosition, cc.v2(), ResourceType.coin, GameUtils.rootGameWorld.coinStorageArea, 0);
        for(let i = 0; i < num; i++) {
            let lastPos = this.owner.centerPosition;
            GameManager.instance.LateTimeCallOnce(()=>{
                let pos = this.owner.isAlive ? this.owner.centerPosition : lastPos;
                GameStuffManager.instance.CreateDropResource(pos, cc.v2(), ResourceType.coin, coinStorageArea, 0);
                lastPos = pos;
            }, i * 0.06);
        }
    }


    static SequentiallyCall<T extends ((func: Function)=>void)>(calls: T[], onfinalCall: Function, isCallNow: boolean = true): Function {
        let funcs: Function[] = [];
        let finalFunc: Function = onfinalCall;
        calls.forEach((e, i)=>{
            funcs[i] = ()=>{
                e(funcs[i + 1]);
            }
        });
        funcs[calls.length] = ()=>{
            finalFunc();
        };
        if(isCallNow) {
            funcs[0]();
        }
        return funcs[0];
    }

    override DestroySelf() {
        super.DestroySelf();
    }
}
