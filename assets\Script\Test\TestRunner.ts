import { FloatEncodingTest } from "./FloatEncodingTest";

const {ccclass, property} = cc._decorator;

/**
 * 测试运行器组件
 */
@ccclass
export default class TestRunner extends cc.Component {

    @property(cc.Label)
    resultLabel: cc.Label = null;

    onLoad() {
        // 运行浮点数编码解码测试
        this.runFloatEncodingTest();
    }

    private runFloatEncodingTest() {
        console.log("开始运行浮点数编码解码测试...");
        
        try {
            const success = FloatEncodingTest.testFloatEncoding();
            
            if (this.resultLabel) {
                this.resultLabel.string = success ? 
                    "浮点数编码解码测试: 通过 ✓" : 
                    "浮点数编码解码测试: 失败 ✗";
                this.resultLabel.node.color = success ? cc.Color.GREEN : cc.Color.RED;
            }
            
            // 输出shader测试代码
            console.log("\n=== Shader测试代码 ===");
            console.log(FloatEncodingTest.generateShaderTestCode());
            
        } catch (error) {
            console.error("测试运行出错:", error);
            if (this.resultLabel) {
                this.resultLabel.string = "测试运行出错";
                this.resultLabel.node.color = cc.Color.RED;
            }
        }
    }
}
