// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import GameManager from "../GameManager";
import UnitComponent from "./UnitComponent";
import UnitStateMachineConfig, { UnitStateMachineStateProp, UnitStateMachineSwitcherConfig } from "./UnitStateMachineConfig";
import { StringDictionary } from "../LocalUtils";


/**
 * 单位状态机
 * - 控制角色 spine 和 animation 动画的播放（通常是帧动画，animation节点的结构必须符合标准）
 * - 持续播放的状态，不会改变动画的播放，会一直循环播放
 * - 单次播放结束的时候会触发对应的回调
 * 
 * 每个状态的属性：
 * - 是否是循环动画？（动画将以循环的方式播放，并且不会触发结束回调）
 * - 可以转化的目标状态（目标可被转为这些状态）
 * - 结束动作时自动转化的状态（不会立即强制播放，但会让单次动作结束时自动转化为该状态，通常为循环动画）
 * 
 */


export default class UnitStateMachine extends UnitComponent {

    private _isCurFrameSetState = false;
    private _isTryingSetCompleteState = false;

    private _isTryingStop = false;
    private _tryingStopTime = 0;
    private _isStoped = false;

    private _isLastCarryingBlock = false;

    private updateId: number = -1;

    unitStateMachineConfig: UnitStateMachineConfig = null;

    spine: sp.Skeleton = null;
    FANode: cc.Node = null;
    mode: StateViewMode = StateViewMode.spine;

    stateInfos: StringDictionary<UnitStateInfo> = null;
    stateSection: UnitStateSection = null;

    stateSwitcher: UnitStateMachineSwitcher = null;

    compliteState: UnitState = UnitState.none;

    get isRunning(): boolean {
        return this.state == UnitState.run || this.state == UnitState.attack_run || this.state == UnitState.gather_run;
    }
    get isAttacking(): boolean {
        return this.state == UnitState.attack || this.state == UnitState.attack_run;
    };
    get isSkilling(): boolean {
        return this.state == UnitState.skill;
    };
    get isGathering(): boolean {
        return this.state == UnitState.gather || this.state == UnitState.gather_run;
    };
    get isStoped(): boolean {
        return this._isStoped;
    }

    state: UnitState = UnitState.none;

    /** 初始化时调用，传入角色 spine */
    InitSpine(spine: sp.Skeleton) {
        this.mode = StateViewMode.spine;
        this.spine = spine;

        this._Init();

        this.SpineMix(spine, 'idle', 'run', 0.2, 0.1);
        this.SpineMix(spine, 'idle2', 'walk2', 0.2, 0.1);

    }

    /** 初始化时调用，传入角色 FA 帧动画节点，其子节点名字须与配置表中一致 */
    InitFA(FANode: cc.Node) {
        this.mode = StateViewMode.FA;
        this.FANode = FANode;
        
        this._Init();
    }

    /** 重置状态机的属性，如果没有 update 函数，会重新生成 */
    Reset() {
        this._Init();

        this.SetState(UnitState.idle, true);
    }

    private _Init() {
        this.unitStateMachineConfig = new UnitStateMachineConfig();
        this.unitStateMachineConfig.LoadConfig(this.owner);

        this._InitStateInfos();
        this._InitStateSwitcher();

        this._InitGameUpdate();
    }

    private _InitGameUpdate() {
        if(this.updateCallbackId < 0) {
            this.updateCallbackId = GameManager.instance.AddGameUpdate('UnitStateMachine', (dt: number)=>{
                this.gameUpdate(dt);
            }, false, 6000);
        }
        if(this.updateId < 0) {
            this.updateId = GameManager.instance.AddUpdateCallback('UnitStateMachine', (dt: number)=>{
                this.updateCallback(dt);
            }, false);
        }
    }

    private _InitStateInfos() {
        let config = this.unitStateMachineConfig;
        let props = config.props;
        this.stateInfos = {};
        // 如果有新状态，在此处添加
        this.stateInfos['none'] = new UnitStateInfo(UnitState.none, props['none']);
        this.stateInfos['idle'] = new UnitStateInfo(UnitState.idle, props['idle']);
        this.stateInfos['specialIdle'] = new UnitStateInfo(UnitState.specialIdle, props['specialIdle']);
        this.stateInfos['run'] = new UnitStateInfo(UnitState.run, props['run']);
        this.stateInfos['attack'] = new UnitStateInfo(UnitState.attack, props['attack']);
        this.stateInfos['attack_run'] = new UnitStateInfo(UnitState.attack_run, props['attack_run']);
        this.stateInfos['skill'] = new UnitStateInfo(UnitState.skill, props['skill']);
        this.stateInfos['gather'] = new UnitStateInfo(UnitState.gather, props['gather']);
        this.stateInfos['gather_run'] = new UnitStateInfo(UnitState.gather_run, props['gather_run']);
        this.stateInfos['die'] = new UnitStateInfo(UnitState.die, props['die']);
    }

    private _InitStateSwitcher() {
        let config = this.unitStateMachineConfig;
        let switcherConfig = config.switcherConfig;
        this.stateSwitcher = new UnitStateMachineSwitcher();
        this.stateSwitcher.Init(this, switcherConfig);
    }

    updateCallback(dt: number) {
        if(!this._isCurFrameSetState) {
            if(this._isTryingSetCompleteState) {
                this._isTryingSetCompleteState = false;
                this.stateSwitcher.AutoSwitchToState(this.stateSection.completeState);
            }
        }
        this._isTryingSetCompleteState = false;
        this._isCurFrameSetState = false;
    }

    gameUpdate(dt: number): void {
        super.gameUpdate(dt);

        if(this._isTryingStop) {
            this._tryingStopTime += dt;
        }
        if(!this._isStoped && this._tryingStopTime > 0.04) {
            this._isStoped = true;
            this.Stop();
        }

        if(this.state == UnitState.run && !this._isTryingStop && !this._isStoped) {
            if(this._isLastCarryingBlock != this.owner.isCarryingBlock) {
                if(this.isRunning) {
                    this.PlayRun();
                } else if(!this.isSkilling && !this.isAttacking && !this.isGathering) {
                    this.PlayIdle();
                }
                this._isLastCarryingBlock = this.owner.isCarryingBlock;
            }
        }
    }

    ReplayNowState() {
        this.CommandSend('replay');
    }

    /** 跑，可以每帧持续调用 */
    Run() {
        if(!this.isRunning) {
            this.CommandSend('to_run');
        }
        this._isTryingStop = false;
        this._tryingStopTime = 0;
        this._isStoped = false;
    }

    /** 调用可以让状态尝试进入停止状态，一定时长后会自动停止，如果此期间跑动了，就不停止 */
    TryStopRun() {
        this._isTryingStop = true;
    }

    /** 停止 */
    Stop() {
        this.CommandSend('to_idle');
        this._isStoped = true;
        this._isTryingStop = false;
        this._tryingStopTime = 0;
    }

    /** 调用特殊站立动作 */
    SpecialIdle() {
        this.CommandSend('to_specialIdle');
        this._isStoped = true;
        this._isTryingStop = false;
        this._tryingStopTime = 0;
    }

    Attack(callbacks: UnitStateSectionCallbacks = {onStateEndCallback: ()=>{}}, onFailCallback: (state: UnitState)=>void = null) {
        this.CommandSend('to_attack', callbacks, onFailCallback);
    }

    Skill(callbacks: UnitStateSectionCallbacks = {onStateEndCallback: ()=>{}}, onFailCallback: (state: UnitState)=>void = null) {
        this.CommandSend('to_skill', callbacks, onFailCallback);
    }

    Gather(callbacks: UnitStateSectionCallbacks = {onStateEndCallback: ()=>{}}, onFailCallback: (state: UnitState)=>void = null) {
        this.CommandSend('to_gather', callbacks, onFailCallback);
    }

    Die(callbacks: UnitStateSectionCallbacks = {onStateEndCallback: ()=>{}}) {
        this.CommandSend('to_die', callbacks);
    }

    /** 发出指令，切换器根据指令决定怎样切换状态 */
    CommandSend(command: string, callbacks: UnitStateSectionCallbacks = {onStateEndCallback: ()=>{}}, onFailCallback: (state: UnitState)=>void = null) {
        this.stateSwitcher.CommandSend(command, callbacks, onFailCallback);
    }
    
    /** 自动切换状态调用，如果此帧已经主动切换状态，则不进行自动切换 */
    AutoSetState(state: UnitState, isPlayAnim = true) {
        if(this._isCurFrameSetState) {
            return;
        }
        this.state = state;
        if(isPlayAnim) {
            this.PlayState(state);
        }
    }

    /** 设定状态，播放动画 */
    SetState(state: UnitState, isPlayAnim = true) {
        this.state = state;
        this._isCurFrameSetState = true;
        if(isPlayAnim) {
            this.PlayState(state);
        }
    }

    PlayState(state: UnitState) {
        switch(state) {
            case UnitState.idle:
                this.PlayIdle();
                break;
            case UnitState.specialIdle:
                this.PlaySpecialIdle();
                break;
            case UnitState.run:
                this.PlayRun();
                break;
            case UnitState.attack:
                this.PlayAttack(()=>{
                    this.stateSection.Finish();
                });
                break;
            case UnitState.attack_run:
                this.PlayAttackRun(()=>{
                    this.stateSection.Finish();
                });
                break;
            case UnitState.skill:
                this.PlaySkill(()=>{
                    this.stateSection.Finish();
                });
                break;
            case UnitState.gather:
                this.PlayGather(()=>{
                    this.stateSection.Finish();
                });
                break;
            case UnitState.gather_run:
                this.PlayGatherRun(()=>{
                    this.stateSection.Finish();
                });
                break;
            case UnitState.die:
                this.PlayDie(()=>{
                    this.stateSection.Finish();
                });
                break;
            default:
                break;
        }
    }

    /** 状态结束要自动切换状态时调用 */
    TrySetCompleteState() {
        this._isTryingSetCompleteState = true;
    }

    OnSwitchState(nowState: UnitState, toState: UnitState, isAuto?: boolean, isBreakNowState?: boolean) {
        // let nowStateName = UnitStateMachineConfig.UnitStateToStateName(nowState);
        // let toStateName = UnitStateMachineConfig.UnitStateToStateName(toState);
        // if(this.owner.tag == 'hero') {
        //     console.log(`::: ${this.owner.node.name} [ 状态切换 ] ${nowStateName} -> ${toStateName} `
        //         +  ` ${isAuto? '[ 自动 ]' : ''}`);
        // }
    }


    PlayIdle() {
        let prop = this.unitStateMachineConfig.props[UnitStateMachineConfig.STATE_NAME_idle];
        this.PlayStateAnimation(prop);
    }

    PlaySpecialIdle() {
        let prop = this.unitStateMachineConfig.props[UnitStateMachineConfig.STATE_NAME_specialIdle];
        this.PlayStateAnimation(prop);
    }

    PlayRun() {
        let prop = this.unitStateMachineConfig.props[UnitStateMachineConfig.STATE_NAME_run];
        this.PlayStateAnimation(prop);
    }

    PlayAttack(callback: Function) {
        let prop = this.unitStateMachineConfig.props[UnitStateMachineConfig.STATE_NAME_attack];
        this.PlayStateAnimation(prop, ()=>{
            callback && callback();
        });
    }

    PlayAttackRun(callback: Function) {
        let prop = this.unitStateMachineConfig.props[UnitStateMachineConfig.STATE_NAME_attack_run];
        this.PlayStateAnimation(prop, ()=>{
            callback && callback();
        });
    }

    PlaySkill(callback: Function) {
        let prop = this.unitStateMachineConfig.props[UnitStateMachineConfig.STATE_NAME_skill];
        this.PlayStateAnimation(prop, ()=>{
            callback && callback();
        });
    }

    PlayGather(callback: Function) {
        let prop = this.unitStateMachineConfig.props[UnitStateMachineConfig.STATE_NAME_gather];
        this.PlayStateAnimation(prop, ()=>{
            callback && callback();
        });
    }

    PlayGatherRun(callback: Function) {
        let prop = this.unitStateMachineConfig.props[UnitStateMachineConfig.STATE_NAME_gather_run];
        this.PlayStateAnimation(prop, ()=>{
            callback && callback();
        });
    }

    PlayDie(callback: Function) {
        let prop = this.unitStateMachineConfig.props[UnitStateMachineConfig.STATE_NAME_die];
        if(this.mode == StateViewMode.spine) {
            this.PlayStateAnimation(prop, ()=>{
                callback && callback();
            });
        } else if(this.mode == StateViewMode.FA) {
            let pauseTimePoint = -1;
            let pauseTime = 0;
            if(this.owner.spDieMark != 0) {
                if(this.owner.spDieMark == 1) {
                    pauseTimePoint = 0.1;
                    pauseTime = 0.5;
                } else if(this.owner.spDieMark == 2) {
                    pauseTimePoint = 0.1;
                    pauseTime = 0.5;
                }
            }
            this.PlayStateAnimation(prop, ()=>{
                callback && callback();
            }, pauseTimePoint, pauseTime);
            if(this.owner.spDie) {
                this.owner.spDieNode.active = true;
                let mark = this.owner.spDieMark;
                let dieName = (mark == 1) && 'die1' || (mark == 2) && 'die2' || (mark == 3) && 'die3' || (mark == 4) && 'die4' || 'die3';
                this.owner.spDie.setAnimation(0, dieName, false);
            }
        }
    }

    PlayStateAnimation(prop: UnitStateMachineStateProp, callback?: Function, pauseTimePoint: number = -1, pauseTime: number = 0) {
        if(prop.OnStateStart) {
            prop.OnStateStart();
        }
        if(this.mode == StateViewMode.spine) {
            let spineName = prop.GetSpineName ? prop.GetSpineName(this.owner) : prop.spineName;
            let isLoop = prop.isLoop;
            let speed = prop.GetAnimSpeed ? prop.GetAnimSpeed(this.owner) : prop.animSpeed;
            let trackEntry = this.spine.setAnimation(0, spineName, isLoop);
            this.spine.timeScale = speed;
            if(trackEntry) {
                if(!isLoop) {
                    this.spine.setTrackCompleteListener(trackEntry, ()=>{
                        if(prop.OnStateEnd) {
                            prop.OnStateEnd();
                        }
                        callback && callback();
                    });
                }
                if(pauseTimePoint >= 0 && pauseTime > 0) {
                    GameManager.instance.scheduleOnce(()=>{
                        trackEntry.timeScale = 0;
                    }, pauseTimePoint);
                    GameManager.instance.scheduleOnce(()=>{
                        trackEntry.timeScale = 1;
                    }, pauseTimePoint + pauseTime);
                }
            }
        } else if(this.mode == StateViewMode.FA) {
            let FANodeName = prop.GetFANodeName ? prop.GetFANodeName(this.owner) : prop.FANodeName;
            let anim = this.PlayFAAnim(this.FANode, FANodeName);
            let speed = prop.GetAnimSpeed ? prop.GetAnimSpeed(this.owner) : prop.animSpeed;
            if(anim) {
                anim.getClips()[0].speed *= speed;
                if(pauseTimePoint >= 0 && pauseTime > 0) {
                    GameManager.instance.scheduleOnce(()=>{
                        anim.pause();
                    }, pauseTimePoint);
                    GameManager.instance.scheduleOnce(()=>{
                        anim.resume();
                    }, pauseTimePoint + pauseTime);
                }
                
                if(prop.stateName == 'die') {  
                    GameManager.instance.scheduleOnce(()=>{
                        // console.log(`---------- die lastframe`);
                        callback && callback();
                    }, anim.currentClip.duration + pauseTime);
                } else {
                    anim.on('lastframe', ()=>{
                        if(prop.OnStateEnd) {
                            prop.OnStateEnd();
                        }
                        callback && callback();
                        anim.off('lastframe');
                    }, this);
                    // anim.on('finished', ()=>{
                    //     this.SetState(this.attackCompliteState);
                    //     callback && callback();
                    //     anim.off('finished');
                    // }, this);
                }
            }
        }
    }

    PlayFAAnim(FANode: cc.Node, name: string) {
        this.HideAllFAChild(FANode);
        let animNode = FANode.getChildByName(name);
        let anim = animNode.getComponent(cc.Animation);
        animNode.active = true;
        anim.setCurrentTime(0);
        anim.play();
        return anim;
    }

    HideAllFAChild(FANode: cc.Node) {
        FANode.children.forEach((e)=>{
            e.active = false;
        });
    }
    
    /** 动画融合 */
    SpineMix(spine: sp.Skeleton, anim1: string, anim2: string, time12: number, time21: number) {
        if(spine.findAnimation(anim1) && spine.findAnimation(anim2)) {
            if(time12 > 0) {
                spine.setMix(anim1, anim2, time12);
            }
            if(time21 > 0) {
                spine.setMix(anim2, anim1, time21);
            }
        }
    }
    
    DestroySelf() {
        if(this.updateCallbackId >= 0) {
            GameManager.instance.RemoveUpdateCallbackByID(this.updateCallbackId);
            this.updateCallbackId = -1;
        }
        if(this.updateId >= 0) {
            GameManager.instance.RemoveUpdateCallbackByID(this.updateId);
            this.updateId = -1;
        }
    }
}

/** 状态切换器 */
export class UnitStateMachineSwitcher {
    unitStateMachine: UnitStateMachine = null;
    switcherConfig: UnitStateMachineSwitcherConfig = null;
    get stateInfos(): StringDictionary<UnitStateInfo> {
        return this.unitStateMachine.stateInfos;
    }

    Init(unitStateMachine: UnitStateMachine, switcherConfig: UnitStateMachineSwitcherConfig) {
        this.unitStateMachine = unitStateMachine;
        this.switcherConfig = switcherConfig;
        this.unitStateMachine.stateSection = this._CreateStateSection(this.stateInfos['none']);;
    }

    CommandSend(command: string, callbacks: UnitStateSectionCallbacks = {onStateEndCallback: ()=>{}}, onFailCallback: (state: UnitState)=>void) {
        let nowState = this.unitStateMachine.stateSection.state;
        let lastCompleteState = this.unitStateMachine.stateSection.completeState;
        let cells = this.switcherConfig[command];
        if(!cells) {
            console.error(`指令未找到！ ${command}`);
            return;
        }
        let trgStateCell = cells.find((e)=>{
            if(e.fromState == nowState) {
                if(e.judgeFunc && e.judgeFunc(this.unitStateMachine.owner) || !e.judgeFunc) {
                    return true;
                }
            }
            return false;
        });
        if(!trgStateCell) {
            // console.warn(`指令中没有设定当前状态： ${command} : ${nowState}`);
        }
        if(trgStateCell) {
            if(trgStateCell.isReplay) {
                if(trgStateCell.isSwitch) {
                    this.Replay();
                } else {
                    if(onFailCallback) {
                        let stateSection = this.unitStateMachine.stateSection;
                        onFailCallback(stateSection.state);
                    }
                }
            } else {
                if(trgStateCell.isSwitch) {
                    let stateSection = this.SwitchToState(trgStateCell.toState, callbacks, trgStateCell.isBreakNowState);
                    let origOnStateFinishCallback = callbacks.onStateFinishCallback;
                    stateSection.callbacks.onStateFinishCallback = ()=>{
                        if(origOnStateFinishCallback) { origOnStateFinishCallback(); }
                        this.unitStateMachine.TrySetCompleteState();
                        // let origOnStateEndCallback = callbacks.onStateEndCallback;
                        // stateSection.callbacks.onStateEndCallback = ()=>{
                        //     if(origOnStateEndCallback) { origOnStateEndCallback(); }
                        //     this.AutoSwitchToState(stateSection.completeState);
                        // };
                    };
                } else {
                    if(onFailCallback) {
                        let stateSection = this.unitStateMachine.stateSection;
                        onFailCallback(stateSection.state);
                    }
                }
                let stateSection = this.unitStateMachine.stateSection;
                if(trgStateCell.isSetCompleteState) {
                    stateSection.completeState = trgStateCell.completeState ? trgStateCell.completeState : trgStateCell.toState;
                } else if(lastCompleteState != UnitState.none) {
                    stateSection.completeState = lastCompleteState;
                } else {
                    stateSection.completeState = UnitState.idle;
                }
            }
        }
    }

    Replay() {
        let stateSection = this.unitStateMachine.stateSection;
        this.unitStateMachine.SetState(stateSection.state, true);
    }

    SwitchToState(toState: UnitState, callbacks?: UnitStateSectionCallbacks, isBreakNowState: boolean = false) {
        // TODO: 切换到指定状态
        let nowStateSection = this.unitStateMachine.stateSection;
        let nowState = nowStateSection.state;

        if(nowState) {
            if(nowStateSection.isExecuting) {
                nowStateSection.Break();
            }
        }
        let newStateSection = this._CreateStateSection(this._GetStateInfoFromUnitState(toState), callbacks);
        this.unitStateMachine.stateSection = newStateSection;
        newStateSection.Start();
        this.unitStateMachine.OnSwitchState(nowState, toState);
        this.unitStateMachine.SetState(toState, true);
        return newStateSection;
    }

    AutoSwitchToState(toState: UnitState) {
        let nowStateSection = this.unitStateMachine.stateSection;
        let nowState = nowStateSection.state;
        if(nowState == UnitState.die) {
            return;
        }

        let newStateSection = this._CreateStateSection(this._GetStateInfoFromUnitState(toState));
        this.unitStateMachine.stateSection = newStateSection;
        newStateSection.Start();
        this.unitStateMachine.OnSwitchState(nowState, toState, true);
        this.unitStateMachine.AutoSetState(toState, true);
        return newStateSection;
    }
    
    private _GetStateInfoFromUnitState(state: UnitState) {
        return this.stateInfos[UnitStateMachineConfig.UnitStateToStateName(state)];
    }

    private _CreateStateSection(info: UnitStateInfo, callbacks?: UnitStateSectionCallbacks) {
        let unitStateSection = new UnitStateSection(info);
        unitStateSection.Init();
        if(callbacks) {
            unitStateSection.callbacks = callbacks;
        }
        return unitStateSection;
    }
}

/** 实际执行中的状态 */
export class UnitStateSection {
    info: UnitStateInfo = null;
    get state(): UnitState {
        return this.info.state;
    }
    isStarted = false;
    isExecuting = false;
    curTime = 0;
    timeOutTime = 0;
    speed = 1;

    callbacks: UnitStateSectionCallbacks = null;
    timeEvents: {time: number, callback: (time: number)=>void, isCalled: boolean, isForceCall?: boolean}[] = [];
    completeState: UnitState = UnitState.none;

    constructor(info: UnitStateInfo) {
        this.info = info;
    }

    Init() {
        this.isStarted = false;
        this.isExecuting = false;
        this.curTime = 0;
        this.timeOutTime = 0;
        this.callbacks = {onStateEndCallback: ()=>{}};
        this.callbacks.onStateStartCallback = null;
        this.callbacks.onStateBreakCallback = null;
        this.callbacks.onStateFinishCallback = null;
        this.callbacks.onStateTimeOutCallback = null;
        this.callbacks.onStateEndCallback = null;
        this.timeEvents = [];
        this.completeState = UnitState.none;
    }

    Start() {
        if(!this.isStarted) {
            this.isStarted = true;
            this.isExecuting = true;
            if(this.callbacks.onStateStartCallback) { this.callbacks.onStateStartCallback(); }
        } else {
            console.error('状态重复开启！');
        }
    }

    Update(dt: number) {
        if(!this.isExecuting) return;
        this.curTime += dt;
        if(this.timeOutTime > 0 && this.curTime >= this.timeOutTime) {
            if(this.callbacks.onStateTimeOutCallback) {
                this.callbacks.onStateTimeOutCallback();
                this.End();
            }
        }
        this.timeEvents.forEach((e)=>{
            if(!e.isCalled && this.curTime >= e.time) {
                e.isCalled = true;
                e.callback(this.curTime);
            }
        });
    }

    Break() {
        if(this.isStarted && this.isExecuting) {
            if(this.callbacks.onStateBreakCallback) { this.callbacks.onStateBreakCallback(); }
            this.End();
        }
    }

    Finish() {
        // console.log(`Finish: ${this.info.state}, isStarted: ${this.isStarted}, isExecuting: ${this.isExecuting}`);
        if(this.isStarted && this.isExecuting) {
            if(this.callbacks.onStateFinishCallback) { this.callbacks.onStateFinishCallback(); }
            this.End();
        }
    }

    End() {
        if(this.isStarted && this.isExecuting) {
            this.timeEvents.forEach((e)=>{
                if(!e.isCalled && e.isForceCall) {
                    e.isCalled = true;
                    e.callback(this.curTime);
                }
            });
            if(this.callbacks.onStateEndCallback) { this.callbacks.onStateEndCallback(); }
            this.isExecuting = false;
        }
    }

    Destroy() {
        if(this.isStarted && !this.isExecuting) {
        } else {
            console.error('摧毁时未结束状态！');
        }
    }
}

/** 此类型中包含了状态在不同时期的回调 */
export type UnitStateSectionCallbacks = {
    /** 当前状态开始时 */
    onStateStartCallback?: Function;
    /** 当前状态被其他状态打断时 */
    onStateBreakCallback?: Function;
    /** 当前状态正常结束时 */
    onStateFinishCallback?: Function;
    /** 当前状态超时结束时 */
    onStateTimeOutCallback?: Function;
    /** 当前状态结束时（此回调必定调用） */
    onStateEndCallback: Function;
}

/** 状态信息 */
export class UnitStateInfo {
    state: UnitState = UnitState.none;
    prop: UnitStateMachineStateProp = null;

    constructor(state: UnitState, prop: UnitStateMachineStateProp) {
        this.state = state;
        this.prop = prop;
    }
}

export enum UnitState {
    none,
    idle,
    specialIdle,
    run,
    attack,
    attack_run,
    skill,
    gather,
    gather_run,
    die,
}

export enum StateViewMode {
    blank,
    spine,
    FA,
}