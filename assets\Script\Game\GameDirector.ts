// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import GameManager from "../GameManager";
import GameUtils from "./GameUtils";
import LocalUtils, { StringDictionary, TweenObject } from "../LocalUtils";
import GameStuffManager from "./GameStuffManager";
import Enemy from "../GameStuffAndComps/Enemy";
import Hero from "../GameStuffAndComps/Hero";
import NPC from "../GameStuffAndComps/NPC";
import { ResourceInfo, ResourceType } from "../GameStuffAndComps/Resource";
import StatueUnlockManager from "./StatueUnlockManager";
import Weapon from "../UnitComponent/Weapon";
import EnemyCreator from "./EnemyCreator";

/**
 * 
 */

export type GameState = {
    name?: string;
    value: boolean;
    nowChangeTimes: number;
    allowChangeTimes: number;
    isOneSide: boolean;
    isSideToTrue?: boolean;
    onChangeCallback?: (value?: boolean)=>any;
}

export default class GameDirector {

    public static get instance(): GameDirector {
        if(!this._instance) {
            this._instance = new GameDirector();
        }
        return this._instance;
    }
    private static _instance: GameDirector = null;

    protected updateCallbackId = -1;

    /** 暂时无用 */
    gamePhase = 0;
    guideStep = 0;

    // isPoped = false;
    isReadyToWin = false;
    isGameOver = false;

    isHeroDeadGameOver = false;

    private get gameStates() {
        if(!this._gameStates) { this._InitGameState(); }
        return this._gameStates;
    }
    private _gameStates: StringDictionary<GameState>;

    // 解锁与否的状态
    isConveyerUnlock = false;

    isAllowUnlockStockade = false;

    // 控制开启大门
    isNPCComing = false;
    comingNPCNum = 0;

    // 推
    isHeroPushingStripLog = false;
    isHeroPushingStripStone = false;
    
    /** 河道中的栅栏是否全部解锁 */
    isAllWaterStockadeUnlock = false;

    isEnemyFollowHero = false;

    isAllowHeroDig = false;
    isFinalShootBlocks = false;

    get isHeroPushing(): boolean {
        return this.isHeroPushingStripLog || this.isHeroPushingStripStone;
    }

    get isCanBuyFood(): boolean {
        return GameUtils.rootGameWorld.FloorUIBuyFood.isAllow;
    }

    private _InitGameState() {
        this._gameStates = {};
        let normalProps = {isOneSide: false, nowChangeTimes: 0, allowChangeTimes: -1};
        let oneSideToTureProps = {isOneSide: true, isSideToTrue: true, nowChangeTimes: 0, allowChangeTimes: 1};

        this.DefineGameState('first_cook', true);
        this.DefineGameState('first_mainHero_get_food', true);
        this.DefineGameState('first_mainHero_get_coin', true);
        this.DefineGameState('first_mainHero_get_plate', true);
        this.DefineGameState('first_wash', true);
        this.DefineGameState('unlock_floorUI_1', true);
        this.DefineGameState('unlock_expand_1', true);
        this.DefineGameState('finish_unlocked_area_1', true);
        this.DefineGameState('finish_unlocked_area_seats', true);
        this.DefineGameState('finish_unlocked_area_beds', true);
        this.DefineGameState('upgrade_1', true);
        this.DefineGameState('upgrade_2', true);
        this.DefineGameState('upgrade_3', true);
        this.DefineGameState('upgrade_4', true);
        this.DefineGameState('unlock_tower_1', true);
        this.DefineGameState('unlock_tower_2', true);
        this.DefineGameState('unlock_tower_3', true);
        this.DefineGameState('unlock_tower_4', true);
    }

    DefineGameState(name: string, isOneSide: boolean, initialValue: boolean = false) {
        let normalProps = {isOneSide: false, nowChangeTimes: 0, allowChangeTimes: -1};
        let oneSideToTureProps = {isOneSide: true, isSideToTrue: true, nowChangeTimes: 0, allowChangeTimes: 1};
        if(isOneSide) {
            this._gameStates[name] = {name: name, value: initialValue, ...oneSideToTureProps};
        } else {
            this._gameStates[name] = {name: name, value: initialValue, ...normalProps};
        }
    }

    private _SetGameStateValue(state: GameState, value: boolean): number {
        if(state.allowChangeTimes != -1 && state.nowChangeTimes >= state.allowChangeTimes) {
            // 超出可更改次数
            return 2;
        }
        if(state.isOneSide) {
            if(state.isSideToTrue && value == true || !state.isSideToTrue && value == false) {
                if(state.value !== value) {
                    state.nowChangeTimes += 1;
                    state.value = value;
                    if(state.onChangeCallback) {
                        state.onChangeCallback(value);
                    } else {
                        console.log(`No callback! state: '${state.name}'`);
                    }
                    return 0;
                } else {
                    return 1;
                }
            }
        } else {
            state.nowChangeTimes += 1;
            state.value = value;
            if(state.onChangeCallback) {
                state.onChangeCallback(value);
            }
            return 0;
        }
        return 3;
    }

    /** 游戏状态
     * @ value: 状态值；
     * @ isOneSide: 控制该值是否只能单向更改；
     * @ isSideToTrue: 只能单向更改为 true / false，不能更改为另一个值
     * */
    GetGameState(propName: string): boolean | undefined {
        return (this.gameStates[propName] || undefined) && this.gameStates[propName].value;
    }

    /** 更改游戏状态为真，适用于仅改变一次的情况 */
    TrySetGameStateTrue(propName: string): number {
        let nowStateValue = this.GetGameState(propName);
        if(nowStateValue === true) {
            return -1;
        } else if(nowStateValue === false) {
            return this.TrySetGameState(propName, true);
        }
        return -2;
    }

    /** 更改游戏状态 */
    TrySetGameState(propName: string, value: boolean): number {
        let result = this._SetGameStateValue(this.gameStates[propName], value);
        if(this.gameStates[propName].value === value) {
            return result * 2;
        } else if(this.gameStates[propName].value !== value) {
            return result * 2 + 1;
        }
    }

    GameStart() {

        this.SetGameStateChangeCallbacks();
        
        // 初始化
        GameUtils.rootHeroControl.Init();
        GameStuffManager.instance.Init();
        StatueUnlockManager.instance.InitStatueUnlockUI();
        StatueUnlockManager.instance.InitFloorUnlockAreaUI();

        // 虚拟摇杆手指
        GameUtils.rootJoystickArea.ShowGuideFinger();

        this.gamePhase = 1;
        this.guideStep = 1;

        // 数据初始化

        // 游戏初始

        // 刷怪脚本初始化
        GameUtils.rootEnemyCreator.StartCreate();
        // GameManager.instance.LateFrameCall(()=>{
        //     GameUtils.rootEnemyCreator.CreateFirstWave();
        // });
        // npc脚本初始化
        GameUtils.rootNPCCreator.StartCreate();
        GameManager.instance.LateFrameCall(()=>{
            GameUtils.rootNPCCreator.CreateFirstWave();
        });

        // 地面 ui 初始解锁情况
        GameUtils.rootGameWorld.floorUnlockAreaList[0].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[1].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[2].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[3].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[4].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[5].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[6].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[7].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[8].SetCannotUnlock();

        GameUtils.rootGameWorld.floorUnlockAreaList[9].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[10].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[11].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[12].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[13].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[14].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[15].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[16].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[17].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[18].SetCannotUnlock();

        GameUtils.rootGameWorld.floorUnlockAreaList[19].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[20].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[21].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[22].SetCannotUnlock();

        GameUtils.rootGameWorld.floorUnlockAreaList[23].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[24].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[25].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[26].SetCannotUnlock();

        // 测试代码
        // GameUtils.rootGameWorld.floorUnlockAreaList[0].SetCanUnlock(true);
        // GameUtils.rootGameWorld.floorUnlockAreaList[9].SetCanUnlock(true);
        // GameUtils.rootGameWorld.floorUnlockAreaList[10].SetCanUnlock(true);
        // GameUtils.rootGameWorld.floorUnlockAreaList[10].SetCanUnlock(true);
        // GameUtils.rootGameWorld.floorUnlockAreaList[11].SetCanUnlock(true);
        // GameUtils.rootGameWorld.floorUnlockAreaList[12].SetCanUnlock(true);
        // GameUtils.rootGameWorld.floorUnlockAreaList[13].SetCanUnlock(true);
        // GameUtils.rootGameWorld.floorUnlockAreaList[14].SetCanUnlock(true);
        // GameUtils.rootGameWorld.floorUnlockAreaList[15].SetCanUnlock(true);
        // GameUtils.rootGameWorld.floorUnlockAreaList[16].SetCanUnlock(true);
        // GameUtils.rootGameWorld.floorUnlockAreaList[17].SetCanUnlock(true);

        // GameUtils.rootGameWorld.floorUnlockAreaList[22].SetCanUnlock(true);
        
        // 开局资源
        // GameUtils.mainHeroBackpack.CreateResources(ResourceType.food, 5);
        
        // let resources = GameUtils.mainHeroBackpack.CreateResources(ResourceType.food, 5);
        let resources = GameUtils.mainHeroBackpack.CreateResources(ResourceType.rawMeat, 10);
        GameUtils.mainHero.script.isCarrying = true;
        GameUtils.mainHero.script.unitStateMachine.PlayIdle();

        // 开局世界散落资源
        // GameUtils.rootGameWorld.blockClodStorageArea.CreateResources(ResourceType.blockClod, 10);
        // GameUtils.rootGameWorld.blockGoldStorageArea.CreateResources(ResourceType.blockGold, 1);

        // 开始导航
        GameUtils.rootGuideToStatue.StartGuide();
    }

    /** 在此注册游戏状态变化的回调 */
    SetGameStateChangeCallbacks() {
        this.SetGameStateChangeCallback('first_mainHero_get_coin', ()=>{
            console.log('英雄首次获得金币！');
            if(this.GetGameState('first_wash')) {
                this.TrySetGameStateTrue('unlock_floorUI_1');
            }
        });
        this.SetGameStateChangeCallback('first_wash', ()=>{
            console.log('英雄首次洗碗！');
            if(this.GetGameState('first_mainHero_get_coin')) {
                this.TrySetGameStateTrue('unlock_floorUI_1');
            }
        });
        this.SetGameStateChangeCallback('unlock_floorUI_1', ()=>{
            console.log('解锁地面UI！');
            GameUtils.rootGameWorld.floorUnlockAreaList[0].SetCanUnlock();
            GameUtils.rootGameWorld.floorUnlockAreaList[1].SetCanUnlock();
            GameUtils.rootGameWorld.floorUnlockAreaList[2].SetCanUnlock();
        });
        this.SetGameStateChangeCallback('unlock_expand_1', ()=>{
            console.log('解锁拓展1！');
            GameUtils.rootGameWorld.UnlockExpand1();
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.rootGameWorld.floorUnlockAreaList[9].SetCanUnlock();
                GameUtils.rootGameWorld.floorUnlockAreaList[10].SetCanUnlock();
                GameUtils.rootGameWorld.floorUnlockAreaList[11].SetCanUnlock();
                GameUtils.rootGameWorld.floorUnlockAreaList[12].SetCanUnlock();
                GameUtils.rootGameWorld.floorUnlockAreaList[13].SetCanUnlock();
                GameUtils.rootGameWorld.floorUnlockAreaList[14].SetCanUnlock();
                GameUtils.rootGameWorld.floorUnlockAreaList[15].SetCanUnlock();
                GameUtils.rootGameWorld.floorUnlockAreaList[16].SetCanUnlock();
            }, 2);
        });
        this.SetGameStateChangeCallback('finish_unlocked_area_1', ()=>{
            console.log('餐桌区域1解锁完成！');
            GameManager.instance.LateTimeCallOnce(()=>{
                // GameUtils.rootGameWorld.floorUnlockAreaList[0].SetCanUnlock();
                GameUtils.rootGameWorld.floorUnlockAreaList[3].SetCanUnlock();
                GameUtils.rootGameWorld.floorUnlockAreaList[4].SetCanUnlock();
                GameUtils.rootGameWorld.floorUnlockAreaList[5].SetCanUnlock();
                GameUtils.rootGameWorld.floorUnlockAreaList[6].SetCanUnlock();
                GameUtils.rootGameWorld.floorUnlockAreaList[7].SetCanUnlock();
                GameUtils.rootGameWorld.floorUnlockAreaList[8].SetCanUnlock();
            }, 1.5);
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.rootGameWorld.floorUnlockAreaList[20].SetCanUnlock();
                GameUtils.rootGameWorld.floorUnlockAreaList[23].SetCanUnlock();
                GameUtils.rootGameWorld.floorUnlockAreaList[24].SetCanUnlock();
                GameUtils.rootGameWorld.floorUnlockAreaList[25].SetCanUnlock();
                GameUtils.rootGameWorld.floorUnlockAreaList[26].SetCanUnlock();
            }, 2);
        });
        this.SetGameStateChangeCallback('finish_unlocked_area_seats', ()=>{
            console.log('餐桌所有区域解锁完成！');
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.rootGameWorld.floorUnlockAreaList[17].SetCanUnlock();
            }, 1);
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.rootGameWorld.floorUnlockAreaList[19].SetCanUnlock();
            }, 2);
        });
        this.SetGameStateChangeCallback('finish_unlocked_area_beds', ()=>{
            console.log('旅馆所有床铺解锁完成！');
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.rootGameWorld.floorUnlockAreaList[21].SetCanUnlock();
            }, 2);
        });
        this.SetGameStateChangeCallback('upgrade_1', ()=>{
            console.log('升级房间1！');
            GameUtils.rootGameWorld.UpgradeRoom(0);
        });
        this.SetGameStateChangeCallback('upgrade_2', ()=>{
            console.log('升级房间2！');
            GameUtils.rootGameWorld.UpgradeRoom(1);
        });
        this.SetGameStateChangeCallback('upgrade_3', ()=>{
            console.log('升级房间3！');
            GameUtils.rootGameWorld.UpgradeRoom(2);
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.rootGameWorld.floorUnlockAreaList[22].SetCanUnlock();
            }, 2);
        });
        this.SetGameStateChangeCallback('upgrade_4', ()=>{
            console.log('升级房间4！');
            GameUtils.rootGameWorld.UpgradeRoom(3);
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.rootGameWorld.UnlockExpand2();
                GameManager.instance.LateTimeCallOnce(()=>{
                    GameUtils.rootHeroControl.isCanHeroMove = false;
                }, 0.5);
                GameManager.instance.LateTimeCallOnce(()=>{
                    this.GameEnd();
                }, 3);
            }, 2);
        });
        this.SetGameStateChangeCallback('unlock_tower_1', ()=>{
            console.log('解锁塔1！');
            GameUtils.rootGameWorld.UnlockTower(0);
        });
        this.SetGameStateChangeCallback('unlock_tower_2', ()=>{
            console.log('解锁塔2！');
            GameUtils.rootGameWorld.UnlockTower(1);
        });
        this.SetGameStateChangeCallback('unlock_tower_3', ()=>{
            console.log('解锁塔3！');
            GameUtils.rootGameWorld.UnlockTower(2);
        });
        this.SetGameStateChangeCallback('unlock_tower_4', ()=>{
            console.log('解锁塔4！');
            GameUtils.rootGameWorld.UnlockTower(3);
        });
    }

    SetGameStateChangeCallback(propName: string, callback: (value?: boolean)=>any) {
        this.gameStates[propName].onChangeCallback = callback;
    }

    SwitchGamePause() {
        if(this.isHeroDeadGameOver) { return true; }
        let isPause = GameManager.instance.isPause;
        if(!isPause) {
            GameManager.instance.GamePause();
            GameUtils.rootLayer_1.OnSwitchGamePause(true);
            return true;
        } else {
            GameManager.instance.GameResume();
            GameUtils.rootLayer_1.OnSwitchGamePause(false);
            return false;
        }
    }

    GameFrameStep() {
        GameManager.instance.GameStep();
    }

    HeroDeadGameOver() {
        if(this.isGameOver) return;
        this.isGameOver = true;
        GameManager.instance.GamePause();
        this.isHeroDeadGameOver = true;
        LocalUtils.CpSDKEnd(false);
        LocalUtils.CpSDKClick('end');
        GameManager.instance.scheduleOnce(()=>{
            // LocalUtils.CpSDKDownload();
            GameUtils.rootLayer_1.ShowEndPage();
        }, 1);
    }

    UpgradeTower(tower_id: number) {
        let tower = GameUtils.rootGameWorld.towers[tower_id - 1];
        if(tower) {
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.OpenHeroChoosePanelOnTower(tower_id - 1);
            }, 0.5);
        } else {
            console.error(`未找到塔！${tower_id}`);
        }
    }

    FinalShootBlocks() {
        if(this.isFinalShootBlocks) return;
        this.isFinalShootBlocks = true;
        let curTime = -0.3;
        let allTime = 0.5;
        let allBlocks: ResourceInfo[] = [];
        GameUtils.rootGameWorld.blockClodStorageArea.resourceGroups.forEach((e)=>{
            allBlocks = allBlocks.concat(e.resourceList.filter((e)=>{
                return e.isInList;
            }));
        });
        GameUtils.rootGameWorld.blockWoodStorageArea.resourceGroups.forEach((e)=>{
            allBlocks = allBlocks.concat(e.resourceList.filter((e)=>{
                return e.isInList;
            }));
        });
        GameUtils.rootGameWorld.blockGoldStorageArea.resourceGroups.forEach((e)=>{
            allBlocks = allBlocks.concat(e.resourceList.filter((e)=>{
                return e.isInList;
            }));
        });
        allBlocks.sort((a, b)=>{
            return a.height - b.height;
        });
        let allBlocksNum = allBlocks.length;
        let radiusAddPerTime = 800;
        GameManager.instance.AddGameUpdate('GameDirector#FinalShootBlocks', (dt: number)=>{
            curTime += dt;
            if(curTime < 0) return;
            let curFrameShootNum = (allBlocksNum / allTime) * dt;
            for(let i = 0; i < curFrameShootNum; i++) {
                if(allBlocks.length <= 0) break;
                let block = allBlocks.pop();
                block.resourceGroup.DeleteResource(block);
                if(GameUtils.mainHero) {
                    let weaponScript = GameUtils.mainHero.script.weaponList[GameUtils.mainHero.script.mainWeaponIndex].weaponScript;
                    this.FinalShootBlock(weaponScript, block, radiusAddPerTime * curTime, block.height > 200 ? block.height : 200);
                }
            }
        });
        GameManager.instance.LateTimeCallOnce(()=>{
            let shootGold = ()=>{
                for(let i = 0; i < 5; i ++) {
                    if(GameUtils.mainHero) {
                        let weaponScript = GameUtils.mainHero.script.weaponList[GameUtils.mainHero.script.mainWeaponIndex].weaponScript;
                        this.FinalShootBlock(weaponScript, null, 500, 200);
                    }
                }
            }
            GameManager.instance.LateTimeCallOnce(()=>{
                shootGold();
            }, 0.05);
            GameManager.instance.LateTimeCallOnce(()=>{
                shootGold();
            }, 0.1);
            GameManager.instance.LateTimeCallOnce(()=>{
                shootGold();
            }, 0.15);
        }, allTime);
        GameUtils.rootCameraControl.MoveToPos(cc.v2(400, -1400));
        GameUtils.rootCameraControl.PlayCameraZoomScaleTo(0.7, 0.4);
    }

    FinalShootBlock(weaponScript: Weapon, blockResource: ResourceInfo, radiusAdd: number, height: number) {
        let weaponInfo = weaponScript.weaponInfo;
        // let weaponOner = weaponInfo.weaponOner;
        let blockType = ResourceType.blockGold;
        let startPos = GameUtils.rootGameWorld.blockWoodStorageArea.rootPosition;
        if(blockResource) {
            blockType = blockResource.type;
            startPos = blockResource.script.rootPosition;
        }
        let prefabIndex = 0;
        if(blockType == ResourceType.blockClod) {
            prefabIndex = 0;
        } else if(blockType == ResourceType.blockWood) {
            prefabIndex = 1;
        } else if(blockType == ResourceType.blockGold) {
            prefabIndex = 2;
        }
        let prefab = GameUtils.rootGameWorld.blockBulletPrefabs[prefabIndex];
        let speed = -1;
        let dir = LocalUtils.AngleToVec2(140, cc.v2(0, 1));
        let range = 100;
        let damage = 20;
        if(blockType == ResourceType.blockClod) {
            range = 100;
            damage = 20;
        } else if(blockType == ResourceType.blockWood) {
            range = 300;
            damage = 80;
        } else if(blockType == ResourceType.blockGold) {
            range = 600;
            damage = 999;
        }
        // 随机一个外围点
        let centerPos = cc.v2(550, -1250);
        let randomDir = EnemyCreator.GetRandomDir([50, 180, -180, -110]);
        let targetPos = centerPos.add(randomDir.normalize().mul(700 + radiusAdd + Math.random() * 500).scale(cc.v2(1, 0.8)));

        let bullet = weaponScript.CreateBullet(prefab, dir, true, range, 1800, true, startPos.add(cc.v2(0, -35)));
        bullet.isParabolic = true;
        bullet.parabolicTopHeightAdd = 300;
        bullet.bulletPrefabIndex = prefabIndex;
        bullet.toGroudCenter = targetPos;
        bullet.damage = damage;
        
        bullet.toGroudSrcHeight = 120 + height;
        bullet.toGroudEndHeight = 60;
        bullet.toGroudRange = 280;
        return bullet;
    }

    OnBossDead() {
        if(this.isReadyToWin) {
            this.GameEnd();
        }
    }

    OnTowerUnlock(towerIndex: number) {
    }

    OnHeroGatherd() {
    }

    OnStockadeUnlock() {
    }

    OnExpandUnlock(expandIndex: number) {
        console.log('解锁扩展！');
    }

    OnBedUnlock(index: number) {
        console.log(`解锁床${index}!`);
        
        GameManager.instance.LateTimeCallOnce(()=>{
        }, 0.5);
    }

    OnAllWaterStockadeUnlock() {
        console.log('全部水中栅栏解锁！');
        this.isAllWaterStockadeUnlock = true;
        GameUtils.rootGameWorld.stockades.forEach((e)=>{
            e.isCanUnlock = true;
        });
        GameManager.instance.LateTimeCallOnce(()=>{
            GameUtils.rootGameWorld.UnlockExpand1();
        }, 0.4);
    }

    OnSoldierGetWeapon() {
        if(this.gamePhase == 5) {
            this.gamePhase = 6;
            GameUtils.rootGuideToStatue.isAutoGuide = true;
        }
    }

    OnBossJumpingSplitting() {
        GameManager.instance.LateTimeCallOnce(()=>{
            let tower = GameUtils.rootGameWorld.towers[0];
            let hero = GameUtils.rootGameWorld.towers[0].GetTowerHero();
            let heroSrcRootPosition = hero.rootPosition;
            let imgNode = tower.entityNode.getChildByName('img');
            let img_group = tower.entityNode.getChildByName('img_group');
            let anim = img_group.getComponent(cc.Animation);
            GameManager.instance.scheduleOnce(()=>{
                LocalUtils.PlaySound('Meteorite');
                imgNode.active = false;
                img_group.active = true;
            }, 0.04);
            anim.play('anim');
            LocalUtils.SetGlobalTimeScale(0.2);
            this.GameEnd(false);
            GameManager.instance.scheduleOnce(()=>{
                LocalUtils.SetGlobalTimeScale(0.5);
                GameManager.instance.scheduleOnce(()=>{
                    LocalUtils.SetGlobalTimeScale(1);
                }, 0.3);
            }, 0.3);
            cc.tween(new TweenObject(0, (value: number)=>{
                hero.rootPosition = heroSrcRootPosition.add(cc.v2(1000, 200).mul(value));
            })).delay(0.04).call(()=>{
                let bodyY = hero.body.y;
                hero.SetHeight(0);
                hero.body.y = bodyY;
            }).to(0.6, {value: 1}).start();
            let mainHero = GameUtils.mainHero.script;
            let mainHeroSrcRootPosition = mainHero.rootPosition;
            cc.tween(new TweenObject(0, (value: number)=>{
                mainHero.rootPosition = mainHeroSrcRootPosition.add(cc.v2(40, 0).mul(value));
            })).delay(0.04).call(()=>{
                mainHero.KillSelf();
            }).to(0.1, {value: 1}).start();
        }, 0.75);
    }

    OnAllFloorUnlockAreaUnlock() {
        console.log(`### 全部已解锁！`);
        // console.log(`### 全部已解锁，游戏即将结束！！`);

        // if(!this.isAllFloorUnlockAreaUnlock) {
        //     this.isAllFloorUnlockAreaUnlock = true;
        //     GameManager.instance.LateTimeCallOnce(()=>{
        //         this.GameEnd();
        //     }, 5);
        // }
    }

    OnAllStatuesAndFloorUnlockAreaUnlock() {
        console.log(`### 全部雕像和地面 UI 已解锁 ！！`);
    }

    OnNPCComing() {
        this.isNPCComing = true;
        this.comingNPCNum += 1;
    }

    OnNPCLeave() {
        this.comingNPCNum -= 1;
        if(this.comingNPCNum == 0) {
            this.isNPCComing = false;
        }
    }

    GuideToGatheringBuilding() {
        let target = GameUtils.rootGameWorld.gatheringBuildingList[0];
        let minDistance = target.rootPosition.sub(GameUtils.mainHero.script.rootPosition).len();
        GameUtils.rootGameWorld.gatheringBuildingList.forEach((e)=>{
            let distance = e.rootPosition.sub(GameUtils.mainHero.script.rootPosition).len();
            if(distance < minDistance) {
                target = e;
                minDistance = distance;
            }
        });
        GameUtils.rootGuideToStatue.GuideResetToStuff(target);
    }

    GuideToStockade() {
        let target = GameStuffManager.instance.FindAStockade(GameUtils.mainHero.script.rootPosition);
        if(target) {
            GameUtils.rootGuideToStatue.GuideResetToStuff(target);
        }
    }

    GameEnd(isWin: boolean = true, isPause: boolean = true) {
        this.isGameOver = true;
        LocalUtils.CpSDKEnd(isWin);
        LocalUtils.CpSDKClick('end');
        GameManager.instance.scheduleOnce(()=>{
            if(isPause) {
                GameManager.instance.GamePause();
            }
            // LocalUtils.CpSDKDownload();
            GameUtils.rootLayer_1.ShowEndPage(!isWin);
        }, 0.5);
    }


}
