// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { TweenObject } from "./LocalUtils";
import SimpleLightSystemManager from "./SimpleLightSystem/SimpleLightSystemManager";
// import MySprite from "./MySprite";

const {ccclass, property, executeInEditMode} = cc._decorator;

export enum ColorMixMode {
    none,
    sprite,
    spine,
}

@ccclass
@executeInEditMode
export default class CustomSpriteData extends cc.Component {

    @property(cc.Material)
    colorMixMaterial: cc.Material = null;
    
    @property({displayName: '> 初始化材质', tooltip: '初始化材质'})
    get initMaterial(): boolean {
        return false;
    }
    set initMaterial(value: boolean) {
        this.InitMaterial();
    }

    @property({type: cc.Enum(ColorMixMode)})
    colorMixMode: ColorMixMode = ColorMixMode.none;

    @property({type: cc.Sprite, visible() {
        return this.colorMixMode == ColorMixMode.sprite;
    },})
    targetSprite: cc.Sprite = null;

    @property({type: sp.Skeleton, visible() {
        return this.colorMixMode == ColorMixMode.spine;
    },})
    targetSpine: sp.Skeleton = null;

    @property(cc.Color)
    defaultColor: cc.Color = cc.Color.WHITE;

    @property({type: cc.Float, range: [0, 1]})
    defaultRatio: number = 0.5;

    @property
    isSetHSV = false;
    @property({type: cc.Integer, range: [-180, 180], visible() {
        return this.isSetHSV;
    }})
    HSV_H: number = 0;
    @property({type: cc.Integer, range: [-100, 1000], visible() {
        return this.isSetHSV;
    }})
    HSV_S: number = 0;
    @property({type: cc.Integer, range: [-100, 1000], visible() {
        return this.isSetHSV;
    }})
    HSV_V: number = 0;

    @property
    isLightReceiver = false;
    @property({visible() {
        return this.isLightReceiver;
    }})
    isChangeOpacity: boolean = false;
    @property({visible() {
        return this.isLightReceiver;
    }})
    isToFrag: boolean = false;

    // tweenObject: TweenObject<{value1: number, value2: number}> = null;
    ratioTweenObject: TweenObject<number> = new TweenObject(0, (value: number)=>{
        this.ratio = value;
    });

    isSetData = false;
    // isSetHSVDefault = false;
    color: cc.Color;
    ratio: number;
    hsv: {h: number, s: number, v: number} = {h: 0, s: 0, v: 0};

    isInitedMaterial = false;

    private _lastColor: cc.Color = null;
    private _lastRatio: number = -1;
    private _lastHSV: {h: number, s: number, v: number} = null;

    private _material: cc.Material = null;

    private _renderTime = 0;
    private _renderTimeInterval = 0.03;

    // LIFE-CYCLE CALLBACKS:

    onLoad(): void {
        if(CC_EDITOR) {
            this.InitData();
        }
    }

    async InitData() {
        await this.SetMaterial();
        setTimeout(()=>{
            this.LoadNode();
        }, 1000);
    }

    async SetMaterial() {
        let customSpriteMaterial = SimpleLightSystemManager.instance.systemScript.customSpriteMaterial;
        if(customSpriteMaterial) {
            this.colorMixMaterial = customSpriteMaterial;
        } else {
            // @ts-ignore
            await Editor.Ipc.sendToPanel('scene', 'scene:set-property',{
                id: this.uuid,
                path: "colorMixMaterial",//要修改的属性
                type: "cc.Material",
                value: { uuid: this.LoadAssets('Material/CustomSprite.mtl') },
                isSubProp: false,
            }, ()=>{
            }, 2);
        }
    }

    // async LoadAssets(url: string) {
    //     // @ts-ignore
    //     let uuid = await cc.Editor.Message.request("asset-db", "query-uuid", url);
    //     // @ts-ignore
    //     cc.Editor.Message.send()
    // }

    LoadAssets(url: string): string {
        // cc.Editor.remote.assetdb.urlToUuid('db://assets/Scene/Main.fire');
        // @ts-ignore
        return Editor.remote.assetdb.urlToUuid('db://assets/' + url);
    }

    Init(materail: cc.Material, color?: cc.Color, ratio?: number) {
        this.colorMixMaterial = materail;
        if(color) {
            this.color = color;
        }
        if(ratio >= 0) {
            this.ratio = ratio;
        }
        this.isSetData = true;
    }

    InitMaterial() {
        if(this.colorMixMode == ColorMixMode.sprite) {
            this.targetSprite.setMaterial(0, this.colorMixMaterial);
        } else if(this.colorMixMode == ColorMixMode.spine) {
            this.targetSpine.setMaterial(0, this.colorMixMaterial);
        }
        this.isInitedMaterial = true;
    }

    LoadNode() {
        let sprite = this.node.getComponent(cc.Sprite);
        if(sprite) {
            sprite.type = cc.Sprite.Type.CUSTOM;
            this.colorMixMode = ColorMixMode.sprite;
            this.targetSprite = sprite;
            if(this.targetSprite) {
                // if(!CC_EDITOR) {
                //     // let material = cc.MaterialVariant.create(this.colorMixMaterial, this.targetSprite);
                //     // this.targetSprite.setMaterial(0, material);
                //     this.targetSprite.setMaterial(0, this.colorMixMaterial);
                // }
            }
        } else {
            // console.log(`${this.node.name} 没有 Spirte!`);
        }
        let spine = this.node.getComponent(sp.Skeleton);
        if(spine) {
            this.colorMixMode = ColorMixMode.spine;
            this.targetSpine = spine;
            if(this.targetSpine) {
                // if(!CC_EDITOR) {
                //     // let material = cc.Material.create(this.colorMixMaterial);
                //     this.targetSpine.setMaterial(0, this.colorMixMaterial);
                // }
            }
        }
        this.SetColorMix(true);
    }

    onEnable () {
        if(!this.colorMixMaterial) return;
        if(CC_EDITOR) {
            this.color = this.defaultColor;
            this.ratio = this.defaultRatio;
            this.LoadNode();
            this.SetColorMix();
        }
    }
    start () {}

    update (dt: number) {
        // this._renderTime += dt;
        // if(this._renderTime > this._renderTimeInterval) {
        //     this._renderTime = 0;
        //     if(this.targetSprite) {
        //         this.targetSprite._resetAssembler();
        //     }
        // }
        this.SetColorMix();
        if(this.targetSprite) {
            if(this.targetSprite.UpdateRenderer()) {
                // console.log('! UpdateRenderer !')
            }
        }
    }

    SetColorMix(forceSet = false) {
        if(CC_EDITOR) {
            this.color = this.defaultColor;
            this.ratio = this.defaultRatio;
            forceSet = true;
        }
        let color = this.isSetData ? this.color : this.defaultColor;
        let ratio = this.isSetData ? this.ratio : this.defaultRatio;
        let hsv = this.isSetData ? this.hsv : {h: this.HSV_H, s: this.HSV_S, v: this.HSV_V};
        if(this.colorMixMode == ColorMixMode.sprite) {
            if(!this._material) {
                this._material = this.targetSprite.getMaterial(0);
            }
            // let material: cc.Material = this._material;
            if(forceSet || this._lastColor == null || !color.equals(this._lastColor)) {
                this.SetProperty('mixColor', color);
                this._lastColor = color;
            }
            if(forceSet || this._lastRatio == -1 || ratio != this._lastRatio) {
                this.SetProperty('mixRatio', ratio);
                this._lastRatio = ratio;
            }
            if(forceSet || this._lastHSV == null || this._lastHSV.h != hsv.h || this._lastHSV.s != hsv.s || this._lastHSV.v != hsv.v) {
                if(this.isSetHSV) {
                    this.SetProperty('hsv', hsv);
                    this._lastHSV = hsv;
                }
            }
            // if(this.isSetHSV) {
            //     this.SetProperty('hsv', {h: 0, s: 0, v: 0});
            // }
            if(forceSet || SimpleLightSystemManager.instance.isDirty) {
                // if(this.isChangeOpacity) {
                //     let intensity = SimpleLightSystemManager.instance.ambientIntensity;
                //     this.SetProperty('ambientColor', SimpleLightSystemManager.instance.ambientColor);
                //     this.node.opacity = intensity < 1 ? intensity * 255 : 255;
                // } else {
                    this.SetProperty('ambientColor', SimpleLightSystemManager.instance.ambientFinalColor);
                // }
                this.SetProperty('isLightReceiver', this.isLightReceiver);
                this.SetProperty('isChangeOpacity', this.isChangeOpacity);
                this.SetProperty('isToFrag', this.isToFrag);
            }
        } else if(this.colorMixMode == ColorMixMode.spine) {
            if(!this._material) {
                this._material = this.targetSpine.getMaterial(0);
            }
            // let material: cc.Material = this._material;
            if(forceSet || this._lastColor == null || !color.equals(this._lastColor)) {
                this.SetProperty('mixColor', color);
                this._lastColor = color;
            }
            if(forceSet || this._lastRatio == -1 || ratio != this._lastRatio) {
                this.SetProperty('mixRatio', ratio);
                this._lastRatio = ratio;
            }
            if(forceSet || this._lastHSV == null || this._lastHSV.h != hsv.h || this._lastHSV.s != hsv.s || this._lastHSV.v != hsv.v) {
                if(this.isSetHSV) {
                    this.SetProperty('hsv', hsv);
                    this._lastHSV = hsv;
                }
            }
            // if(this.isSetHSV) {
            //     this.SetProperty('hsv', {h: 0, s: 0, v: 0});
            // }
        }
    }

    PlayFlashWhite() {
        this.PlayFlashColor(cc.Color.WHITE);
    }

    PlayFlashRed() {
        this.PlayFlashColor(cc.Color.RED);
    }

    PlayFlashColor(color: cc.Color, ratio: number = 0.8, time = 0.1) {
        // console.log(`设置颜色闪光！`);
        if(this.colorMixMode == ColorMixMode.sprite) {
            this.node.color = color;
        }
        this.color = color;
        // this.ratio = ratio * 0.5;
        // GameManager.instance.scheduleOnce(()=>{
        //     this.ratio = ratio;
        // }, time * 0.2);
        // GameManager.instance.scheduleOnce(()=>{
        //     this.ratio = ratio;
        // }, time * 0.4);
        // GameManager.instance.scheduleOnce(()=>{
        //     this.ratio = ratio * 0.7;
        // }, time * 0.6);
        // GameManager.instance.scheduleOnce(()=>{
        //     this.ratio = ratio * 0.5;
        // }, time * 0.8);
        // GameManager.instance.scheduleOnce(()=>{
        //     this.ratio = 0;
        // }, time);
        cc.Tween.stopAllByTarget(this.ratioTweenObject);
        if(time < 0.1) {
            cc.tween(this.ratioTweenObject).set({value: ratio / 2}).to(time, {value: ratio}).to(time * 1.5, {value: 0}).start();
        } else {
            cc.tween(this.ratioTweenObject).set({value: ratio}).delay(time).to(time * 1.5, {value: 0}).start();
        }
    }

    SetProperty(name: string, value: any) {
        // 设置材质参数方式
        // if(this._material) {
        //     this._material.setProperty(name, value);
        // }
        // 设置顶点信息方式
        if(this.targetSprite) {
            // cc.log(`设置了属性：${name} = ${value}`);
            if(name == 'mixColor' && value) {
                this.targetSprite.mixColor = value;
            }
            if(name == 'mixRatio' && value != -1) {
                this.targetSprite.ratio = value;
            }
            if(name == 'hsv' && value) {
                this.targetSprite.hsv = value;
            }
            if(name == 'ambientColor') {
                // cc.log(`设置了属性：${name} = ${value}`);
                // this.targetSprite.ambientColor = value;
                this.targetSprite.isRendererDirty = true;
                // this.SetMaterialProperty('dataTexture', SimpleLightSystemManager.instance.systemScript.dataTexture);
            }
            if(name == 'isLightReceiver') {
                this.targetSprite.isLightReceiver = value;
            }
            if(name == 'isChangeOpacity') {
                this.targetSprite.isChangeOpacity = value;
            }
            if(name == 'isToFrag') {
                this.targetSprite.isToFrag = value;
            }
        } else if(this.targetSpine) {
            if(name == 'mixRatio' && value != -1) {
                this.SetMaterialProperty(name, value);
            }
            if(name == 'mixColor' && value) {
                this.SetMaterialProperty(name, value);
            }
            // if(name == 'hsv' && value) {
            //     this.targetSprite.hsv = value;
            // }
            // if(name == 'hsv') {
            //     this.SetMaterialProperty('h', value.h);
            //     this.SetMaterialProperty('s', value.s);
            //     this.SetMaterialProperty('v', value.v);
            // }
        }
    }

    
    SetMaterialProperty(name: string, value: any) {
        if(this._material) {
            this._material.setProperty(name, value);
        }
    }
}
