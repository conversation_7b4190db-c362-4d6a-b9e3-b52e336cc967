// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import GameManager from "../GameManager";
import GameUtils, { UnitInfo } from "../Game/GameUtils";
import LocalUtils from "../LocalUtils";
import { UnitState } from "../UnitComponent/UnitStateMachine";
import { WeaponInfo } from "../UnitComponent/Weapon";
import WeaponUtils from "../Weapons/WeaponUtils";
import DriftingMeat from "./DriftingMeat";
import Enemy from "./Enemy";
import GameDirector from "../Game/GameDirector";
import GameStuffManager, { DigBlockInfo } from "../Game/GameStuffManager";
import GatheringBuilding from "./GatheringBuilding";
import PushingStuff from "./PushingStuff";
import Resource, { ResourceInfo, ResourceType } from "./Resource";
import Unit from "./Unit";
import UnitBehaviorFactory from "../UnitComponent/UnitBehaviors/UnitBehaviorFactory";
import NpcDiner from "../UnitComponent/UnitBehaviors/NpcDiner";

const {ccclass, property} = cc._decorator;

@ccclass
export default class NPC extends Unit {

    /** 
     * npc
     * 职业（身份）
     * 1. 战士（弓箭手）
     * 2. 伐木工
     * 3. 伐木工2（推木头）
     * 4. 买家|顾客|食客
     * 5. 收银员
     * 6. 剑士
     * 7. 捡肉农民
     * 8. 弓箭手（拿箭、上塔）
     * 9. 挖掘者（挖掘方块，放到存储区）
     * 10. 投掷兵（拿方块上城墙投掷）
    */

    @property(cc.Integer)
    npcRef: number = 0;

    pickUpResourceDistance = 200;

    attackDistance = 105;
    RemoteMinDistance = 480;
    RemoteAttackDistance = 500;

    tirednessProgress = 0;
    tirednessMaxProgress = 100;
    isTired = false;
    isOnBed = false;
    targetBedIndex = -1;

    isSoldier = false;
    soldierListGap = 80;
    isSoldierGetWeapon = false;
    isSoldierGotWeapon = false;
    isSoldierGotedWeapon = false;
    isSoldierFight = false;

    isFirstBuyer = false;
    isFirstWaveBuyer = false;
    firstWaveBuyerTPPosition = cc.v2();

    // isBuyerBuy = false;
    // isBuyerBuyed = false;
    // isBuyerBuyComplite = false;
    // isBuyerUIShowed = false;
    // isBuyerUIHided = false;
    // buyerBuyProgress = 0;
    // buyerBuyOverProgress = 0;
    // isBuyerBuyUITweenOver = false;
    // buyerBuyMaxProgress = 2;
    // buyerListGap = 100;

    // 座位
    isFindingSeat = false;
    isGotoSeat = false;
    targetSeatIndex = -1;
    isOnSeat = false;
    onSeatTime = 0;
    onSeatAllTime = 3;
    isSeatOver = false;


    isWorkerIsMaker = false;

    isMaatCollectorInWater = false;
    isMeatCollectorBackpackFull = false;

    isArcher = false;
    archerArrowNum = 20;
    archerArrowMaxNum = 20;
    isArcherGotoTower = false;
    isArcherMovingOnTower = false;
    isArcherMovingDownTower = false;
    isArcherGotoGetArrow = false;
    isArcherWattingForArrow = false;
    isArcherOnTower = false;
    archerTowerId = -1;
    isArcherRanOut = false;

    isDigger = false;
    isDiggerDigging = false;
    isDiggerPicking = false;
    diggerPickingBlock: DigBlockInfo = null;
    diggerFocusBlock: DigBlockInfo = null;
    diggerDigTime = 0;
    diggerDigCDTime = 1.3;
    isDiggerCarrying = false;
    // isDiggerOnWall = false;
    // isDiggerOnWallMoving = false;
    // diggerOnWallMovingPos: cc.Vec2 = cc.v2();
    // isDiggerOnWallStand = false;
    // isDiggerAttacking = false;
    // isDiggerAttacked = false;
    isDiggerDelivery = false;
    isDiggerDeliverying = false;
    isDiggerDeliveryToConveyer = false;
    isDiggerBack = false;
    isDiggerBackDone = false;

    isSlinger = false;
    isSlingerCarrying = false;
    isSlingerOnWall = false;
    isSlingerOnWallMoving = false;
    slingerOnWallMovingPos: cc.Vec2 = cc.v2();
    isSlingerOnWallStand = false;
    isSlingerAttacking = false;
    isSlingerAttacked = false;
    isSlingerBack = false;
    isSlingerBackDone = false;

    focusMoveTargetPos: cc.Vec2 = null;
    isCollectorBackToWoodStorage = false;
    collectorPathPointRef = 0;

    focusPushingStuff: PushingStuff = null;

    isPushing = false;

    private _isNormalAttack = false;
    private _isRemoteAttack = false;

    private _isAttacking = false;
    private _attackingTime = 0;
    private _attackCDTime = 0;
    private _isAttackComplete = true;

    private _isGathering = false;
    private _gatheringTime = 0;
    private _gatheringCDTime = 1.2;
    private _isGatherComplete = true;
    
    private _skillTime = 0;
    private _skillCDTime = 14;

    private _nextBuyerBuyTime = 0.08;
    private _nextBuyerBuyTimeInterval = 0.08;

    // private _isMoveToPathPoint = false;
    // private _pathPointRef = 0;
    // private _movingPathPointPos: cc.Vec2 = cc.v2();

    private _pushingLocationRef = 0;
    private _pushingLocationPoses: cc.Vec2[] = [
        cc.v2(-70, -90),
        cc.v2(140, -140),
    ];

    isCashierWorking = false;
    // private _isCashierWork = false;
    private _isCashierBackToWork = false;
    private _isCashierTakeRawMeat = false;
    private _isCashierGotoTakeRawMeat = false;
    private _isCashierTakingRawMeat = false;
    private _isCashierPuttingInStorage = false;


    // LIFE-CYCLE CALLBACKS:
    // onLoad () {}
    // start () {}
    // update (dt) {}

    protected override InitOnLoad() {
        this.isUpdatePhysicsWorld = true;
        this.unitNode = this.singlePrefabCreator.runningNode;
        super.InitOnLoad();
        this.attribute.maxHp = 4;
        this.attribute.nowHp = 4;
        if(this.npcRef == 1) {
            this.attribute.maxHp = 2;
            this.attribute.nowHp = 2;
            this.attribute.moveSpeed = 150;
        } else if(this.npcRef == 4) {
            this.attribute.moveSpeed = 300;
        } else if(this.npcRef == 5) {
            this.attribute.moveSpeed = 300;
        } else {
            this.attribute.moveSpeed = 280;
        }
        this.minDistance = 140;
        this.attackDistance = 145;
        // if(this.npcRef == 10) {
        //     this.LoadBackpack();
        // }
        // if(this.npcRef == 4) {
        //     this.buyerBuyMaxProgress = Math.floor(Math.random() * 6 + 2);
        // }

        this.gameUpdateCallbackId = GameManager.instance.AddGameUpdate('NPC', (dt: number)=>{
            this.gameUpdate(dt);
        }, false, 2030);
        
        let pos = cc.v2(this.node.x, this.node.y);
        this.node.setPosition(cc.v3());
        this.rootPosition = pos;
        this.Init();
    }
    
    Init() {
        this._isNormalAttack = false;
        this._isRemoteAttack = false;
        this._isAttacking = false;
        this._attackingTime = 0;
        this._attackCDTime = 0;
        this._isAttackComplete = true;

        this._isGathering = false;
        this._gatheringTime = 0;
        this._gatheringCDTime = 1.2;
        this._isGatherComplete = true;

        this._nextBuyerBuyTime = 0.08;
    }

    gameUpdate(dt: number) {
        if(this.isCanMove) {
            if(this.isMoveToPathPoint) {
                // 当设定了路径移动时
                this.MoveToTargetPos(dt, this.movingPathPointPos);
                let distance = this.movingPathPointPos.sub(this.rootPosition).len();
                if(distance < 10) {
                    this.isMoveToPathPoint = false;
                    this.ArrivePathPoint();
                } else {
                    // console.log(`distance: ${distance}!`);
                }
            } else {
                // 正常移动时
                // this.AI(dt);
            }
        } else {
            if(this.isAlive) {
            }
        }

        this.unitBehavior.BehaviorUpdate(dt);

        this.weaponList.forEach((e)=>{
            e.weaponScript.gameUpdate(dt);
        });

        if(this.isAlive) {
        }
    }

    /** 自动索敌、攻击和移动行为 */
    AI(dt: number) {
        if(this._isNormalAttack) {
            this._isNormalAttack = false;
            // console.log(`发动普通攻击！`);
            this._isAttacking = true;
            this._attackingTime = 0;
            this._attackCDTime = this.weaponList[this.mainWeaponIndex].weaponScript.finalCDTime / 1000;
            this._isAttackComplete = false;
            this.NormalAttack();
        } else if(this._isRemoteAttack) {
            this._isRemoteAttack = false;
            // console.log(`发动远程攻击！`);
            this._isAttacking = true;
            this._attackingTime = 0;
            this._attackCDTime = this.weaponList[this.mainWeaponIndex].weaponScript.finalCDTime / 1000;
            this._isAttackComplete = false;
            this.RemoteAttack();
        } else {
            if(this._isAttacking) {
                this._attackingTime += dt;
            } else if(this._isGathering) {
                this._gatheringTime += dt;
            } else {
                this.FocusATarget();
            }
            if(!this._isAttackComplete && this._attackingTime > this._attackCDTime) {
                // console.log(`攻击完成！`);
                this._isAttackComplete = true;
                this._isAttacking = false;
                this.FocusATarget();
            }
            if(!this._isGatherComplete && this._gatheringTime > this._gatheringCDTime) {
                // console.log(`攻击完成！`);
                this._isGatherComplete = true;
                this._isGathering = false;
                this.FocusATarget();
            }
            if(this.isAttacking) {
            } else if(this.isGathering) {
            } else {
                let targetPos: cc.Vec2 = null;
                let moveTargetPos: cc.Vec2 = null;
                if(this.focusMoveTargetPos) {
                    moveTargetPos = this.focusMoveTargetPos;
                } else if(this.focusMoveTarget) {
                    moveTargetPos = this.focusMoveTarget.rootPosition;
                } else if(this.focusPushingStuff) {
                    if(!this.focusPushingStuff.isNpcPushing) {
                        this.isPushing = false;
                    }
                    if(this.focusPushingStuff.npcPushingMovePosRef == 0) {
                        moveTargetPos = this.focusPushingStuff.rootPosition.add(this._pushingLocationPoses[0]);
                    } else if(this.focusPushingStuff.npcPushingMovePosRef == 2) {
                        let offset = this.focusPushingStuff.GetNewPosAdd();
                        moveTargetPos = this.focusPushingStuff.rootPosition.add(offset.mul(0.5)).add(this._pushingLocationPoses[1]);
                        // moveTargetPos = this.focusPushingStuff.rootPosition.add(this._pushingLocationPoses[1]);
                    } else {
                        moveTargetPos = this.focusPushingStuff.rootPosition.add(this._pushingLocationPoses[1]);
                    }
                }
                if(this.focusUnit) {
                    targetPos = this.focusUnit.rootPosition;
                } else if(this.focusStuff) {
                    targetPos = this.focusStuff.rootPosition;
                }
                if(this.isPushing) {
                    this.attribute.moveDir = moveTargetPos.sub(this.rootPosition).normalize();
                    this.Move(moveTargetPos);
                } else if(moveTargetPos) {
                    // console.log(`move to ${moveTargetPos.toString()}!`);
                    this.MoveToTargetPos(dt, moveTargetPos, false);
                } else {
                    if(!targetPos) {
                        // console.log('没有目标！');
                        return;
                    }
                    this.MoveToTargetPos(dt, targetPos, true);
                }
                if(this.npcRef == 1 || this.npcRef == 6) {
                    // 接近后发动攻击
                    if(this.weaponList.length > 0 && this.weaponList[this.mainWeaponIndex].weaponProps.weaponName == 'Combat') {
                        // let distance = targetPos.sub(this.rootPosition).len();
                        let distance = GameUtils.Fake3dDistanceExpand(targetPos, this.rootPosition);
        
                        let attckDir = this.focusUnit.centerPosition.sub(this.centerPosition).normalize();
                        if(attckDir.equals(cc.v2(0, 0))) {
                            attckDir = this.attribute.attackDir;
                        }
                        this.attribute.attackDir = attckDir;
                        this.attribute.gatherDir = attckDir;
        
                        if(this._isAttackComplete && distance <= this.attackDistance) {
                            this._isNormalAttack = true;
                        }
                    } else if(this.weaponList.length > 0 && this.weaponList[this.mainWeaponIndex].weaponProps.weaponName == 'HorizontalBow') {
                        let distance = GameUtils.Fake3dDistanceExpand(targetPos, this.rootPosition);
        
                        let attckDir = this.focusUnit.centerPosition.sub(this.centerPosition).normalize();
                        if(attckDir.equals(cc.v2(0, 0))) {
                            attckDir = this.attribute.attackDir;
                        }
                        this.attribute.attackDir = attckDir;
        
                        if(this._isAttackComplete && distance <= this.RemoteAttackDistance) {
                            this._isRemoteAttack = true;
                        }
                    }
                } else if(this.npcRef == 2 || this.npcRef == 3) {
                    if(targetPos && !this.isPushing) {
                        let distance = GameUtils.Fake3dDistanceExpand(targetPos, this.rootPosition);
            
                        let gatherDir = targetPos.sub(this.centerPosition).normalize();
                        if(gatherDir.equals(cc.v2(0, 0))) {
                            gatherDir = this.attribute.gatherDir;
                        }
                        this.attribute.gatherDir = gatherDir;
                        this.attribute.attackDir = gatherDir;
            
                        if(this.focusStuff && this._isGatherComplete && distance <= this.attackDistance + (this.focusStuff ? this.focusStuff.hitboxRadius : 0)) {
                            this._isGatherComplete = false;
                            this._isGathering = true;
                            this._gatheringTime = 0;
                            this.OnGather();
                        }
                    }
                    if(this.focusPushingStuff && !this.focusPushingStuff.isNpcPushing) {
                        let distanceToTarget = GameUtils.Fake3dDistanceExpand(moveTargetPos, this.rootPosition);
                        if(distanceToTarget < 4) {
                            this.isPushing = true;
                            this.focusPushingStuff.isNpcPushing = true;
                            if(this.focusPushingStuff.npcPushingMovePosRef == 2) {
                                this.focusPushingStuff.isNpcPushingInHole = true;
                            }
                        } else {
                            this.isPushing = false;
                        }
                    }
                }
            }
        }
    }

    NormalAttack() {
        if(this.weaponList.length > 0) {
            this.unitStateMachine.Stop();
            let attackTime = 0.4;
            this.weaponList[this.mainWeaponIndex].weaponScript.NormalAttack(attackTime);
        }
    }

    RemoteAttack() {
        if(this.weaponList.length > 0) {
            this.unitStateMachine.Stop();
            this.weaponList[this.mainWeaponIndex].weaponScript.RemoteAttack();
        }
    }

    OnBorn(): void {
        super.OnBorn();
        // console.log(`npc ${this.npcRef}: on born!`);
        GameManager.instance.LateFrameCall(()=>{
            
            // this.EquipWeapon();
            
            // LocalUtils.PlaySound('level_up');
            // GameUtils.rootGameWorldUI.PlayLevelUp(false, this);

            if(this.npcRef == 4) {
                // GameUtils.rootGameWorldUI.GenerateNewNPCBuyProgressBar(this);
                this.NpcDinerMove();
                
                if(this.isFirstWaveBuyer) {
                    // 直接开始排队
                    this.pathPointRef = 1;
                    this.ArrivePathPoint();
                }
            }
        });
    }

    // override OnShoot() {
    //     super.OnShoot();
    // }

    OnGather() {
        super.OnGather();
        if(this.npcRef == 2 || this.npcRef == 3) {
            GameManager.instance.LateTimeCallOnce(()=>{
                LocalUtils.PlaySound('kan');
                let gatheringBuilding = (this.focusStuff as GatheringBuilding);
                gatheringBuilding.Gather();
                gatheringBuilding.GatherAndDrop(this.attribute.gatherDir.x < 0, true);
            }, 0.3);
        }
    }

    
    ReadyToDead() {
        super.ReadyToDead();
        GameStuffManager.instance.RemoveNpc(this.unit_id);
    }

    TryMoveToBed(dt: number) {
        let targetBedIndex = this.targetBedIndex;
        // if(targetBedIndex < 0) {
        //     targetBedIndex = GameStuffManager.instance.FindAEmptyBed(this.rootPosition);
        //     this.targetBedIndex = targetBedIndex;
        // }
        if(targetBedIndex >= 0) {
            let targetBedPos = cc.v2(GameUtils.rootGameWorld.bedNodes[targetBedIndex].position);
            if(!this.isOnBed) {
                this.MoveToTargetPos(dt, targetBedPos);
                let distance = GameUtils.Fake3dDistanceExpand(targetBedPos, this.rootPosition);
                if(distance < 10) {
                    this.GotoBed(targetBedIndex);
                }
            }
        } else {
        }
    }

    GotoBed(bedIndex: number) {
        this.isOnBed = true;
        GameManager.instance.LateTimeCallOnce(()=>{
            this.isOnBed = false;
            this.isTired = false;
            this.tirednessProgress = 0;
            this.targetBedIndex = -1;
            GameStuffManager.instance.isBedUsed[bedIndex] = false;
            this.unitStateMachine.Stop();
        }, 3);
        this.unitStateMachine.Stop();
        this.unitStateMachine.SpecialIdle();
        this.TurnFaceDir(false);
        GameManager.instance.LateFrameCall(()=>{
            this.TurnFaceDir(false);
        });
    }

    override LoadBackpack() {
        super.LoadBackpack();
        this.backpack.isResourceNumLimit = true;
        this.backpack.resourceNumLimitValue = 10;
    }

    EquipWeapon() {
        let weaponScript = null;
        if(this.npcRef == 1) {
            if(this.isSoldier) {
                weaponScript = new WeaponUtils.weaponTypeList['HorizontalBow'](this);
            } else {
                weaponScript = new WeaponUtils.weaponTypeList['NPCBow'](this);
            }
        } else if(this.npcRef == 2) {
            weaponScript = new WeaponUtils.weaponTypeList['Combat'](this);
        } else if(this.npcRef == 6) {
            weaponScript = new WeaponUtils.weaponTypeList['Combat'](this);
        } else if(this.npcRef == 8) {
            weaponScript = new WeaponUtils.weaponTypeList['NPCBow'](this);
        } else if(this.npcRef == 10) {
            weaponScript = new WeaponUtils.weaponTypeList['Block'](this);
        }
        if(weaponScript) {
            let weaponInfo = new WeaponInfo(weaponScript);
            weaponInfo.weaponOner = this;
            weaponInfo.weaponScript.Init(weaponInfo);
            if(this.npcRef == 2 || this.isSoldier || this.npcRef == 6 || this.npcRef == 8 || this.npcRef == 10) {
                weaponInfo.weaponScript.isAutoShoot = false;
            }
            if(this.npcRef == 6) {
                weaponInfo.weaponBuffs.damageUp = 3900;
                weaponInfo.weaponBuffs.attackSpeedUp = 100;
            }
            // weaponInfo.weaponBuffs.attckSpeedUp = 100;
            // weaponInfo.BulletCopyUp(2);
            this.weaponList.push(weaponInfo);
            this.mainWeaponIndex = 0;
        }
    }

    override LoadUnitBehavior() {
        if(this.npcRef == 4) {
            this.unitBehavior = UnitBehaviorFactory.CreateUnitBehavior('NpcDiner', this);
            this.unitBehavior.Init();
        }
    }

    FocusATarget() {
        // this.FocusEnemy();
        // if(!this.isCollectorBackToWoodStorage) {
        //     // 如果累了就去床铺，没床铺就去火堆
        //     if(this.isTired) {
        //         this.TryUseBedOrWaitting(1);
        //     } else {
        //         this.FocusWoodResource();
        //         // console.log(`focusMoveTarget: ${this.focusMoveTarget}`);
        //         this.FocusGatheringBuilding();
        //         if(!this.focusMoveTarget && !this.focusStuff) {
        //             let pos = cc.v2(GameUtils.rootPathPoints.npcPathPoints[2 + this.collectorPathPointRef].position);
        //             this.focusMoveTargetPos = pos;
        //         } else {
        //             this.focusMoveTargetPos = null;
        //         }
        //     }
        // }
    }

    TryUseBedOrWaitting(waittingPointRef: number) {
        let bedIndex = GameStuffManager.instance.FindAEmptyBed(this.rootPosition);
        if(bedIndex >= 0) {
            GameStuffManager.instance.isBedUsed[bedIndex] = true;
            this.targetBedIndex = bedIndex;   
        } else {
            let pos = cc.v2(GameUtils.rootPathPoints.npcWaittingPathPoints[waittingPointRef].position);
            this.focusMoveTargetPos = pos;
        }
    }

    // MoveToMeatCollectorPoint(onCloseTo: Function = null) {
    //     let pos = cc.v2(GameUtils.rootPathPoints.npcWaittingPathPoints[0].position);
    //     if(this.rootPosition.sub(pos).len() < 10) {
    //         onCloseTo && onCloseTo();
    //     } else {
    //         this.focusMoveTargetPos = pos;
    //         this.focusMoveTarget = null;
    //     }
    // }


    // FocusWoodResource() {
    //     let nearWoodResources: ResourceInfo[] = [];
    //     let nearWoodResource: Resource = null;
    //     // if(this.focusMoveTarget) {
    //     //     console.log(`isCanPickUp: ${(this.focusMoveTarget as Resource).info.isCanPickUp}, isOnGroud: ${(this.focusMoveTarget as Resource).info.isOnGroud}`);
    //     // }
    //     if(!this.focusMoveTarget || this.focusMoveTarget.tag != 'resource' || !(this.focusMoveTarget as Resource).info.isOnGroud) {
    //         nearWoodResources = GameStuffManager.instance.TryGetResourcesInArea(this.rootPosition, 700, ResourceType.wood);
    //         if(nearWoodResources.length > 0) {
    //             nearWoodResource = nearWoodResources[Math.floor(Math.random() * nearWoodResources.length)].script;
    //         }
    //         if(!nearWoodResource) {
    //             // console.log('附近没有木头！');
    //         }
    //         this.focusMoveTarget = nearWoodResource;
    //     }
    // }

    FocusGatheringBuilding() {
        let nearGatheringBuildings: GatheringBuilding[] = [];
        let nearGatheringBuilding: GatheringBuilding = null;
        if(!this.focusStuff || (this.focusStuff as GatheringBuilding).isDead) {
            nearGatheringBuildings = GameStuffManager.instance.TryGetGatheringBuildingInArea(this.rootPosition, 1000, 100);
            if(nearGatheringBuildings.length > 0) {
                nearGatheringBuilding = nearGatheringBuildings[Math.floor(Math.random() * nearGatheringBuildings.length)];
            } else {
                nearGatheringBuilding = GameStuffManager.instance.FindAGatheringBuilding(this.rootPosition, 100);
            }
            if(!nearGatheringBuilding) {
                // console.log('附近没有树！');
                this.focusStuff = null;
                return false;
            }
            this.focusStuff = nearGatheringBuilding;
        }
        if(this.focusStuff) {
            let posSub = this.focusStuff.rootPosition.sub(this.rootPosition);
            let distance = posSub.len();
            let dir = posSub.normalize();
            this.attribute.gatherDir = dir;
            this.attribute.attackDir = dir;
            // this.OnGather();
            return true;
        }
        return false;
    }


    FocusEnemy() {
        let enemyInfo: UnitInfo = GameStuffManager.instance.FindAEnemy(this.rootPosition);
        if(!enemyInfo) { return; }
        let enemy = enemyInfo.script as Enemy;
        if(enemy) {
            let isFocusEnemy = false;
            let findDistance = 1500;
            if(this.isSoldier) {
                let distance1 = 800;
                let distance2 = 1500;
                let distance3 = 2000;
                let isFocusNew = false;
                if(this.npcRef == 6 || this.npcRef == 10) {
                    if(this.focusUnit && this.focusUnit.isValid && this.focusUnit.isAlive) {
                    } else {
                        isFocusNew = true;
                    }
                } else {
                    if(this.focusUnit && this.focusUnit.isValid && this.focusUnit.isAlive) {
                        enemy = this.focusUnit as Enemy;
                        let distance = GameUtils.Fake3dDistanceExpand(this.centerPosition, this.focusUnit.centerPosition);
                        if(distance > distance3) {
                            isFocusNew = true;
                        }
                    } else {
                        isFocusNew = true;
                    }
                }
                if(isFocusNew) {
                    let enemyInfos = GameStuffManager.instance.TryGetEnemysInArea(this.rootPosition, distance1);
                    if(enemyInfos.length > 0) {
                        enemy = enemyInfos[Math.floor(Math.random() * enemyInfos.length)].script as Enemy;
                    } else {
                        enemyInfos = GameStuffManager.instance.TryGetEnemysInArea(this.rootPosition, distance2);
                        if(enemyInfos.length > 0) {
                            enemy = enemyInfos[Math.floor(Math.random() * enemyInfos.length)].script as Enemy;
                        } else {
                            enemyInfos = GameStuffManager.instance.TryGetEnemysInArea(this.rootPosition, distance3);
                            if(enemyInfos.length > 0) {
                                enemy = enemyInfos[Math.floor(Math.random() * enemyInfos.length)].script as Enemy;
                            }
                        }
                    }
                    findDistance = 2000;
                }
                if(enemy) {
                    isFocusEnemy = true;
                }
            } else {
                let distance = GameUtils.Fake3dDistanceExpand(this.centerPosition, enemy.centerPosition);
                if(distance < findDistance) {
                    isFocusEnemy = true;
                }
            }
            if(isFocusEnemy) {
                this.focusUnit = enemy;
                let attckDir = enemy.centerPosition.sub(this.centerPosition).normalize();
                if(attckDir.equals(cc.v2(0, 0))) {
                    attckDir = this.attribute.attackDir;
                }
                this.attribute.attackDir = attckDir;
            } else {
                this.focusUnit = null;
            }
        } else {
            this.focusUnit = null;
        }
    }

    FocusPushingStuff() {
        if(!this.focusPushingStuff || !this.focusPushingStuff.isRunning) {
            this.focusPushingStuff = null;
            this.isPushing = false;
            if(GameStuffManager.instance.mainStripLog) {
                this.focusPushingStuff = GameStuffManager.instance.mainStripLog;
                // this.focusPushingStuff.isNpcPushing = true;
                if(this.focusPushingStuff.npcPushingMovePosRef < 2) {
                    this.focusPushingStuff.SetNextNpcPushingMovePos();
                }
            }
        }
        if(this.focusPushingStuff) {
            // if(this.focusPushingStuff.isInPlace) {
            //     this.focusPushingStuff = null;
            // }
        }
    }

    FocusDriftingMeat() {
        let nearDriftingMeat = GameStuffManager.instance.FindCanNpcPickUpDriftingMeat(this.rootPosition);
        if(nearDriftingMeat) {
            // console.log(`找到水中的肉！${nearDriftingMeat.rootPosition}`);
            this.focusMoveTarget = nearDriftingMeat;
            this.focusMoveTargetPos = null;
        } else {
            this.focusMoveTarget = null;
        }
    }

    OnGetFood() {
        GameManager.instance.LateTimeCallOnce(()=>{
            (this.unitBehavior as NpcDiner).StartEat();
        }, 0.3);
        GameUtils.rootGameWorldUI.CloseNpcDemandBubble(this.unit_id);
    }

    // override ArrivePathPoint() {
    //     if(this.npcRef == 1 || this.npcRef == 6) {
    //         if(this.pathPointRef <= 6) {
    //             let queueHeadPathPointIndex = this._queueHeadPathPointIndex; // 队首路径点坐标
    //             let queueStartPathPointIndex = this._queueStartPathPointIndex; // 开始排队的路径点坐标
    //             if(this.pathPointRef == queueStartPathPointIndex) { // 排队开始
    //                 // console.log(`开始排队！`);
    //                 this.QueueUp();
    //             } else if(this.pathPointRef == queueHeadPathPointIndex) { // 排队中途到达
    //                 if(!this._isQueueArrived) {
    //                     this._isQueueArrived = true;
    //                     // 判断是否需要转到下一个排队点
    //                     if(this._queueCurMiddleIndex < this._queueTargetMiddleIndex) { // 目标为中途点
    //                         // console.log(`到达站点！ ${this._queueIndex}`);
    //                         this.GoToQueuePathPoint(queueHeadPathPointIndex, this._queueIndex);
    //                         this._isQueueArrived = false;
    //                     } else if(this._queueCurMiddleIndex == this._queueTargetMiddleIndex) { // 目标为目标点
    //                         this.GoToQueuePathPoint(queueHeadPathPointIndex, this._queueIndex);
    //                         // if(this._queueIndex == 0 || this._queueIndex == 1 || this._queueIndex == 2) {
    //                         if(this._queueIndex == 0) {
    //                             // console.log(`到达队首点！ ${this._queueIndex}`);
    //                             this.isSoldierGetWeapon = true;
    //                             GameManager.instance.LateTimeCallOnce(()=>{
    //                                 // 测试
    //                                 // this.OnBuyComplite();
    //                                 // this.OnBuyerGetWeapon();
    //                             }, 1);
    //                         } else {
    //                             // console.log(`士兵到达点..！ ${this._queueIndex}`);
    //                         }
    //                     } else if(this._queueCurMiddleIndex > this._queueTargetMiddleIndex) {
    //                         console.error(`超出站点！ ${this._queueIndex}`);
    //                     }
    //                 } else {
    //                 }
    //             } else {
    //                 this.GoToNextPathPoint();
    //             }
    //         } else {
    //             console.log(`士兵到达终点！`);
    //         }
    //     } else {
    //         super.ArrivePathPoint();
    //     }
    // }

    override GoToNextPathPoint(isOffset = false, offsetNum = 0) {
        // console.log(`go to next path point! _pathPointRef: ${this._pathPointRef}`);
        if(this.npcRef == 1 || this.npcRef == 6) {
            this.pathPointRef += 1;
            if(this.pathPointRef <= 6) {
                this.movingPathPointPos = this.GetPathPointPosition(this.pathPointRef);
                this.isMoveToPathPoint = true;
            } else {
                this.isSoldierFight = true;
                // console.log(`士兵开始战斗！`);
            }
            if(this.pathPointRef == 5) {
                GameDirector.instance.OnNPCComing();
            } else if(this.pathPointRef == 6) {
                GameDirector.instance.OnNPCLeave();
            }
        // } else if(this.npcRef == 4) { // 买家
        //     this.pathPointRef += 1;
        //     if(this.pathPointRef <= 9) {
        //         this.movingPathPointPos = this.GetPathPointPosition(this.pathPointRef);
        //         this.isMoveToPathPoint = true;
        //         if(this.pathPointRef == 9) {
        //             GameStuffManager.instance.RemoveBuyer(this.unit_id);
        //         }
        //     } else {
        //         this.DestroySelf();
        //         // console.log(`买家到达终点！`);
        //     }
        } else {
            super.GoToNextPathPoint(isOffset, offsetNum);
        }
    }

    override GetPathPointPosition(pathPointIndex: number) {
        if(this.npcRef == 1 || this.npcRef == 6) {
            let pathPoints = GameUtils.rootPathPoints.npcPathPoints;
            let lastPathPoint = GameUtils.rootPathPoints.GetNPCStandPathPoint();
            let pos = cc.v2();
            if(pathPointIndex >= 6) {
                pos = cc.v2(lastPathPoint.getPosition());
            } else {
                pos = cc.v2(pathPoints[pathPointIndex].getPosition());
            }
            if(pos.equals(cc.Vec2.ZERO)) {
                console.log(`未找到路径点！${pathPointIndex}`);
            }
            return pos;
        } else {
            return super.GetPathPointPosition(pathPointIndex);
        }
    }

    // PayCoin(num: number) {
    //     let coinStorageArea = GameUtils.rootGameWorld.coinStorageArea;
    //     // GameStuffManager.instance.CreateDropResource(this.centerPosition, cc.v2(), ResourceType.coin, GameUtils.rootGameWorld.coinStorageArea, 0);
    //     for(let i = 0; i < num; i++) {
    //         let lastPos = this.centerPosition;
    //         GameManager.instance.LateTimeCallOnce(()=>{
    //             let pos = this.isAlive ? this.centerPosition : lastPos;
    //             GameStuffManager.instance.CreateDropResource(pos, cc.v2(), ResourceType.coin, coinStorageArea, 0);
    //             lastPos = pos;
    //         }, i * 0.06);
    //     }
    // }

    NpcDinerMove() {
        // 买家移动
        // console.log(`买家移动！`);
        this.MoveToTrack(1, false, ()=>{
            this.ReadyToQueueUp(1, 0, 1);
            this.SetOnArriveQueueHeadCallback(()=>{
                if(!(this.unitBehavior as NpcDiner).isSeatOver) {
                    (this.unitBehavior as NpcDiner).isFindingSeat = true;
                }
                // GameManager.instance.LateTimeCallOnce(()=>{
                //     this.QueueOut();
                //     this.MoveToTrack(11);
                // }, 3);
            });
            this.SetOnArriveEndCallback(()=>{});
        });
        // this.ArrivePathPoint();
        // this.GoToNextPathPoint();
    }

    override DestroySelf(): void {
        if(this.npcRef == 4) {
            GameStuffManager.instance.RemoveBuyer(this.unit_id);
        }
        if(this.unitBehavior) {
            this.unitBehavior.DestroySelf();
            this.unitBehavior = null;
        }
        
        super.DestroySelf();
    }

}