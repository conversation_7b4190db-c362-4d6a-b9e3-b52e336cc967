// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import LocalUtils from "../../LocalUtils";
import Stuff from "../Stuff";

const {ccclass, property, executeInEditMode} = cc._decorator;

@ccclass
export default class MiddleNodeReleaser extends cc.Component {
    @property({type: cc.Node, tooltip: '根节点：此节点必须为 MiddleNode 节点的子节点，由此可以将目标节点释放在 MiddleNode 下'})
    rootNode: cc.Node = null;

    @property({tooltip: '自动模式：自动寻找子节点中的所有 Stuff 节点（如果是 Stuff 节点，不会再检测该 Stuff 的子节点）'})
    isAuto: boolean = true;

    @property({type: [cc.Node], visible: function() {
        return !this.isAuto;
    }})
    targetNodes: cc.Node[] = [];

    releasedNodes: cc.Node[] = [];

    protected onLoad(): void {
        let count = 0;
        let nodes: cc.Node[] = [];
        if(this.isAuto) {
            nodes = this.FindStuffNodes(this.node, true);
        } else {
            nodes = this.targetNodes;
        }
        nodes.forEach((e)=>{
            if(this.ReleaseNode(e, this.rootNode.parent)) {
                this.releasedNodes.push(e);
                count ++;
            }
        });
        // console.log(`MiddleNodeReleaser: ${count} 个节点已释放！`);
    }

    FindStuffNodes(node: cc.Node, isFirst = false): cc.Node[] {
        let nodes: cc.Node[] = [];
        if(!node) return [];
        if(!isFirst && node.getComponent(MiddleNodeReleaser)) return [];
        if(node.getComponent(Stuff)) {
            nodes = nodes.concat([node]);
        } else {
            this.node.children.forEach((e)=>{
                nodes = nodes.concat(this.FindStuffNodes(e));
            });
        }
        return nodes;
    }
    
    ReleaseNode(node: cc.Node, parent: cc.Node): boolean {
        if(node && parent && (node instanceof cc.Node) && (parent instanceof cc.Node)) {
            LocalUtils.ChangeParentWithoutMoving(node, parent);
            return true;
        }
        return false;
    }
}