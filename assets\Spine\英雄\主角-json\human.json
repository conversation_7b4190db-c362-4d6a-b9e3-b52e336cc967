{"skeleton": {"hash": "8jC8rp/0xCt39xzyPdldtsQ0lKg", "spine": "3.8.99", "x": -141.94, "y": -13.7, "width": 348.77, "height": 293.35, "images": "./images/", "audio": ""}, "bones": [{"name": "root"}, {"name": "archer", "parent": "root", "x": -7.06, "y": -17.05, "scaleX": 0.1776, "scaleY": 0.1776}, {"name": "archer2", "parent": "archer", "x": 67.24, "y": 516.27}, {"name": "archer5", "parent": "archer2", "length": 197.2, "rotation": -118.69, "x": -116.84, "y": -27.2}, {"name": "archer6", "parent": "archer5", "length": 157.15, "rotation": 6.77, "x": 202.06, "y": 0.18}, {"name": "archer7", "parent": "archer", "length": 72.6, "rotation": -109.25, "x": -208.12, "y": 157.88, "color": "ff3f00ff"}, {"name": "archer8", "parent": "archer2", "length": 204.24, "rotation": -75.82, "x": 46.52, "y": -37.13}, {"name": "archer9", "parent": "archer8", "length": 110.58, "rotation": -5.69, "x": 204.24}, {"name": "archer10", "parent": "archer", "length": 65.07, "rotation": -66.29, "x": 177.4, "y": 164.36, "color": "ff3f00ff"}, {"name": "archer11", "parent": "archer2", "length": 93.09, "rotation": 91, "x": -6.82, "y": 73.48}, {"name": "archer12", "parent": "archer11", "length": 104.23, "rotation": 12.89, "x": 100.88, "y": -6.23}, {"name": "archer13", "parent": "archer12", "length": 70.8, "rotation": -11.25, "x": 154.59, "y": -37.38}, {"name": "archer16", "parent": "archer12", "length": 188.32, "rotation": 126.08, "x": 125.28, "y": 181.72}, {"name": "archer17", "parent": "archer16", "length": 168.35, "rotation": 5.04, "x": 188.96, "y": 1.48}, {"name": "archer18", "parent": "archer17", "length": 77.65, "rotation": 13.19, "x": 171.32, "y": 1.09}, {"name": "archer19", "parent": "archer12", "length": 159.08, "rotation": -160.69, "x": -3.96, "y": -107.78}, {"name": "archer20", "parent": "archer19", "length": 95.96, "rotation": -2.85, "x": 160.27, "y": -0.55}, {"name": "archer21", "parent": "archer20", "length": 84.95, "rotation": -3.7, "x": 107.92, "y": -2.13}, {"name": "archer22", "parent": "archer21", "rotation": 17.8, "x": 120.75, "y": 47.3}, {"name": "archer29", "parent": "archer13", "x": 196.63, "y": 83.03}, {"name": "archer30", "parent": "archer13", "x": 200.19, "y": -171.87}, {"name": "archer31", "parent": "archer13", "x": 113.82, "y": -69.16}, {"name": "archer32", "parent": "archer13", "x": 72.84, "y": 32.37}, {"name": "archer33", "parent": "archer13", "x": 615.91, "y": 70.98}, {"name": "archer44", "parent": "archer33", "length": 136.56, "rotation": -91.08, "x": -240.11, "y": 37.81}, {"name": "archer96", "parent": "archer44", "length": 168.14, "rotation": 121.54, "x": 1.88, "y": 0.57}, {"name": "archer34", "parent": "archer96", "length": 210.7, "rotation": 125.23, "x": 74.62, "y": 13.7}, {"name": "archer95", "parent": "archer44", "length": 183.9, "rotation": 44.94, "x": 134.68, "y": -0.57}, {"name": "archer<PERSON>", "parent": "archer95", "length": 215.72, "rotation": -130.09, "x": 80.32, "y": -11.56}, {"name": "archer103", "parent": "archer33", "length": 331.48, "rotation": -12.26, "x": -307.92, "y": -251.41}, {"name": "archer45", "parent": "archer103", "length": 53.63, "rotation": -137.43, "x": 57.84, "y": -67.95}, {"name": "archer<PERSON>", "parent": "archer45", "length": 199.34, "rotation": -15.98, "x": 52.91, "y": 0.93}, {"name": "archer51", "parent": "archer103", "length": 42.34, "rotation": -61.83, "x": 229.84, "y": -88.09}, {"name": "archer52", "parent": "archer51", "length": 185.94, "rotation": 2.95, "x": 46.57, "y": 0.71}, {"name": "archer55", "parent": "archer33", "length": 36.63, "rotation": 178.06, "x": -328.74, "y": 221.16}, {"name": "archer56", "parent": "archer55", "length": 190.99, "rotation": 20.13, "x": 41.07, "y": 0.65}, {"name": "archer<PERSON>", "parent": "archer33", "length": 76.05, "rotation": 176.81, "x": -249.79, "y": -292.32}, {"name": "archer61", "parent": "archer<PERSON>", "length": 246.85, "rotation": 13.67, "x": 78.08, "y": 2.7}, {"name": "archer64", "parent": "archer29", "x": 93.22, "y": -28.05}, {"name": "archer65", "parent": "archer30", "x": 79.24, "y": 3.45}, {"name": "archer66", "parent": "archer12", "x": 123.77, "y": 96.75}, {"name": "archer67", "parent": "archer66", "length": 125.46, "rotation": 117.55, "x": 56.67, "y": 173.82}, {"name": "archer68", "parent": "archer67", "length": 202, "rotation": -2.37, "x": 125.91, "y": 3.33}, {"name": "archer70", "parent": "archer68", "length": 257.69, "rotation": -8, "x": 210.87, "y": -8.29}, {"name": "archer72", "parent": "archer66", "length": 100.5, "rotation": 166.13, "x": -63.12, "y": -176.57}, {"name": "archer73", "parent": "archer72", "length": 158.54, "rotation": -2.16, "x": 96.54, "y": -3.12}, {"name": "archer75", "parent": "archer73", "length": 175.02, "rotation": -25.89, "x": 180.28, "y": -3.37}, {"name": "archer77", "parent": "archer66", "length": 80.62, "rotation": 150.69, "x": -14.03, "y": 4.81}, {"name": "archer78", "parent": "archer77", "length": 154.6, "rotation": -3.69, "x": 87.44, "y": -0.93}, {"name": "archer<PERSON>", "parent": "archer78", "length": 177.7, "rotation": -2.78, "x": 155.71, "y": -0.51}, {"name": "archer<PERSON>", "parent": "archer12", "length": 277.91, "rotation": 59.81, "x": 94.35, "y": -4.81}, {"name": "archer87", "parent": "archer<PERSON>", "length": 183.53, "rotation": -156.71, "x": -7.97, "y": 0.77}, {"name": "archer84", "parent": "archer10", "length": 236.51, "rotation": 77.88, "x": -26.39, "y": -130.74}, {"name": "archer85", "parent": "archer13", "x": 75.89, "y": -462.27}, {"name": "archer88", "parent": "archer13", "x": 75.49, "y": -492.89}, {"name": "archer89", "parent": "archer2", "x": 320.76, "y": 194.49}, {"name": "archer90", "parent": "archer2", "x": 357.35, "y": 197.25}, {"name": "archer94", "parent": "archer21", "x": 99.15, "y": 17.32}, {"name": "archer97", "parent": "archer33", "length": 34.35, "rotation": -48.92, "x": 37.36, "y": -197.26}, {"name": "archer98", "parent": "archer97", "length": 88.83, "rotation": 12.2, "x": 35.03, "y": 3.83}, {"name": "archer<PERSON>", "parent": "archer103", "length": 53.63, "rotation": -111.16, "x": 108.02, "y": -123.78}, {"name": "archer101", "parent": "archer<PERSON>", "length": 141.29, "rotation": -14.26, "x": 52.91, "y": 0.93}, {"name": "archer14", "parent": "archer7", "length": 236.68, "rotation": 103.96, "x": 23.31, "y": -109.59}, {"name": "archer<PERSON>", "parent": "archer16", "length": 95.82, "rotation": -129.26, "x": 110.22, "y": -85.24}, {"name": "ALL714", "parent": "archer", "x": 1709.52, "y": 965.53}, {"name": "ALL717", "parent": "ALL714", "x": 320.89, "y": -692.38}, {"name": "ALL723", "parent": "ALL714", "x": 856.08, "y": -304.63}, {"name": "ALL724", "parent": "ALL714", "x": -389.72, "y": -178.81}, {"name": "ALL726", "parent": "ALL714", "x": 208.28, "y": 157.05}, {"name": "ren", "parent": "archer2", "x": -27.48, "y": -420.25}, {"name": "wuti", "parent": "archer18", "x": 40.25, "y": -0.8}, {"name": "bone", "parent": "archer94", "length": 89.53, "rotation": 92.34, "x": -26.18, "y": 20.64, "scaleX": 6, "scaleY": 6}, {"name": "archer15", "parent": "archer2", "length": 204.24, "rotation": -75.82, "x": 139.13, "y": -37.13}, {"name": "archer24", "parent": "archer15", "length": 110.58, "rotation": -5.69, "x": 204.24}, {"name": "archer3", "parent": "archer", "length": 78.37, "rotation": -57.6, "x": 160.37, "y": 173.01, "color": "ff3f00ff"}], "slots": [{"name": "archer11", "bone": "archer13", "attachment": "archer11"}, {"name": "archer24", "bone": "archer22", "attachment": "archer24"}, {"name": "archer<PERSON>", "bone": "archer66", "attachment": "archer<PERSON>"}, {"name": "BarbarianAxe", "bone": "archer94"}, {"name": "archer22", "bone": "archer19", "attachment": "archer22"}, {"name": "archer21", "bone": "archer20", "attachment": "archer21"}, {"name": "archer20", "bone": "archer8", "attachment": "archer20"}, {"name": "archer25", "bone": "archer15"}, {"name": "archer19", "bone": "archer5", "attachment": "archer19"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "archer7"}, {"name": "archer18", "bone": "archer2", "attachment": "archer18"}, {"name": "archer17", "bone": "archer16", "attachment": "archer17"}, {"name": "archer16", "bone": "archer17", "attachment": "archer16"}, {"name": "archer15", "bone": "archer12", "attachment": "archer15"}, {"name": "archer13", "bone": "archer<PERSON>", "attachment": "archer13"}, {"name": "barb3", "bone": "root"}, {"name": "archer12", "bone": "archer13", "attachment": "archer12"}, {"name": "archer10", "bone": "archer13", "attachment": "archer10"}, {"name": "archer9", "bone": "archer32", "attachment": "archer9"}, {"name": "archer8", "bone": "archer30", "attachment": "archer8"}, {"name": "archer7", "bone": "archer65", "attachment": "archer7"}, {"name": "archer6", "bone": "archer31", "attachment": "archer6"}, {"name": "archer5", "bone": "archer29", "attachment": "archer5"}, {"name": "archer4", "bone": "archer64", "attachment": "archer4"}, {"name": "archer2", "bone": "archer33", "attachment": "archer2"}, {"name": "archer1", "bone": "archer44", "attachment": "archer1"}, {"name": "img_v2_3b71fc62-e05e-495d-9d1b-f4ce67b9a12g52", "bone": "ALL714"}], "ik": [{"name": "archer3", "order": 2, "bones": ["archer15", "archer24"], "target": "archer3", "bendPositive": false}, {"name": "archer7", "bones": ["archer5", "archer6"], "target": "archer7", "bendPositive": false}, {"name": "archer10", "order": 1, "bones": ["archer8", "archer9"], "target": "archer10", "bendPositive": false}], "transform": [{"name": "archer88", "order": 3, "bones": ["archer85"], "target": "archer88", "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}, {"name": "archer90", "order": 4, "bones": ["archer89"], "target": "archer90", "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}], "skins": [{"name": "default", "attachments": {"BarbarianAxe": {"BarbarianAxe": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [320.74, -296.27, -179.02, -296.27, -179.02, 756.97, 320.74, 756.97], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 80, "height": 169}, "barb": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [362.89, -195.89, -342.82, -195.89, -342.82, 702.12, 362.89, 702.12], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 80, "height": 102}}, "archer1": {"archer1": {"type": "mesh", "uvs": [0.87289, 0.01787, 0.89936, 0.03751, 0.91941, 0.07074, 0.93655, 0.09915, 0.95705, 0.13313, 0.97368, 0.16069, 0.98731, 0.20994, 1, 0.25577, 1, 0.30042, 1, 0.35168, 1, 0.40129, 1, 0.4542, 1, 0.49605, 0.9893, 0.55502, 0.97992, 0.60677, 0.9713, 0.65432, 0.96385, 0.69539, 0.95625, 0.73726, 0.94757, 0.78515, 0.93874, 0.83379, 0.92597, 0.87512, 0.90938, 0.9288, 0.89887, 0.96282, 0.88738, 1, 0.86481, 1, 0.8486, 0.95743, 0.83246, 0.91503, 0.81498, 0.86908, 0.7995, 0.83375, 0.78548, 0.80174, 0.7745, 0.77667, 0.76268, 0.74722, 0.74551, 0.7044, 0.73183, 0.67027, 0.71774, 0.63516, 0.70078, 0.59285, 0.68153, 0.54485, 0.63803, 0.54058, 0.56449, 0.53632, 0.54863, 0.53691, 0.4957, 0.53889, 0.40713, 0.54658, 0.33992, 0.56024, 0.31461, 0.58072, 0.27666, 0.62254, 0.23712, 0.66949, 0.20233, 0.72155, 0.16913, 0.77362, 0.13038, 0.84019, 0.09638, 0.88884, 0.06397, 0.94688, 0.02838, 0.94347, 0.01621, 0.88124, 0.00877, 0.84322, 0, 0.79838, 0, 0.7634, 0, 0.72843, 0, 0.69499, 0, 0.65698, 0, 0.63164, 0.0043, 0.60115, 0.01074, 0.55552, 0.01475, 0.52711, 0.01938, 0.49432, 0.02286, 0.46407, 0.02594, 0.43737, 0.02971, 0.40462, 0.03979, 0.37191, 0.04999, 0.33884, 0.06096, 0.30326, 0.07235, 0.26631, 0.08431, 0.22754, 0.09401, 0.19607, 0.10625, 0.17035, 0.12395, 0.13317, 0.13873, 0.10211, 0.15832, 0.06095, 0.17288, 0.03036, 0.25387, 0.04859, 0.31585, 0.06987, 0.38487, 0.09951, 0.46305, 0.12611, 0.52643, 0.13522, 0.60686, 0.11309, 0.68781, 0.07724, 0.75908, 0.03541, 0.82411, 0, 0.8488, 0, 0.36805, 0.5121, 0.40475, 0.46296, 0.45435, 0.41674, 0.49105, 0.3764, 0.52298, 0.3456, 0.54677, 0.39107, 0.55765, 0.44607, 0.55493, 0.49375, 0.58008, 0.47614, 0.59027, 0.43874, 0.60454, 0.3874, 0.62628, 0.43873, 0.65279, 0.48934], "triangles": [63, 43, 44, 67, 42, 65, 65, 66, 67, 67, 68, 88, 68, 69, 88, 46, 60, 45, 46, 57, 58, 60, 46, 59, 62, 44, 45, 59, 46, 58, 45, 60, 61, 62, 45, 61, 63, 44, 62, 64, 43, 63, 42, 64, 65, 53, 54, 48, 47, 48, 55, 48, 54, 55, 57, 46, 47, 47, 55, 56, 47, 56, 57, 49, 50, 52, 50, 51, 52, 48, 49, 53, 49, 52, 53, 64, 42, 43, 42, 88, 41, 42, 67, 88, 88, 89, 41, 41, 89, 40, 37, 100, 36, 36, 100, 8, 38, 96, 37, 37, 96, 100, 89, 90, 40, 40, 90, 95, 39, 40, 95, 91, 95, 90, 39, 95, 38, 91, 94, 95, 100, 96, 99, 96, 97, 99, 38, 95, 96, 88, 69, 89, 95, 94, 96, 94, 91, 93, 96, 94, 97, 94, 93, 97, 97, 98, 99, 97, 93, 98, 91, 92, 93, 93, 92, 98, 35, 36, 13, 12, 36, 11, 11, 36, 10, 10, 36, 9, 14, 33, 34, 34, 35, 14, 17, 31, 16, 16, 31, 32, 16, 32, 15, 15, 32, 33, 15, 33, 14, 14, 35, 13, 13, 36, 12, 19, 28, 18, 28, 29, 18, 29, 30, 18, 18, 30, 17, 30, 31, 17, 23, 24, 22, 24, 25, 22, 22, 25, 21, 25, 26, 21, 21, 26, 20, 26, 27, 20, 20, 27, 19, 27, 28, 19, 89, 69, 70, 89, 70, 71, 73, 89, 71, 73, 71, 72, 89, 73, 74, 89, 79, 80, 79, 74, 78, 74, 79, 89, 78, 74, 75, 75, 76, 78, 76, 77, 78, 89, 80, 90, 91, 80, 81, 91, 90, 80, 91, 81, 92, 92, 81, 82, 6, 100, 5, 4, 5, 99, 4, 99, 3, 84, 3, 98, 3, 85, 2, 1, 2, 0, 85, 3, 84, 100, 99, 5, 2, 85, 0, 85, 86, 0, 86, 87, 0, 3, 99, 98, 84, 98, 83, 9, 36, 8, 98, 92, 83, 83, 92, 82, 8, 100, 6, 6, 7, 8], "vertices": [3, 27, 200.9, 67.52, 0.97324, 25, 41.8, -292.37, 0.00676, 54, 485.1, 368.96, 0.02, 3, 27, 203.02, 54.13, 0.97556, 25, 29.27, -297.53, 0.00444, 54, 476.91, 358.16, 0.02, 3, 27, 199.42, 39.06, 0.97757, 25, 13.77, -297.52, 0.00243, 54, 463.54, 350.31, 0.02, 3, 27, 196.33, 26.16, 0.97898, 25, 0.51, -297.51, 0.00102, 54, 452.11, 343.59, 0.02, 3, 27, 192.65, 10.74, 0.97987, 25, -15.35, -297.5, 0.00013, 54, 438.44, 335.56, 0.02, 2, 27, 189.66, -1.77, 0.98, 54, 427.35, 329.05, 0.02, 3, 27, 179.65, -19.2, 0.95827, 54, 407.85, 324.19, 0.02, 28, -58.13, 80.91, 0.02173, 3, 27, 170.34, -35.41, 0.8853, 54, 389.7, 319.67, 0.02, 28, -39.72, 84.23, 0.0947, 3, 27, 157.67, -47.43, 0.75462, 54, 372.26, 320.48, 0.02, 28, -22.38, 82.28, 0.22538, 3, 27, 143.13, -61.22, 0.5276, 54, 352.24, 321.4, 0.02, 28, -2.46, 80.04, 0.4524, 3, 27, 129.06, -74.57, 0.30129, 54, 332.87, 322.3, 0.02, 28, 16.82, 77.87, 0.67871, 3, 27, 114.05, -88.81, 0.12817, 54, 312.2, 323.26, 0.02, 28, 37.38, 75.56, 0.85183, 3, 27, 102.18, -100.07, 0.05383, 54, 295.85, 324.01, 0.02, 28, 53.64, 73.73, 0.92617, 3, 27, 82.34, -112.67, 0.0082, 54, 273.03, 329.58, 0.02, 28, 76.05, 66.66, 0.9718, 3, 27, 64.94, -123.72, 8e-05, 54, 253, 334.48, 0.02, 28, 95.71, 60.47, 0.97992, 2, 54, 234.6, 338.97, 0.02, 28, 113.78, 54.77, 0.98, 2, 54, 218.7, 342.85, 0.02, 28, 129.39, 49.85, 0.98, 2, 54, 202.49, 346.81, 0.02, 28, 145.29, 44.84, 0.98, 2, 54, 183.96, 351.34, 0.02, 28, 163.49, 39.1, 0.98, 2, 54, 165.13, 355.93, 0.02, 28, 181.98, 33.28, 0.98, 2, 54, 149.24, 362.06, 0.02, 28, 197.43, 26.12, 0.98, 2, 54, 128.59, 370.02, 0.02, 28, 217.51, 16.81, 0.98, 2, 54, 115.51, 375.07, 0.02, 28, 230.23, 10.92, 0.98, 2, 54, 101.21, 380.59, 0.02, 28, 244.13, 4.47, 0.98, 2, 54, 101.65, 390.1, 0.02, 28, 243.07, -4.99, 0.98, 2, 54, 118.6, 396.16, 0.02, 28, 225.76, -9.93, 0.98, 2, 54, 135.47, 402.2, 0.02, 28, 208.53, -14.84, 0.98, 2, 54, 153.76, 408.74, 0.02, 28, 189.85, -20.17, 0.98, 3, 54, 167.86, 414.63, 0.02, 28, 175.39, -25.11, 0.97899, 24, 189.51, -128.91, 0.00101, 3, 54, 180.63, 419.96, 0.02, 28, 162.29, -29.6, 0.9726, 24, 183.93, -116.23, 0.0074, 3, 54, 190.64, 424.14, 0.02, 28, 152.04, -33.11, 0.95897, 24, 179.57, -106.31, 0.02103, 3, 54, 202.37, 428.59, 0.02, 28, 140.03, -36.77, 0.92695, 24, 174.9, -94.66, 0.05305, 3, 54, 219.44, 435.05, 0.02, 28, 122.58, -42.1, 0.8381, 24, 168.11, -77.72, 0.1419, 3, 54, 233.03, 440.21, 0.02, 28, 108.68, -46.35, 0.72793, 24, 162.7, -64.23, 0.25207, 3, 54, 247.02, 445.51, 0.02, 28, 94.37, -50.72, 0.58425, 24, 157.14, -50.34, 0.39575, 3, 54, 263.87, 451.9, 0.02, 28, 77.13, -55.98, 0.39619, 24, 150.44, -33.61, 0.58381, 4, 27, -4.16, -15.7, 0.01185, 54, 283, 459.14, 0.02, 28, 57.57, -61.96, 0.13355, 24, 142.83, -14.62, 0.8346, 4, 27, -15.58, -1.24, 0.58154, 54, 285.51, 477.4, 0.02, 28, 53.87, -80.01, 0.00186, 24, 124.53, -12.46, 0.3966, 3, 27, -35.74, 22.42, 0.00365, 54, 288.61, 508.33, 0.02, 24, 93.55, -9.94, 0.97635, 2, 54, 288.69, 515.02, 0.02, 24, 86.85, -9.99, 0.98, 2, 54, 288.95, 537.37, 0.02, 24, 64.5, -10.15, 0.98, 2, 54, 287.67, 574.85, 0.02, 24, 27.06, -12.14, 0.98, 3, 54, 283.65, 603.43, 0.02, 24, -1.44, -16.7, 0.90869, 26, 49.03, 72.61, 0.07131, 3, 54, 276.14, 614.46, 0.02, 24, -12.34, -24.41, 0.752, 26, 60.42, 65.65, 0.228, 3, 54, 260.54, 631.22, 0.02, 24, -28.79, -40.32, 0.469, 26, 81.53, 56.8, 0.511, 3, 54, 242.98, 648.73, 0.02, 24, -45.97, -58.22, 0.21203, 26, 104.75, 48.07, 0.76797, 3, 54, 223.32, 664.34, 0.02, 24, -61.21, -78.16, 0.0613, 26, 129.08, 41.94, 0.9187, 3, 54, 203.63, 679.28, 0.02, 24, -75.77, -98.13, 0.00939, 26, 153.18, 36.43, 0.97061, 2, 54, 178.38, 696.81, 0.02, 26, 183.4, 30.85, 0.98, 2, 54, 160.04, 712.02, 0.02, 26, 206.38, 24.53, 0.98, 2, 54, 138.01, 726.74, 0.02, 26, 232.52, 20.2, 0.98, 2, 54, 140.03, 741.67, 0.02, 26, 236.82, 5.75, 0.98, 2, 54, 164.58, 745.68, 0.02, 26, 216.11, -8.01, 0.98, 2, 54, 179.57, 748.13, 0.02, 26, 203.45, -16.41, 0.98, 2, 54, 197.25, 751.02, 0.02, 26, 188.52, -26.32, 0.98, 2, 54, 210.92, 750.39, 0.02, 26, 175.81, -31.37, 0.98, 2, 54, 224.58, 749.76, 0.02, 26, 163.11, -36.42, 0.98, 2, 54, 237.64, 749.15, 0.02, 26, 150.95, -41.25, 0.98, 2, 54, 252.48, 748.47, 0.02, 26, 137.14, -46.73, 0.98, 2, 54, 262.38, 748.01, 0.02, 26, 127.94, -50.39, 0.98, 2, 54, 274.2, 745.64, 0.02, 26, 116.18, -53.1, 0.98, 2, 54, 291.9, 742.1, 0.02, 26, 98.6, -57.17, 0.98, 3, 25, 72.82, 119.73, 0.00018, 54, 302.92, 739.9, 0.02, 26, 87.65, -59.69, 0.97982, 3, 25, 82.49, 111.09, 0.00356, 54, 315.64, 737.36, 0.02, 26, 75.01, -62.61, 0.97644, 3, 25, 91.6, 103.4, 0.01498, 54, 327.39, 735.34, 0.02, 26, 63.48, -65.61, 0.96502, 3, 25, 99.63, 96.61, 0.03753, 54, 337.75, 733.57, 0.02, 26, 53.3, -68.26, 0.94247, 3, 25, 109.49, 88.28, 0.08361, 54, 350.47, 731.39, 0.02, 26, 40.81, -71.51, 0.89639, 3, 25, 117.88, 77.73, 0.16262, 54, 363.05, 726.54, 0.02, 26, 27.35, -72.28, 0.81738, 3, 25, 126.36, 67.07, 0.29202, 54, 375.77, 721.65, 0.02, 26, 13.75, -73.05, 0.68798, 3, 25, 135.48, 55.59, 0.47718, 54, 389.45, 716.38, 0.02, 26, -0.89, -73.88, 0.50282, 3, 25, 144.96, 43.67, 0.68359, 54, 403.66, 710.91, 0.02, 26, -16.09, -74.75, 0.29641, 3, 25, 154.9, 31.16, 0.85639, 54, 418.57, 705.17, 0.02, 26, -32.04, -75.66, 0.12361, 3, 25, 162.97, 21.01, 0.93815, 54, 430.68, 700.51, 0.02, 26, -44.99, -76.4, 0.04185, 3, 25, 168.58, 11.19, 0.97099, 54, 440.48, 694.89, 0.02, 26, -56.24, -75.31, 0.00901, 3, 27, -49.33, 265.78, 5e-05, 25, 176.67, -3, 0.97995, 54, 454.66, 686.76, 0.02, 3, 27, -36.23, 269.62, 0.00061, 25, 183.44, -14.86, 0.97939, 54, 466.5, 679.97, 0.02, 3, 27, -18.86, 274.7, 0.00192, 25, 192.4, -30.58, 0.97808, 54, 482.2, 670.97, 0.02, 3, 27, -5.95, 278.47, 0.00261, 25, 199.07, -42.26, 0.97739, 54, 493.86, 664.27, 0.02, 4, 27, 12.4, 248.77, 0.01487, 25, 174.43, -66.99, 0.96212, 54, 485.16, 630.46, 0.02, 24, -32.27, 184.27, 0.00301, 4, 27, 24.36, 224.07, 0.04255, 25, 153.17, -84.36, 0.92216, 54, 475.64, 604.72, 0.02, 24, -6.35, 175.24, 0.01529, 4, 27, 36, 194.96, 0.1068, 25, 127.55, -102.43, 0.82353, 54, 462.72, 576.16, 0.02, 24, 22.45, 162.86, 0.04967, 4, 27, 51.16, 163.87, 0.24649, 25, 100.82, -124.38, 0.62504, 54, 450.81, 543.68, 0.02, 24, 55.14, 151.56, 0.10847, 4, 27, 66.99, 142.01, 0.42766, 25, 83.23, -144.84, 0.42048, 54, 446.01, 517.13, 0.02, 24, 81.78, 147.27, 0.13186, 4, 27, 96.62, 123.34, 0.68198, 25, 71.94, -177.99, 0.20914, 54, 453.09, 482.82, 0.02, 24, 115.94, 154.99, 0.08888, 4, 27, 130.31, 108.21, 0.86326, 25, 65.02, -214.27, 0.08506, 54, 465.52, 448.05, 0.02, 24, 150.48, 168.07, 0.03168, 4, 27, 162.87, 97.65, 0.94329, 25, 62.29, -248.39, 0.03029, 54, 480.46, 417.26, 0.02, 24, 180.99, 183.59, 0.00643, 4, 27, 191.8, 87.26, 0.96836, 25, 58.9, -278.94, 0.01121, 54, 493.03, 389.2, 0.02, 24, 208.8, 196.69, 0.00042, 4, 27, 198.97, 79.71, 0.97093, 25, 53.21, -287.67, 0.00898, 54, 492.55, 378.79, 0.02, 24, 219.21, 196.4, 9e-05, 4, 27, -85.92, 89.08, 5e-05, 25, -3.7, -8.36, 0.04146, 54, 301.9, 590.7, 0.02, 24, 10.94, 1.79, 0.93849, 4, 27, -61.32, 91.07, 0.00735, 25, 3.94, -31.83, 0.4134, 54, 320.38, 574.34, 0.02, 24, 26.95, 20.58, 0.55925, 4, 27, -33.8, 88.32, 0.04883, 25, 7.64, -59.23, 0.34414, 54, 337.46, 552.6, 0.02, 24, 48.37, 38.07, 0.58703, 4, 27, -11.7, 87.94, 0.1355, 25, 12.39, -80.82, 0.31967, 54, 352.5, 536.4, 0.02, 24, 64.28, 53.41, 0.52483, 4, 27, 6.31, 86.45, 0.24825, 25, 15.12, -98.69, 0.28711, 54, 363.91, 522.38, 0.02, 24, 78.08, 65.08, 0.44464, 4, 27, 0.32, 66.93, 0.21686, 25, -5.25, -97.39, 0.15802, 54, 345.69, 513.17, 0.02, 24, 87.63, 47.04, 0.60512, 4, 27, -12.12, 48.8, 0.09218, 25, -25.78, -89.48, 0.05735, 54, 323.99, 509.58, 0.02, 24, 91.63, 25.41, 0.83046, 4, 27, -26.43, 36.8, 0.00586, 25, -40.77, -78.34, 0.00764, 54, 305.42, 511.59, 0.02, 24, 89.97, 6.81, 0.96651, 4, 27, -14.14, 33.84, 0.03461, 25, -40.8, -90.99, 0.0153, 54, 311.81, 500.67, 0.02, 24, 100.77, 13.4, 0.93009, 4, 27, -0.57, 40.79, 0.19994, 25, -30.89, -102.58, 0.04343, 54, 326.22, 495.7, 0.02, 24, 105.46, 27.9, 0.73662, 4, 27, 18.14, 50.24, 0.45115, 25, -17.37, -118.59, 0.08544, 54, 346, 488.76, 0.02, 24, 112.03, 47.8, 0.4434, 4, 27, 9.89, 29.76, 0.42764, 25, -39.2, -115.31, 0.02564, 54, 325.52, 480.51, 0.02, 24, 120.66, 27.49, 0.52672, 4, 27, 3.24, 8.03, 0.42592, 25, -61.88, -113.87, 0.00137, 54, 305.24, 470.26, 0.02, 24, 131.3, 7.4, 0.55271], "hull": 88, "edges": [0, 174, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174], "width": 84, "height": 78}}, "archer2": {"archer2": {"type": "mesh", "uvs": [0.25948, 0.04431, 0.25644, 0.08545, 0.25509, 0.12361, 0.25441, 0.15286, 0.25408, 0.17367, 0.25678, 0.20738, 0.274, 0.16327, 0.32067, 0.14964, 0.38065, 0.14659, 0.4701, 0.14557, 0.52408, 0.15173, 0.54685, 0.1512, 0.56677, 0.14181, 0.58918, 0.12614, 0.61585, 0.10056, 0.63506, 0.07968, 0.65641, 0.05305, 0.67312, 0.03426, 0.6902, 0.05619, 0.69909, 0.08908, 0.70336, 0.12406, 0.70478, 0.16008, 0.70194, 0.1914, 0.699, 0.20737, 0.6957, 0.22533, 0.71021, 0.22724, 0.72415, 0.22907, 0.75005, 0.23175, 0.78542, 0.23336, 0.81861, 0.23389, 0.84961, 0.23443, 0.87733, 0.22961, 0.90578, 0.22211, 0.94189, 0.21034, 0.9707, 0.20177, 1, 0.20177, 1, 0.24085, 0.98675, 0.2826, 0.97471, 0.31472, 0.9583, 0.34684, 0.94116, 0.37467, 0.92511, 0.39769, 0.90724, 0.42285, 0.88937, 0.44426, 0.86383, 0.46202, 0.84284, 0.47201, 0.85419, 0.48034, 0.86445, 0.49526, 0.87599, 0.51204, 0.88596, 0.52655, 0.89589, 0.5461, 0.90563, 0.56527, 0.91539, 0.58449, 0.92255, 0.59858, 0.92871, 0.61406, 0.93844, 0.63854, 0.94374, 0.66258, 0.95161, 0.69828, 0.93461, 0.72451, 0.90003, 0.71562, 0.87611, 0.70759, 0.86555, 0.703, 0.84787, 0.69532, 0.84959, 0.71172, 0.85277, 0.74185, 0.85093, 0.76779, 0.84926, 0.79135, 0.84758, 0.81502, 0.84367, 0.84611, 0.84009, 0.87465, 0.83487, 0.89586, 0.82885, 0.92033, 0.81068, 0.93269, 0.80208, 0.91057, 0.79166, 0.88378, 0.78086, 0.85602, 0.7741, 0.83862, 0.76614, 0.8178, 0.75823, 0.79711, 0.75127, 0.77891, 0.74205, 0.76418, 0.72966, 0.74437, 0.72171, 0.73166, 0.71171, 0.71748, 0.69938, 0.69998, 0.68659, 0.68183, 0.66874, 0.6552, 0.64826, 0.63709, 0.59596, 0.5974, 0.53771, 0.57079, 0.47412, 0.56338, 0.41161, 0.57179, 0.36992, 0.5926, 0.34876, 0.62211, 0.33098, 0.65099, 0.32315, 0.67926, 0.32214, 0.69663, 0.32129, 0.71112, 0.32019, 0.72989, 0.3212, 0.74806, 0.32223, 0.76639, 0.32305, 0.7812, 0.3239, 0.79646, 0.32655, 0.81675, 0.32905, 0.83586, 0.33146, 0.85431, 0.33406, 0.87418, 0.33752, 0.89024, 0.34023, 0.90286, 0.3432, 0.91661, 0.34595, 0.92937, 0.35108, 0.94721, 0.35568, 0.9632, 0.36156, 0.98366, 0.36626, 1, 0.34378, 1, 0.32851, 0.99068, 0.31366, 0.98162, 0.30186, 0.97442, 0.29254, 0.96679, 0.27938, 0.95602, 0.26511, 0.93984, 0.25366, 0.92398, 0.24616, 0.90675, 0.24119, 0.88682, 0.23636, 0.86306, 0.23323, 0.84217, 0.2314, 0.81821, 0.23075, 0.79809, 0.2301, 0.77011, 0.22983, 0.75324, 0.22913, 0.74037, 0.1981, 0.74485, 0.16248, 0.74186, 0.12381, 0.72917, 0.08819, 0.70527, 0.11211, 0.67017, 0.13806, 0.63358, 0.15892, 0.59176, 0.18131, 0.54397, 0.14213, 0.5589, 0.0999, 0.55965, 0.05359, 0.54098, 0.02103, 0.49991, 0, 0.45585, 0, 0.40709, 0.02966, 0.41185, 0.06072, 0.3993, 0.08172, 0.38249, 0.10463, 0.3275, 0.12825, 0.27566, 0.15403, 0.22802, 0.17235, 0.21233, 0.15648, 0.18656, 0.15682, 0.15187, 0.16425, 0.12411, 0.18147, 0.08495, 0.21389, 0.03043, 0.24867, 0, 0.26522, 0, 0.23888, 0.2277, 0.21929, 0.23761, 0.19768, 0.22819, 0.23973, 0.71405, 0.26796, 0.69399, 0.30095, 0.68919, 0.43211, 0.19358, 0.47626, 0.30384, 0.49097, 0.45274, 0.29194, 0.51185, 0.21217, 0.42319, 0.18583, 0.32202], "triangles": [102, 126, 101, 101, 126, 100, 100, 126, 99, 98, 99, 127, 99, 126, 127, 98, 127, 128, 98, 128, 129, 122, 123, 106, 123, 124, 105, 105, 124, 104, 124, 125, 104, 125, 103, 104, 103, 126, 102, 125, 126, 103, 109, 118, 119, 109, 119, 108, 108, 119, 120, 108, 120, 107, 120, 121, 107, 107, 121, 106, 121, 122, 106, 106, 123, 105, 115, 113, 114, 113, 115, 112, 115, 116, 112, 112, 116, 111, 116, 117, 111, 111, 117, 110, 110, 117, 118, 110, 118, 109, 63, 79, 80, 63, 80, 62, 62, 80, 81, 81, 82, 62, 82, 83, 62, 62, 84, 45, 83, 84, 62, 76, 77, 67, 67, 77, 66, 66, 77, 78, 66, 78, 65, 65, 78, 79, 65, 79, 64, 63, 64, 79, 72, 73, 71, 71, 73, 70, 73, 74, 70, 70, 74, 69, 69, 74, 68, 68, 74, 75, 68, 75, 67, 67, 75, 76, 62, 50, 51, 62, 49, 50, 62, 48, 49, 62, 47, 48, 57, 58, 56, 56, 58, 59, 56, 59, 60, 56, 60, 55, 55, 60, 61, 55, 61, 54, 54, 61, 53, 53, 61, 52, 61, 62, 52, 62, 51, 52, 47, 45, 46, 47, 62, 45, 28, 29, 85, 45, 84, 29, 44, 29, 30, 44, 45, 29, 44, 30, 43, 43, 30, 42, 41, 42, 30, 41, 30, 31, 40, 31, 32, 41, 31, 40, 40, 32, 39, 39, 32, 38, 38, 32, 33, 38, 33, 37, 37, 33, 34, 37, 34, 36, 34, 35, 36, 27, 28, 86, 26, 27, 87, 85, 29, 84, 86, 28, 85, 87, 27, 86, 87, 88, 24, 23, 24, 13, 22, 23, 20, 22, 20, 21, 26, 87, 25, 87, 24, 25, 24, 88, 89, 24, 89, 12, 13, 24, 12, 23, 13, 14, 23, 14, 15, 20, 15, 16, 16, 17, 18, 20, 23, 15, 20, 16, 19, 18, 19, 16, 140, 141, 148, 147, 148, 141, 141, 142, 147, 139, 140, 170, 170, 140, 148, 170, 148, 149, 171, 149, 150, 149, 171, 170, 142, 143, 147, 147, 143, 146, 169, 170, 160, 143, 144, 146, 144, 145, 146, 98, 129, 164, 164, 130, 163, 164, 165, 98, 164, 129, 130, 130, 131, 163, 131, 132, 163, 163, 132, 138, 133, 137, 132, 98, 165, 97, 135, 136, 134, 137, 133, 134, 134, 136, 137, 132, 137, 138, 164, 163, 139, 97, 165, 96, 96, 165, 95, 164, 94, 165, 139, 163, 138, 164, 139, 169, 95, 165, 94, 169, 94, 164, 94, 169, 93, 93, 169, 92, 92, 169, 91, 139, 170, 169, 12, 168, 11, 160, 171, 161, 160, 5, 169, 7, 5, 6, 160, 170, 171, 150, 151, 171, 171, 151, 162, 171, 162, 161, 162, 151, 152, 161, 162, 160, 160, 162, 4, 5, 160, 4, 153, 154, 152, 154, 155, 152, 3, 4, 162, 155, 3, 162, 155, 162, 152, 3, 155, 156, 3, 156, 2, 2, 156, 1, 156, 157, 1, 1, 157, 0, 157, 158, 0, 0, 158, 159, 90, 91, 168, 90, 168, 89, 12, 89, 168, 10, 11, 167, 168, 169, 167, 169, 168, 91, 167, 5, 166, 166, 5, 7, 7, 8, 166, 11, 168, 167, 166, 9, 167, 167, 9, 10, 166, 8, 9, 5, 167, 169], "vertices": [3, 23, 183.81, 210.41, 0.65763, 34, -512.62, -6.59, 0.33237, 53, 723.42, 713.04, 0.01, 3, 23, 151.55, 215.43, 0.65482, 34, -480.21, -10.5, 0.33518, 53, 691.16, 718.05, 0.01, 3, 23, 121.54, 218.38, 0.65175, 34, -450.12, -12.44, 0.33825, 53, 661.16, 721, 0.01, 3, 23, 98.53, 220.22, 0.65052, 34, -427.06, -13.51, 0.33948, 53, 638.14, 722.85, 0.01, 3, 23, 82.14, 221.37, 0.65256, 34, -410.64, -14.1, 0.33744, 53, 621.76, 724, 0.01, 3, 23, 55.43, 219.47, 0.67949, 34, -384.01, -11.3, 0.31051, 53, 595.05, 722.1, 0.01, 3, 23, 89.28, 197.94, 0.75284, 34, -418.56, 9.07, 0.23716, 53, 628.89, 700.57, 0.01, 3, 23, 97.53, 143.46, 0.82083, 34, -428.65, 63.25, 0.16917, 53, 637.14, 646.09, 0.01, 4, 23, 96.73, 73.97, 0.91786, 34, -430.2, 132.73, 0.07074, 53, 636.35, 576.59, 0.01, 59, -149.63, 256.56, 0.0014, 5, 23, 92.76, -29.55, 0.84944, 34, -429.73, 236.31, 0.00021, 53, 632.37, 473.08, 0.01, 59, -90.92, 171.21, 0.07161, 58, -90.01, 151.97, 0.06875, 5, 23, 85.02, -91.76, 0.51882, 53, 624.64, 410.87, 0.01, 59, -59.93, 116.72, 0.23547, 58, -48.2, 105.25, 0.23542, 30, -423.18, -28.71, 0.00029, 5, 23, 84.22, -118.11, 0.32883, 53, 623.83, 384.51, 0.01, 59, -44.81, 95.11, 0.38382, 58, -28.86, 87.33, 0.27691, 30, -409.18, -6.36, 0.00044, 4, 23, 90.56, -141.5, 0.17325, 53, 630.18, 361.13, 0.01, 59, -25.75, 80.16, 0.59262, 58, -7.07, 76.74, 0.22413, 4, 23, 101.71, -167.99, 0.05611, 53, 641.33, 334.63, 0.01, 59, -0.97, 65.59, 0.86663, 58, 20.22, 67.74, 0.06726, 3, 23, 120.45, -199.78, 0.00496, 53, 660.06, 302.84, 0.01, 59, 33.06, 51.31, 0.98504, 2, 53, 675.5, 279.86, 0.01, 59, 59.17, 42.12, 0.99, 2, 53, 695.34, 254.21, 0.01, 59, 90.42, 33.42, 0.99, 2, 53, 709.26, 234.18, 0.01, 59, 113.55, 25.69, 0.99, 3, 53, 691.07, 215.23, 0.01, 59, 110.29, -0.38, 0.98939, 58, 142.92, 26.76, 0.00061, 4, 53, 664.67, 206.14, 0.01, 59, 94.57, -23.45, 0.9488, 58, 132.42, 0.89, 0.04057, 32, -24.14, 243.02, 0.00063, 4, 53, 636.88, 202.47, 0.01, 59, 74.48, -43.01, 0.75533, 58, 116.92, -22.47, 0.2257, 32, -28.24, 215.28, 0.00897, 5, 53, 608.41, 202.14, 0.01, 59, 51.86, -60.3, 0.42377, 58, 98.46, -44.15, 0.52639, 30, -303.84, 143.3, 0.00074, 32, -35.72, 187.81, 0.0391, 6, 53, 583.87, 206.57, 0.01, 59, 29.54, -71.42, 0.16241, 58, 78.99, -59.73, 0.72319, 30, -284.89, 127.09, 0.00488, 32, -46.7, 165.43, 0.09891, 33, -84.67, 169.3, 0.00061, 6, 53, 571.44, 210.54, 0.01, 59, 17.21, -75.66, 0.06884, 58, 67.83, -66.49, 0.7603, 30, -276.16, 117.39, 0.0093, 32, -53.93, 154.56, 0.14976, 33, -92.45, 158.82, 0.00181, 7, 23, 17.85, -287.61, 8e-05, 53, 557.47, 215.01, 0.01, 59, 3.33, -80.43, 0.01248, 58, 55.28, -74.08, 0.70543, 30, -266.35, 106.48, 0.0179, 32, -62.06, 142.35, 0.24861, 33, -101.2, 147.04, 0.00551, 6, 53, 555.19, 198.31, 0.01, 59, 11.49, -95.19, 0.00101, 58, 66.37, -86.78, 0.57481, 30, -255.95, 119.75, 0.01917, 32, -46.62, 135.58, 0.37674, 33, -86.12, 139.48, 0.01827, 5, 53, 553, 182.24, 0.01, 58, 77.04, -98.99, 0.47679, 30, -245.96, 132.52, 0.01632, 32, -31.77, 129.07, 0.46022, 33, -71.63, 132.22, 0.03667, 5, 53, 549.5, 152.39, 0.01, 58, 97.25, -121.24, 0.32264, 30, -227.87, 156.53, 0.00791, 32, -4.01, 117.52, 0.55415, 33, -44.5, 119.26, 0.1053, 5, 53, 546.35, 111.52, 0.01, 58, 125.98, -150.48, 0.15605, 30, -204.53, 190.22, 0.00046, 32, 34.42, 103.29, 0.50542, 33, -6.85, 103.07, 0.32807, 4, 53, 544.15, 73.15, 0.01, 58, 153.46, -177.35, 0.05802, 32, 70.72, 90.66, 0.26005, 33, 28.75, 88.59, 0.67193, 4, 53, 542.08, 37.3, 0.01, 58, 179.11, -202.47, 0.01447, 32, 104.62, 78.84, 0.05502, 33, 62, 75.04, 0.92051, 4, 53, 544.39, 5.07, 0.01, 58, 204.93, -221.91, 0.0019, 32, 136.26, 72.23, 0.0029, 33, 93.26, 66.81, 0.9852, 2, 53, 548.78, -28.11, 0.01, 33, 126.08, 60.24, 0.99, 2, 53, 556.14, -70.31, 0.01, 33, 168.39, 53.56, 0.99, 2, 53, 561.35, -103.95, 0.01, 33, 201.91, 47.62, 0.99, 2, 53, 559.78, -137.84, 0.01, 33, 233.47, 35.19, 0.99, 2, 53, 528.98, -136.42, 0.01, 33, 222.17, 6.5, 0.99, 2, 53, 496.79, -119.58, 0.01, 33, 195.82, -18.53, 0.99, 3, 53, 472.11, -104.48, 0.01, 33, 173.57, -37, 0.98984, 60, 87.99, 251.17, 0.00016, 3, 53, 447.68, -84.33, 0.01, 33, 146.6, -53.61, 0.98549, 60, 84.64, 219.67, 0.00451, 4, 53, 426.65, -63.49, 0.01, 32, 169.92, -59.79, 0.00019, 33, 120.08, -66.77, 0.96907, 60, 78.82, 190.64, 0.02074, 4, 53, 409.37, -44.09, 0.01, 32, 146.53, -71.1, 0.00796, 33, 96.13, -76.85, 0.92979, 60, 72.15, 165.53, 0.05225, 4, 53, 390.49, -22.5, 0.01, 32, 120.59, -83.33, 0.0437, 33, 69.6, -87.74, 0.83373, 60, 64.53, 137.89, 0.11257, 4, 53, 374.57, -1.05, 0.01, 32, 95.6, -92.76, 0.10419, 33, 44.16, -95.87, 0.69637, 60, 55.4, 112.78, 0.18944, 5, 53, 361.94, 29.14, 0.01, 32, 63.1, -96.64, 0.20116, 33, 11.51, -98.07, 0.45007, 60, 37.17, 85.61, 0.33454, 61, -36.13, 78.19, 0.00422, 5, 53, 355.18, 53.78, 0.01, 32, 37.55, -96.38, 0.21029, 33, -14, -96.5, 0.16734, 60, 20.32, 66.39, 0.56834, 61, -47.72, 55.42, 0.04402, 5, 53, 348.01, 40.96, 0.01, 32, 47.92, -106.79, 0.12613, 33, -4.18, -107.43, 0.08702, 60, 34.97, 67.47, 0.62218, 61, -33.79, 60.07, 0.15467, 5, 53, 335.7, 29.63, 0.01, 32, 55.44, -121.74, 0.067, 33, 2.56, -122.74, 0.04268, 60, 51.21, 63.44, 0.56801, 61, -17.06, 60.16, 0.31231, 5, 53, 321.86, 16.9, 0.01, 32, 63.89, -138.54, 0.02758, 33, 10.14, -139.95, 0.01741, 60, 69.46, 58.9, 0.39196, 61, 1.75, 60.26, 0.55306, 5, 53, 309.89, 5.89, 0.01, 32, 71.2, -153.06, 0.01123, 33, 16.69, -154.84, 0.0074, 60, 85.24, 54.98, 0.22082, 61, 18.01, 60.35, 0.75056, 5, 53, 293.95, -4.89, 0.01, 32, 77.2, -171.35, 0.00232, 33, 21.74, -173.41, 0.00178, 60, 103.02, 47.61, 0.07579, 61, 37.06, 57.59, 0.91011, 5, 53, 278.33, -15.46, 0.01, 32, 83.08, -189.27, 0.00013, 33, 26.69, -191.61, 0.00017, 60, 120.44, 40.39, 0.0141, 61, 55.73, 54.89, 0.9756, 3, 53, 262.66, -26.05, 0.01, 60, 137.91, 33.15, 2e-05, 61, 74.44, 52.18, 0.98998, 2, 53, 251.17, -33.82, 0.01, 61, 88.17, 50.19, 0.99, 2, 53, 238.64, -40.37, 0.01, 61, 101.84, 46.6, 0.99, 2, 53, 218.82, -50.74, 0.01, 61, 123.48, 40.93, 0.99, 2, 53, 199.6, -56, 0.01, 61, 141.23, 31.87, 0.99, 2, 53, 171.04, -63.81, 0.01, 61, 167.61, 18.42, 0.99, 2, 53, 151.28, -43.18, 0.01, 61, 168.34, -10.13, 0.99, 3, 53, 160.12, -3.51, 0.01, 61, 135.09, -33.52, 0.9678, 31, 103.08, 126.97, 0.0222, 3, 53, 167.73, 23.87, 0.01, 61, 111.04, -48.64, 0.86858, 31, 88.93, 102.33, 0.12142, 3, 53, 171.91, 35.92, 0.01, 61, 99.84, -54.74, 0.78085, 31, 81.9, 91.7, 0.20915, 4, 53, 178.91, 56.09, 0.01, 60, 115.49, -82, 4e-05, 61, 81.08, -64.95, 0.47751, 31, 70.13, 73.88, 0.51245, 3, 53, 165.89, 54.69, 0.01, 61, 91.65, -72.67, 0.2944, 31, 83.09, 72.02, 0.6956, 3, 53, 141.98, 52.12, 0.01, 61, 111.07, -86.87, 0.13486, 31, 106.89, 68.59, 0.85514, 3, 53, 121.63, 55.19, 0.01, 61, 124.05, -102.84, 0.06215, 31, 125.85, 60.58, 0.92785, 3, 53, 103.15, 57.98, 0.01, 61, 135.84, -117.34, 0.02693, 31, 143.06, 53.3, 0.96307, 3, 53, 84.59, 60.78, 0.01, 61, 147.68, -131.91, 0.00837, 31, 160.35, 45.99, 0.98163, 3, 53, 60.28, 66.43, 0.01, 61, 161.85, -152.44, 0.00074, 31, 182.5, 34.5, 0.98926, 2, 53, 37.99, 71.62, 0.01, 31, 202.82, 23.96, 0.99, 2, 53, 21.54, 78.42, 0.01, 31, 217.07, 13.3, 0.99, 2, 53, 2.58, 86.28, 0.01, 31, 233.5, 1, 0.99, 2, 53, -6.19, 107.74, 0.01, 31, 236.68, -21.97, 0.99, 2, 53, 11.7, 116.89, 0.01, 31, 217.09, -26.41, 0.99, 2, 53, 33.37, 127.97, 0.01, 31, 193.34, -31.78, 0.99, 2, 53, 55.83, 139.45, 0.01, 31, 168.74, -37.34, 0.99, 2, 53, 69.91, 146.64, 0.01, 31, 153.33, -40.83, 0.99, 2, 53, 86.74, 155.09, 0.01, 31, 134.93, -44.85, 0.99, 3, 53, 103.47, 163.49, 0.01, 30, 151.6, -78.14, 0.00162, 31, 116.64, -48.84, 0.98838, 3, 53, 118.19, 170.88, 0.01, 30, 135.17, -77.09, 0.01333, 31, 100.55, -52.36, 0.97667, 4, 23, -409.33, -321.62, 2e-05, 53, 130.29, 181, 0.01, 30, 119.61, -79.72, 0.04294, 31, 86.32, -59.18, 0.94704, 5, 23, -393.06, -308.01, 0.00058, 53, 146.56, 194.61, 0.01, 58, -199.38, -397.21, 0.00041, 30, 98.69, -83.26, 0.12939, 31, 67.19, -68.34, 0.85962, 5, 23, -382.61, -299.28, 0.00159, 34, 36.25, 521.96, 3e-05, 58, -199.1, -383.59, 0.00155, 30, 85.27, -85.54, 0.21967, 31, 54.9, -74.22, 0.77717, 5, 23, -370.9, -288.23, 0.00359, 34, 24.92, 510.52, 0.0001, 58, -199.73, -367.51, 0.00414, 30, 69.58, -89.16, 0.34889, 31, 40.82, -82.02, 0.64328, 6, 23, -356.45, -274.6, 0.00818, 34, 10.93, 496.41, 0.00035, 58, -200.51, -347.65, 0.01079, 30, 50.22, -93.64, 0.51014, 32, -177.17, -214.05, 0.00062, 31, 23.45, -91.65, 0.46992, 6, 23, -341.46, -260.46, 0.01622, 34, -3.56, 481.77, 0.00087, 58, -201.32, -327.07, 0.02303, 30, 30.15, -98.28, 0.64892, 32, -186.66, -195.76, 0.00382, 31, 5.43, -101.64, 0.30715, 6, 23, -319.52, -240.78, 0.03597, 34, -24.83, 461.36, 0.00241, 58, -201.73, -297.6, 0.05311, 30, 1.28, -104.2, 0.75818, 32, -199.57, -169.26, 0.01641, 31, -20.7, -115.28, 0.13391, 6, 23, -304.16, -217.76, 0.06424, 34, -39.41, 437.83, 0.00513, 58, -208.99, -270.89, 0.09221, 30, -23.61, -116.32, 0.75389, 32, -217.5, -148.18, 0.03428, 31, -41.29, -133.78, 0.05024, 7, 23, -270.08, -158.7, 0.15851, 34, -71.47, 377.66, 0.01891, 58, -231.11, -206.39, 0.17745, 30, -82.83, -150.12, 0.57346, 32, -264.96, -99.22, 0.06017, 31, -88.92, -182.57, 0.00151, 54, 270.35, 405.17, 0.01, 6, 23, -246, -92.29, 0.29828, 34, -93.29, 310.47, 0.05813, 58, -265.34, -144.6, 0.2003, 30, -137.13, -195.3, 0.37619, 32, -322.23, -57.86, 0.04709, 54, 294.42, 471.58, 0.02, 6, 23, -236.76, -18.99, 0.43948, 34, -100.04, 236.9, 0.16032, 58, -314.52, -89.47, 0.13979, 30, -182.09, -253.91, 0.21909, 32, -390.18, -28.89, 0.02132, 54, 303.66, 544.87, 0.02, 6, 23, -240.05, 53.62, 0.45155, 34, -94.3, 164.45, 0.35568, 58, -371.41, -44.23, 0.06045, 30, -215.89, -318.26, 0.10672, 32, -460.91, -12.15, 0.00559, 54, 300.37, 617.48, 0.02, 6, 23, -254.23, 102.6, 0.34707, 34, -78.48, 115.97, 0.56552, 58, -417.64, -22.72, 0.02367, 30, -228.37, -367.7, 0.05258, 32, -511.9, -12.36, 0.00116, 54, 286.2, 666.47, 0.01, 6, 23, -276.35, 128.15, 0.23577, 34, -55.5, 91.18, 0.71463, 58, -451.44, -22.61, 0.01063, 30, -222.16, -400.93, 0.02875, 32, -542.54, -26.63, 0.00022, 54, 264.07, 692.02, 0.01, 6, 23, -298.17, 149.77, 0.13332, 34, -32.96, 70.32, 0.83798, 58, -482.07, -24.85, 0.0044, 30, -214.23, -430.6, 0.01427, 32, -569.31, -41.69, 2e-05, 54, 242.25, 713.63, 0.01, 6, 23, -320.03, 159.85, 0.05913, 34, -10.78, 60.97, 0.90648, 58, -504.04, -34.7, 0.00163, 30, -200.45, -450.34, 0.00612, 54, 220.39, 723.72, 0.01, 35, -27.92, 74.49, 0.01664, 6, 23, -333.67, 161.66, 0.03041, 34, 2.91, 59.63, 0.8832, 58, -514.37, -43.79, 0.00079, 30, -189.59, -458.78, 0.00326, 54, 206.76, 725.53, 0.01, 35, -15.53, 68.51, 0.07234, 6, 23, -345.05, 163.17, 0.01562, 34, 14.33, 58.51, 0.80083, 58, -522.98, -51.37, 0.0004, 30, -180.53, -465.82, 0.00178, 54, 195.38, 727.04, 0.01, 35, -5.19, 63.52, 0.17138, 6, 23, -359.78, 165.12, 0.0053, 34, 29.12, 57.05, 0.57051, 58, -534.13, -61.19, 0.00013, 30, -168.8, -474.94, 0.0007, 54, 180.65, 728.99, 0.01, 35, 8.19, 57.07, 0.41336, 6, 23, -374.15, 164.61, 0.00117, 34, 43.47, 58.05, 0.31744, 58, -543.2, -72.36, 3e-05, 30, -156.13, -481.76, 0.0002, 54, 166.27, 728.48, 0.01, 35, 22.01, 53.06, 0.67115, 5, 23, -388.65, 164.1, 9e-05, 34, 57.95, 59.05, 0.12509, 30, -143.35, -488.63, 2e-05, 54, 151.77, 727.96, 0.01, 35, 35.95, 49.02, 0.8648, 3, 34, 69.65, 59.86, 0.04766, 54, 140.05, 727.55, 0.01, 35, 47.21, 45.76, 0.94234, 3, 34, 81.7, 60.7, 0.00892, 54, 127.98, 727.12, 0.01, 35, 58.81, 42.39, 0.98108, 2, 54, 111.85, 724.79, 0.01, 35, 74.86, 39.57, 0.99, 2, 54, 96.65, 722.6, 0.01, 35, 89.98, 36.91, 0.99, 2, 54, 81.98, 720.48, 0.01, 35, 104.58, 34.34, 0.99, 2, 54, 66.18, 718.2, 0.01, 35, 120.31, 31.57, 0.99, 2, 54, 53.34, 714.78, 0.01, 35, 133.57, 30.81, 0.99, 2, 54, 43.25, 712.1, 0.01, 35, 144, 30.21, 0.99, 2, 54, 32.25, 709.17, 0.01, 35, 155.36, 29.56, 0.99, 2, 54, 22.04, 706.45, 0.01, 35, 165.9, 28.95, 0.99, 2, 54, 7.71, 701.17, 0.01, 35, 181.17, 29.49, 0.99, 2, 54, -5.14, 696.43, 0.01, 35, 194.85, 29.98, 0.99, 2, 54, -21.57, 690.37, 0.01, 35, 212.36, 30.61, 0.99, 2, 54, -34.7, 685.52, 0.01, 35, 226.35, 31.11, 0.99, 2, 54, -33.5, 711.52, 0.01, 35, 217.09, 6.79, 0.99, 2, 54, -25.34, 728.86, 0.01, 35, 203.92, -7.13, 0.99, 2, 54, -17.41, 745.7, 0.01, 35, 191.13, -20.66, 0.99, 2, 54, -11.1, 759.09, 0.01, 35, 180.95, -31.41, 0.99, 2, 54, -4.6, 769.59, 0.01, 35, 171.5, -39.35, 0.99, 2, 54, 4.6, 784.43, 0.01, 35, 158.13, -50.57, 0.99, 2, 54, 18.11, 800.34, 0.01, 35, 140.32, -61.47, 0.99, 2, 54, 31.22, 813.01, 0.01, 35, 123.91, -69.42, 0.99, 2, 54, 45.2, 821.06, 0.01, 35, 108.11, -72.7, 0.99, 2, 54, 61.18, 826.08, 0.01, 35, 91.37, -72.48, 0.99, 2, 54, 80.16, 830.8, 0.01, 35, 71.86, -71.03, 0.99, 3, 34, 116.47, -44.73, 6e-05, 54, 96.8, 833.67, 0.01, 35, 55.16, -68.56, 0.98994, 3, 34, 97.54, -46.62, 0.01323, 54, 115.77, 834.91, 0.01, 35, 36.75, -63.82, 0.97677, 3, 34, 81.66, -47.18, 0.06866, 54, 131.67, 834.94, 0.01, 35, 21.64, -58.88, 0.92134, 3, 34, 59.57, -47.66, 0.31837, 54, 153.76, 834.67, 0.01, 35, 0.74, -51.73, 0.67163, 3, 34, 46.26, -47.8, 0.59823, 54, 167.06, 834.36, 0.01, 35, -11.81, -47.28, 0.39177, 4, 23, -363.18, 270.84, 7e-05, 34, 36.09, -48.49, 0.86597, 54, 177.25, 834.7, 0.01, 35, -21.59, -44.42, 0.12396, 2, 34, 39.19, -84.47, 0.99985, 35, -31.07, -79.27, 0.00015, 1, 34, 36.32, -125.68, 1, 1, 34, 25.75, -170.33, 1, 1, 34, 6.39, -211.34, 1, 2, 23, -301.6, 403.66, 0.00417, 34, -20.96, -183.31, 0.99583, 2, 23, -274.15, 372.31, 0.03006, 34, -49.45, -152.91, 0.96994, 3, 23, -242.3, 346.66, 0.0939, 34, -82.15, -128.35, 0.906, 30, -361.82, -572.39, 0.0001, 4, 23, -205.83, 319.02, 0.24059, 34, -119.54, -101.96, 0.74881, 53, 333.79, 821.65, 0.01, 30, -379.37, -530.12, 0.00061, 4, 23, -215.51, 364.88, 0.31127, 34, -108.31, -147.47, 0.67871, 53, 324.11, 867.51, 0.01, 30, -394.15, -574.6, 2e-05, 3, 23, -213.84, 413.76, 0.32547, 34, -108.32, -196.38, 0.66453, 53, 325.78, 916.39, 0.01, 3, 23, -196.65, 466.64, 0.33132, 34, -123.71, -249.81, 0.65868, 53, 342.96, 969.27, 0.01, 3, 23, -162.55, 502.82, 0.33392, 34, -156.58, -287.12, 0.65608, 53, 377.07, 1005.44, 0.01, 3, 23, -126.7, 525.54, 0.33497, 34, -191.64, -311.04, 0.65503, 53, 412.92, 1028.17, 0.01, 3, 23, -88.27, 523.77, 0.33552, 34, -230.11, -310.57, 0.65448, 53, 451.35, 1026.39, 0.01, 3, 23, -93.6, 489.63, 0.33898, 34, -225.93, -276.26, 0.65102, 53, 446.02, 992.25, 0.01, 3, 23, -85.37, 453.25, 0.3538, 34, -235.39, -240.18, 0.6362, 53, 454.25, 955.87, 0.01, 3, 23, -73.24, 428.34, 0.37781, 34, -248.36, -215.7, 0.61219, 53, 466.38, 930.97, 0.01, 3, 23, -31.12, 399.84, 0.43723, 34, -291.41, -188.64, 0.55277, 53, 508.5, 902.47, 0.01, 3, 23, 8.48, 370.63, 0.49778, 34, -331.97, -160.78, 0.49222, 53, 548.09, 873.25, 0.01, 3, 23, 44.64, 339.08, 0.55759, 34, -369.19, -130.48, 0.43241, 53, 584.26, 841.71, 0.01, 3, 23, 56.03, 317.31, 0.59634, 34, -381.3, -109.1, 0.39366, 53, 595.65, 819.94, 0.01, 3, 23, 77.19, 334.73, 0.62055, 34, -401.86, -127.23, 0.36945, 53, 616.81, 837.36, 0.01, 3, 23, 104.52, 333.08, 0.63056, 34, -429.23, -126.5, 0.35944, 53, 644.14, 835.71, 0.01, 3, 23, 126, 323.48, 0.63777, 34, -451.02, -117.63, 0.35223, 53, 665.61, 826.1, 0.01, 3, 23, 155.94, 302.13, 0.64725, 34, -481.67, -97.31, 0.34275, 53, 695.56, 804.76, 0.01, 3, 23, 197.18, 262.64, 0.65606, 34, -524.22, -59.24, 0.33394, 53, 736.8, 765.27, 0.01, 3, 23, 219.31, 221.3, 0.65862, 34, -547.73, -18.67, 0.33138, 53, 758.93, 723.93, 0.01, 3, 23, 218.43, 202.16, 0.65882, 34, -547.5, 0.49, 0.33118, 53, 758.04, 704.79, 0.01, 3, 23, 40.37, 240.91, 0.64331, 34, -368.23, -32.22, 0.34669, 53, 579.99, 743.54, 0.01, 3, 23, 33.6, 263.93, 0.61501, 34, -360.69, -55, 0.37499, 53, 573.22, 766.56, 0.01, 3, 23, 42.18, 288.59, 0.60039, 34, -368.43, -79.93, 0.38961, 53, 581.79, 791.22, 0.01, 3, 23, -343, 257.63, 0.00142, 34, 15.48, -35.97, 0.98858, 54, 197.42, 821.49, 0.01, 2, 34, 0.06, -3.08, 0.99, 54, 211.73, 788.1, 0.01, 6, 23, -326.68, 185.91, 0.02725, 34, -3.26, 35.16, 0.93739, 58, -528.04, -22.59, 0.00065, 30, -207.86, -476.18, 0.00276, 54, 213.75, 749.77, 0.01, 35, -29.74, 47.66, 0.02195, 4, 23, 56.95, 16.14, 0.96495, 34, -392.4, 191.86, 0.0116, 59, -146.95, 186.42, 0.01608, 58, -147.98, 155, 0.00736, 7, 23, -32.31, -30.91, 0.79889, 34, -304.78, 241.9, 0.01466, 59, -190.36, 95.34, 0.00656, 58, -171.17, 56.8, 0.12029, 30, -352.59, -140.45, 0.04263, 32, -322.68, 164.47, 0.00696, 54, 508.11, 532.96, 0.01, 6, 23, -150.46, -42.51, 0.47845, 34, -187.09, 257.5, 0.09596, 58, -240.07, -39.87, 0.17802, 30, -244.73, -190.06, 0.20067, 32, -343.9, 47.66, 0.0269, 54, 389.97, 521.35, 0.02, 5, 23, -186.42, 189.88, 0.34191, 34, -143.3, 26.45, 0.62767, 58, -438.86, 85.74, 0.0036, 30, -330.96, -408.83, 0.01682, 54, 354.01, 753.74, 0.01, 4, 23, -112.28, 278.93, 0.41738, 34, -214.38, -65.05, 0.57103, 30, -439.9, -448.3, 0.00159, 54, 428.15, 842.79, 0.01, 3, 23, -31.14, 305.71, 0.51441, 34, -294.57, -94.56, 0.48554, 30, -523.46, -430.48, 5e-05], "hull": 160, "edges": [0, 318, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 258, 260, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 270, 272, 272, 274, 274, 276, 276, 278, 278, 280, 280, 282, 282, 284, 284, 286, 286, 288, 288, 290, 290, 292, 292, 294, 294, 296, 296, 298, 298, 300, 300, 302, 302, 304, 304, 306, 306, 308, 308, 310, 310, 312, 312, 314, 314, 316, 316, 318], "width": 232, "height": 158}}, "archer4": {"archer4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 38, -66.78, -79.55, 0.98, 54, 140.35, 399.35, 0.02, 2, 38, -58.38, 102.56, 0.98, 54, 148.75, 581.45, 0.02, 2, 38, 50.94, 97.51, 0.98, 54, 258.07, 576.41, 0.02, 2, 38, 42.54, -84.59, 0.98, 54, 249.67, 394.3, 0.02], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 37, "height": 22}}, "archer5": {"archer5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 19, -76.84, -80.55, 0.98, 54, 39.36, 446.74, 0.02, 2, 19, -67.35, 125.19, 0.98, 54, 48.86, 652.48, 0.02, 2, 19, 83.05, 118.25, 0.98, 54, 199.25, 645.54, 0.02, 2, 19, 73.55, -87.5, 0.98, 54, 189.75, 439.79, 0.02], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 40, "height": 31}, "archer5_1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 19, -70.17, -93.89, 0.98, 54, 46.04, 433.38, 0.02, 2, 19, -59.48, 137.86, 0.98, 54, 56.73, 665.14, 0.02, 2, 19, 76.38, 131.59, 0.98, 54, 192.59, 658.87, 0.02, 2, 19, 65.68, -100.16, 0.98, 54, 181.89, 427.11, 0.02], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 40, "height": 31}, "archer5_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 19, -70.17, -93.89, 0.98, 54, 46.04, 433.38, 0.02, 2, 19, -59.48, 137.86, 0.98, 54, 56.73, 665.14, 0.02, 2, 19, 76.38, 131.59, 0.98, 54, 192.59, 658.87, 0.02, 2, 19, 65.68, -100.16, 0.98, 54, 181.89, 427.11, 0.02], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 40, "height": 31}, "archer5_3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 19, -57.01, -120.11, 0.98, 54, 59.2, 407.17, 0.02, 2, 19, -48.81, 137.37, 0.98, 54, 67.4, 664.65, 0.02, 2, 19, 126.02, 129.3, 0.98, 54, 242.23, 656.58, 0.02, 2, 19, 117.82, -128.18, 0.98, 54, 234.03, 399.1, 0.02], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 40, "height": 31}, "archer5_4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 19, -163.46, -105.42, 0.98, 54, -47.25, 421.86, 0.02, 2, 19, -151.79, 147.35, 0.98, 54, -35.58, 674.63, 0.02, 2, 19, 46.52, 138.2, 0.98, 54, 162.73, 665.48, 0.02, 2, 19, 34.85, -114.57, 0.98, 54, 151.06, 412.7, 0.02], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 36, "height": 28}, "archer16_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 19, -83.94, -97.77, 0.98, 54, 32.27, 429.5, 0.02, 2, 19, -74.62, 104.24, 0.98, 54, 41.59, 631.51, 0.02, 2, 19, 61.24, 97.97, 0.98, 54, 177.45, 625.24, 0.02, 2, 19, 51.91, -104.04, 0.98, 54, 168.13, 423.23, 0.02], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 30, "height": 24}}, "archer6": {"archer6": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 21, -48.48, -47.07, 0.97, 54, 8.38, 311.5, 0.03, 2, 21, -43.7, 56.47, 0.97, 54, 13.16, 415.04, 0.03, 2, 21, 34.22, 52.87, 0.97, 54, 91.07, 411.45, 0.03, 2, 21, 29.44, -50.67, 0.97, 54, 86.29, 307.91, 0.03], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 18, "height": 15}}, "archer7": {"archer7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 39, -63.24, -46.4, 0.99, 53, 120.12, 171.96, 0.01, 2, 39, -58.09, 67.01, 0.98, 54, 126.08, 346.6, 0.02, 2, 39, 44.28, 62.29, 0.98, 54, 228.45, 341.88, 0.02, 2, 39, 39.13, -51.12, 0.99, 53, 222.49, 167.24, 0.01], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 22, "height": 23}}, "archer8": {"archer8": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 20, -98.03, -56.76, 0.99, 53, 6.08, 158.13, 0.01, 2, 20, -91.9, 76.29, 0.98, 54, 13.02, 352.41, 0.02, 2, 20, 65.41, 69.03, 0.98, 54, 170.33, 345.16, 0.02, 2, 20, 59.29, -64.02, 0.99, 53, 163.39, 150.87, 0.01], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 26, "height": 32}, "archer8_1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 20, -91.1, -63.49, 0.99, 53, 13.01, 151.4, 0.01, 2, 20, -84.37, 82.36, 0.98, 54, 20.56, 358.48, 0.02, 2, 20, 58.48, 75.76, 0.98, 54, 163.4, 351.89, 0.02, 2, 20, 51.75, -70.08, 0.99, 53, 155.86, 144.8, 0.01], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 26, "height": 32}, "archer8_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 20, -91.1, -63.49, 0.99, 53, 13.01, 151.4, 0.01, 2, 20, -84.37, 82.36, 0.98, 54, 20.56, 358.48, 0.02, 2, 20, 58.48, 75.76, 0.98, 54, 163.4, 351.89, 0.02, 2, 20, 51.75, -70.08, 0.99, 53, 155.86, 144.8, 0.01], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 26, "height": 32}, "archer8_3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 20, -101.31, -63.02, 0.99, 53, 2.8, 151.87, 0.01, 2, 20, -59.25, 81.2, 0.98, 54, 45.67, 357.32, 0.02, 2, 20, 101.76, 73.76, 0.98, 54, 206.68, 349.89, 0.02, 2, 20, 59.7, -70.45, 0.99, 53, 163.81, 144.43, 0.01], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 26, "height": 32}, "archer8_4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 20, -113.91, -87, 0.99, 53, -9.79, 127.88, 0.01, 2, 20, -106.28, 78.14, 0.98, 54, -1.36, 354.27, 0.02, 2, 20, 37.32, 71.51, 0.98, 54, 142.24, 347.64, 0.02, 2, 20, 29.7, -93.63, 0.99, 53, 133.81, 121.25, 0.01], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 28, "height": 24}, "archer16_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 20, -88.73, -75.25, 0.99, 53, 15.39, 139.64, 0.01, 2, 20, -81.99, 70.59, 0.98, 54, 22.93, 346.72, 0.02, 2, 20, 60.85, 64, 0.98, 54, 165.78, 340.13, 0.02, 2, 20, 54.12, -81.84, 0.99, 53, 158.24, 133.04, 0.01], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 30, "height": 24}}, "archer9": {"archer9": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 22, -36.25, -50.16, 0.97, 54, -74.98, 352.81, 0.03, 2, 22, -30.36, 77.34, 0.97, 54, -69.09, 480.31, 0.03, 2, 22, 23.65, 74.85, 0.97, 54, -15.08, 477.82, 0.03, 2, 22, 17.77, -52.65, 0.97, 54, -20.97, 350.31, 0.03], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 25, "height": 11}, "zui2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 22, -68.38, -129.85, 0.97, 54, -107.12, 273.11, 0.03, 2, 22, -57.83, 98.7, 0.97, 54, -96.57, 501.66, 0.03, 2, 22, 37.43, 94.31, 0.97, 54, -1.31, 497.27, 0.03, 2, 22, 26.88, -134.24, 0.97, 54, -11.86, 268.72, 0.03], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 30, "height": 16}, "zui3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 22, -58.81, -55.99, 0.97, 54, -97.56, 346.98, 0.03, 2, 22, -53.61, 56.71, 0.97, 54, -92.36, 459.68, 0.03, 2, 22, 14.95, 53.55, 0.97, 54, -23.79, 456.51, 0.03, 2, 22, 9.75, -59.15, 0.97, 54, -28.99, 343.81, 0.03], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 30, "height": 28}}, "archer10": {"archer10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 11, 64.77, 241.78, 1, 2, 11, 75.27, 469.17, 0.97, 53, -1.03, 900.83, 0.03, 2, 11, 324, 457.69, 0.97, 53, 247.71, 889.35, 0.03, 1, 11, 313.51, 230.3, 1], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 47, "height": 44}}, "archer11": {"archer11": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 23, -642.83, -208.62, 0.99, 53, -103.21, 294, 0.01, 2, 23, -606.31, 582.53, 0.99, 53, -66.7, 1085.16, 0.01, 2, 23, 25.02, 553.4, 0.99, 53, 564.63, 1056.03, 0.01, 2, 23, -11.5, -237.76, 0.99, 53, 528.12, 264.87, 0.01], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 158, "height": 126}}, "archer12": {"archer12": {"type": "mesh", "uvs": [0.7924, 0.05397, 0.92352, 0.1322, 0.97869, 0.20979, 1, 0.31506, 1, 0.44489, 0.97452, 0.5302, 0.95417, 0.6053, 0.94544, 0.69571, 0.95998, 0.74529, 0.97015, 0.81602, 0.95126, 0.8707, 0.88003, 0.92612, 0.76811, 0.97861, 0.67654, 1, 0.55212, 1, 0.42933, 0.97875, 0.30802, 0.94703, 0.21759, 0.90498, 0.14848, 0.8504, 0.10878, 0.77221, 0.06687, 0.67854, 0.02276, 0.57306, 0, 0.45135, 0, 0.26382, 0.03932, 0.15849, 0.15028, 0.0643, 0.27917, 0.01806, 0.3961, 0, 0.53975, 0, 0.63712, 0, 0.39439, 0.02662, 0.2766, 0.05488, 0.16565, 0.10455, 0.08712, 0.17819, 0.03335, 0.28009, 0.03079, 0.44792, 0.06066, 0.5661, 0.101, 0.67766, 0.14384, 0.76893, 0.18405, 0.83904, 0.23425, 0.87999, 0.32609, 0.92252, 0.44491, 0.95216, 0.56116, 0.97214, 0.65878, 0.9702, 0.76131, 0.94119, 0.87405, 0.87825, 0.90934, 0.83694, 0.93287, 0.81727, 0.93479, 0.75486, 0.92018, 0.70038, 0.93406, 0.60249, 0.95072, 0.51646, 0.96676, 0.43724, 0.96923, 0.32518, 0.94746, 0.2308, 0.89692, 0.15592, 0.7733, 0.08103, 0.53325, 0.03109, 0.64623, 0.04593, 0.68644, 0.8985, 0.69154, 0.78489, 0.7006, 0.64458, 0.68701, 0.43611, 0.67054, 0.22885, 0.26157, 0.45135, 0.20851, 0.54467, 0.18889, 0.64092, 0.23395, 0.7255, 0.31099, 0.76925, 0.45561, 0.74956, 0.55082, 0.70727, 0.59152, 0.63217, 0.58134, 0.54103, 0.48396, 0.48635, 0.36913, 0.45718, 0.28987, 0.45288, 0.33497, 0.40104, 0.39239, 0.3376, 0.45634, 0.31136, 0.52538, 0.33104, 0.58425, 0.43093, 0.63876, 0.54541, 0.15328, 0.603, 0.08304, 0.62798, 0.73114, 0.60829, 0.75008, 0.52877, 0.76481, 0.47318, 0.78304, 0.41055, 0.82793, 0.44292, 0.89667, 0.46051, 0.91771, 0.51821, 0.8132, 0.4584, 0.86782, 0.45313, 0.72202, 0.6688, 0.74797, 0.71666, 0.80759, 0.75043, 0.88124, 0.7434], "triangles": [30, 26, 27, 58, 27, 28, 30, 27, 58, 0, 59, 29, 58, 28, 29, 59, 58, 29, 31, 26, 30, 31, 25, 26, 57, 59, 0, 32, 25, 31, 56, 0, 1, 57, 0, 56, 33, 24, 25, 32, 33, 25, 56, 1, 2, 64, 59, 57, 58, 59, 64, 55, 56, 2, 34, 23, 24, 33, 34, 24, 79, 30, 58, 79, 58, 64, 31, 30, 79, 55, 2, 3, 54, 55, 3, 80, 79, 64, 78, 31, 79, 32, 31, 78, 77, 32, 78, 33, 32, 77, 65, 34, 33, 56, 64, 57, 88, 56, 55, 88, 55, 54, 88, 64, 56, 81, 80, 64, 64, 63, 81, 88, 63, 64, 54, 89, 88, 54, 93, 89, 54, 3, 4, 53, 54, 4, 34, 22, 23, 65, 35, 34, 33, 77, 65, 35, 22, 34, 76, 65, 77, 54, 90, 93, 75, 77, 78, 76, 77, 75, 92, 88, 89, 92, 89, 93, 53, 90, 54, 87, 63, 88, 87, 88, 92, 74, 79, 80, 74, 80, 81, 78, 79, 74, 75, 78, 74, 52, 90, 53, 52, 53, 4, 91, 90, 52, 86, 63, 87, 5, 52, 4, 73, 74, 81, 82, 73, 81, 63, 82, 81, 66, 35, 65, 63, 62, 82, 36, 35, 66, 21, 22, 35, 21, 35, 36, 51, 91, 52, 51, 52, 5, 83, 36, 66, 6, 51, 5, 62, 63, 85, 84, 36, 83, 72, 73, 82, 62, 72, 82, 67, 83, 66, 63, 86, 85, 94, 62, 85, 37, 84, 83, 37, 83, 67, 84, 21, 36, 20, 84, 37, 20, 21, 84, 7, 51, 6, 51, 86, 91, 50, 51, 7, 72, 71, 74, 72, 74, 73, 51, 95, 85, 94, 85, 95, 91, 92, 93, 92, 86, 87, 90, 91, 93, 91, 86, 92, 86, 51, 85, 50, 95, 51, 68, 66, 69, 67, 66, 68, 50, 96, 95, 70, 75, 74, 70, 74, 71, 97, 96, 50, 49, 50, 7, 49, 7, 8, 97, 50, 49, 38, 37, 67, 38, 67, 68, 76, 66, 65, 75, 66, 76, 75, 69, 66, 70, 69, 75, 19, 37, 38, 20, 37, 19, 62, 71, 72, 61, 62, 94, 61, 94, 95, 61, 71, 62, 49, 8, 9, 48, 97, 49, 48, 49, 9, 47, 97, 48, 39, 38, 68, 39, 68, 69, 18, 38, 39, 19, 38, 18, 10, 48, 9, 47, 48, 10, 47, 46, 96, 47, 96, 97, 40, 39, 69, 60, 71, 61, 17, 39, 40, 18, 39, 17, 41, 69, 70, 40, 69, 41, 10, 11, 46, 10, 46, 47, 96, 61, 95, 45, 96, 46, 45, 46, 11, 45, 61, 96, 60, 61, 45, 16, 40, 41, 17, 40, 16, 42, 41, 70, 71, 43, 70, 44, 60, 45, 70, 43, 42, 43, 71, 60, 44, 43, 60, 12, 45, 11, 13, 44, 45, 15, 41, 42, 16, 41, 15, 14, 42, 43, 15, 42, 14, 12, 13, 45, 14, 43, 44, 13, 14, 44], "vertices": [2, 11, 537.29, -154.11, 0.99, 53, 461, 277.54, 0.01, 2, 11, 485.51, -232.71, 0.99, 53, 409.21, 198.94, 0.01, 2, 11, 436.27, -264.51, 0.99, 53, 359.97, 167.14, 0.01, 2, 11, 370.99, -274.66, 0.99, 53, 294.69, 156.99, 0.01, 2, 11, 291.23, -270.98, 0.99, 53, 214.93, 160.67, 0.01, 2, 11, 239.54, -252.85, 0.99, 53, 163.25, 178.8, 0.01, 2, 11, 193.99, -238.18, 0.99, 53, 117.69, 193.47, 0.01, 2, 11, 138.69, -230.24, 0.99, 53, 62.39, 201.41, 0.01, 2, 11, 107.82, -237.8, 0.99, 53, 31.52, 193.85, 0.01, 1, 11, 64.08, -242.06, 1, 1, 11, 31.02, -228.86, 1, 2, 11, -1, -183.4, 0.99, 54, -76.48, 309.49, 0.01, 2, 11, -30.07, -112.92, 0.98, 54, -105.55, 379.96, 0.02, 2, 11, -40.6, -55.88, 0.97, 54, -116.09, 437.01, 0.03, 2, 11, -37.06, 20.81, 0.98, 54, -112.55, 513.7, 0.02, 2, 11, -20.51, 95.88, 0.98, 54, -96, 588.77, 0.02, 2, 11, 2.42, 169.75, 0.99, 54, -73.06, 662.64, 0.01, 1, 11, 30.83, 224.3, 1, 2, 11, 66.33, 265.35, 0.99, 53, -9.97, 697, 0.01, 2, 11, 115.49, 287.6, 0.99, 53, 39.19, 719.25, 0.01, 2, 11, 174.23, 310.77, 0.99, 53, 97.93, 742.42, 0.01, 2, 11, 240.29, 334.97, 0.99, 53, 163.99, 766.62, 0.01, 2, 11, 315.7, 345.55, 0.99, 53, 239.41, 777.2, 0.01, 2, 11, 430.91, 340.23, 0.99, 53, 354.62, 771.88, 0.01, 2, 11, 494.5, 313.01, 0.99, 53, 418.2, 744.66, 0.01, 2, 11, 549.21, 241.95, 0.99, 53, 472.92, 673.6, 0.01, 2, 11, 573.95, 161.2, 0.99, 53, 497.66, 592.85, 0.01, 2, 11, 581.72, 88.62, 0.99, 53, 505.43, 520.27, 0.01, 2, 11, 577.64, 0.08, 0.99, 53, 501.34, 431.73, 0.01, 2, 11, 574.87, -59.93, 0.99, 53, 498.57, 371.72, 0.01, 2, 11, 565.42, 90.43, 0.99, 53, 489.12, 522.08, 0.01, 2, 11, 551.41, 163.83, 0.99, 53, 475.11, 595.47, 0.01, 2, 11, 524.05, 233.62, 0.99, 53, 447.75, 665.27, 0.01, 2, 11, 481.04, 284.11, 0.99, 53, 404.75, 715.76, 0.01, 2, 11, 419.97, 320.14, 0.99, 53, 343.67, 751.79, 0.01, 2, 11, 316.93, 326.48, 0.99, 53, 240.64, 758.13, 0.01, 2, 11, 243.49, 311.42, 0.99, 53, 167.19, 743.06, 0.01, 2, 11, 173.8, 289.71, 0.99, 53, 97.5, 721.36, 0.01, 2, 11, 116.51, 265.9, 0.99, 53, 40.21, 697.54, 0.01, 2, 11, 72.29, 243.1, 0.99, 53, -4, 674.75, 0.01, 1, 11, 45.71, 213.32, 1, 2, 11, 16.97, 157.92, 0.99, 54, -58.52, 650.81, 0.01, 2, 11, -4.62, 85.53, 0.98, 54, -80.11, 578.42, 0.02, 2, 11, -20.2, 14.45, 0.98, 54, -95.69, 507.34, 0.02, 2, 11, -21.79, -45.78, 0.97, 54, -97.28, 447.11, 0.03, 2, 11, -6.88, -109.8, 0.98, 54, -82.37, 383.09, 0.02, 2, 11, 28.58, -181.06, 0.99, 54, -46.91, 311.82, 0.01, 1, 11, 52.95, -203.99, 1, 1, 11, 64.37, -219.04, 1, 2, 11, 102.66, -222, 0.99, 53, 26.36, 209.65, 0.01, 2, 11, 136.54, -214.54, 0.99, 53, 60.25, 217.11, 0.01, 2, 11, 196.28, -225.87, 0.99, 53, 119.98, 205.78, 0.01, 2, 11, 248.66, -238.57, 0.99, 53, 172.36, 193.07, 0.01, 2, 11, 296.87, -250.71, 0.99, 53, 220.58, 180.94, 0.01, 2, 11, 365.65, -255.41, 0.99, 53, 289.35, 176.24, 0.01, 2, 11, 424.25, -244.66, 0.99, 53, 347.96, 186.98, 0.01, 2, 11, 471.69, -215.64, 0.99, 53, 395.4, 216.01, 0.01, 2, 11, 521.21, -141.57, 0.99, 53, 444.91, 290.08, 0.01, 2, 11, 558.72, 4.97, 0.99, 53, 482.42, 436.62, 0.01, 2, 11, 546.39, -64.25, 0.99, 53, 470.1, 367.4, 0.01, 2, 11, 21.48, -64.86, 0.97, 54, -54.01, 428.03, 0.03, 2, 11, 91.13, -71.22, 0.97, 54, 15.64, 421.66, 0.03, 2, 11, 177.07, -80.78, 0.97, 54, 101.58, 412.1, 0.03, 2, 11, 305.53, -78.32, 0.98, 54, 230.04, 414.57, 0.02, 2, 11, 433.33, -74.04, 0.99, 54, 357.84, 418.85, 0.01, 2, 11, 308.27, 184.33, 0.98, 54, 232.78, 677.22, 0.02, 2, 11, 252.44, 219.68, 0.98, 54, 176.95, 712.57, 0.02, 2, 11, 193.87, 234.5, 0.98, 54, 118.38, 727.39, 0.02, 2, 11, 140.63, 209.13, 0.98, 54, 65.14, 702.02, 0.02, 1, 11, 111.56, 162.89, 1, 2, 11, 119.54, 73.19, 0.98, 54, 44.05, 566.08, 0.02, 2, 11, 142.81, 13.31, 0.98, 54, 67.33, 506.2, 0.02, 2, 11, 187.79, -13.9, 0.98, 54, 112.3, 478.98, 0.02, 2, 11, 244.07, -10.22, 0.98, 54, 168.59, 482.67, 0.02, 2, 11, 280.44, 48.26, 0.98, 54, 204.95, 541.14, 0.02, 2, 11, 301.62, 118.2, 0.98, 54, 226.14, 611.09, 0.02, 2, 11, 306.52, 166.93, 0.98, 54, 231.03, 659.82, 0.02, 2, 11, 337.09, 137.66, 0.98, 54, 261.6, 630.55, 0.02, 2, 11, 374.42, 100.48, 0.98, 54, 298.94, 593.37, 0.02, 2, 11, 388.73, 60.32, 0.98, 54, 313.24, 553.2, 0.02, 2, 11, 374.67, 18.32, 0.98, 54, 299.18, 511.21, 0.02, 2, 11, 311.63, -15.13, 0.98, 54, 236.14, 477.76, 0.02, 2, 11, 239.75, -45.48, 0.98, 54, 164.27, 447.41, 0.02, 2, 11, 218.18, 255.38, 0.99, 54, 142.69, 748.26, 0.01, 2, 11, 204.83, 299.38, 0.99, 53, 128.53, 731.03, 0.01, 2, 11, 198.5, -100.64, 0.98, 54, 123.01, 392.25, 0.02, 2, 11, 246.81, -114.56, 0.98, 54, 171.32, 378.33, 0.02, 2, 11, 280.54, -125.22, 0.98, 54, 205.05, 367.67, 0.02, 2, 11, 318.5, -138.23, 0.98, 54, 243.01, 354.65, 0.02, 2, 11, 297.34, -164.98, 0.98, 54, 221.85, 327.9, 0.02, 1, 11, 284.57, -206.85, 1, 1, 11, 248.52, -218.18, 1, 2, 11, 288.24, -155.47, 0.98, 54, 212.76, 337.42, 0.02, 1, 11, 289.93, -189.28, 1, 2, 11, 161.58, -93.3, 0.98, 54, 86.09, 399.59, 0.02, 2, 11, 131.44, -107.94, 0.98, 54, 55.95, 384.95, 0.02, 2, 11, 108.99, -143.73, 0.98, 54, 33.51, 349.16, 0.02, 1, 11, 111.22, -189.32, 1], "hull": 30, "edges": [0, 58, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58], "width": 123, "height": 123}, "archer12_1": {"type": "mesh", "uvs": [0.70901, 0.04721, 0.71437, 0.09785, 0.70704, 0.13213, 0.78938, 0.1315, 0.8715, 0.12297, 0.94256, 0.10221, 0.97245, 0.09907, 0.98429, 0.12087, 0.96493, 0.1777, 0.9321, 0.24635, 0.89217, 0.29778, 0.85319, 0.32597, 0.87673, 0.34986, 0.90732, 0.3988, 0.93143, 0.45539, 0.9422, 0.50631, 0.92884, 0.52617, 0.88402, 0.51359, 0.85137, 0.50465, 0.85137, 0.56027, 0.84395, 0.61092, 0.82941, 0.65925, 0.80795, 0.68769, 0.80044, 0.66829, 0.79239, 0.64788, 0.78379, 0.62195, 0.77969, 0.64112, 0.7838, 0.65788, 0.7933, 0.66671, 0.80072, 0.67433, 0.8021, 0.68117, 0.80032, 0.6858, 0.78924, 0.69397, 0.78082, 0.69898, 0.78439, 0.72435, 0.78034, 0.75819, 0.76516, 0.80244, 0.7152, 0.82811, 0.63047, 0.84232, 0.5384, 0.84013, 0.45221, 0.82265, 0.3931, 0.79673, 0.31896, 0.81774, 0.24884, 0.83696, 0.18433, 0.83115, 0.19835, 0.77975, 0.16509, 0.76232, 0.13183, 0.72612, 0.10018, 0.65684, 0.08319, 0.59814, 0.03528, 0.5373, 0, 0.46166, 0, 0.41151, 0.03601, 0.3967, 0.0596, 0.34739, 0.02127, 0.30216, 0.02201, 0.26271, 0.05377, 0.26213, 0.0899, 0.25431, 0.11201, 0.22184, 0.14436, 0.17373, 0.17995, 0.13163, 0.16917, 0.11479, 0.17133, 0.0805, 0.19936, 0.0378, 0.23873, 0, 0.27108, 0, 0.26873, 0.08286, 0.27061, 0.12952, 0.28988, 0.09911, 0.39703, 0.08444, 0.4874, 0.08105, 0.54455, 0.0865, 0.58666, 0.06847, 0.62952, 0.03576, 0.66599, 0, 0.68335, 0, 0.77725, 0.67571, 0.77612, 0.6581, 0.38958, 0.79001, 0.4277, 0.79629, 0.47775, 0.80406, 0.53343, 0.8048, 0.59707, 0.80258, 0.66303, 0.78779, 0.66237, 0.75193, 0.67297, 0.71681, 0.68027, 0.69907, 0.71109, 0.71016, 0.73753, 0.71142, 0.75708, 0.70597, 0.7666, 0.68775, 0.76685, 0.66006, 0.76401, 0.61429, 0.75421, 0.57754, 0.73752, 0.5552, 0.71837, 0.53037, 0.66334, 0.68732, 0.62282, 0.6513, 0.59907, 0.59975, 0.59084, 0.53548, 0.60541, 0.47933, 0.64656, 0.47086, 0.68297, 0.48498, 0.76381, 0.57049, 0.74806, 0.54345, 0.7353, 0.53206, 0.7268, 0.5413, 0.32694, 0.62529, 0.30365, 0.43754, 0.33012, 0.22501, 0.50479, 0.19785, 0.49844, 0.42692, 0.53867, 0.62529, 0.83268, 0.43287, 0.81022, 0.37198, 0.78513, 0.30568, 0.76311, 0.26885, 0.7202, 0.19715], "triangles": [67, 65, 66, 64, 65, 67, 67, 61, 63, 67, 63, 64, 61, 67, 68, 62, 63, 61, 0, 75, 76, 0, 74, 75, 7, 8, 5, 7, 5, 6, 118, 2, 3, 111, 71, 72, 110, 69, 70, 68, 69, 110, 8, 9, 4, 8, 4, 5, 117, 118, 3, 10, 4, 9, 10, 116, 117, 117, 3, 4, 4, 10, 117, 11, 116, 10, 54, 57, 58, 55, 56, 57, 54, 55, 57, 115, 116, 11, 111, 70, 71, 74, 0, 1, 2, 74, 1, 73, 74, 2, 118, 72, 73, 12, 114, 115, 12, 115, 11, 114, 12, 13, 68, 60, 61, 111, 110, 70, 112, 110, 111, 51, 52, 53, 68, 110, 60, 110, 59, 60, 72, 118, 111, 2, 118, 73, 102, 118, 117, 102, 117, 116, 103, 102, 116, 115, 103, 116, 102, 111, 118, 112, 111, 102, 101, 112, 102, 114, 103, 115, 114, 17, 18, 14, 114, 13, 106, 96, 103, 14, 17, 114, 15, 17, 14, 16, 17, 15, 103, 114, 106, 114, 105, 106, 100, 112, 101, 50, 51, 53, 109, 59, 110, 107, 96, 106, 18, 105, 114, 95, 106, 105, 107, 106, 95, 18, 104, 105, 19, 104, 18, 95, 105, 104, 94, 95, 104, 54, 109, 53, 49, 53, 109, 50, 53, 49, 58, 59, 109, 109, 54, 58, 103, 99, 100, 20, 104, 19, 25, 93, 104, 94, 104, 93, 20, 25, 104, 112, 108, 109, 112, 109, 110, 113, 108, 112, 100, 113, 112, 49, 109, 108, 99, 113, 100, 26, 93, 25, 24, 25, 20, 103, 100, 102, 102, 100, 101, 96, 99, 103, 107, 99, 96, 98, 107, 95, 99, 107, 98, 48, 49, 108, 26, 92, 93, 78, 26, 27, 21, 24, 20, 78, 92, 26, 21, 23, 24, 77, 78, 27, 77, 27, 28, 92, 78, 77, 31, 28, 29, 31, 29, 30, 77, 28, 31, 97, 98, 95, 97, 95, 94, 97, 94, 93, 87, 97, 93, 23, 21, 22, 93, 92, 87, 91, 92, 77, 32, 77, 31, 33, 91, 77, 32, 33, 77, 87, 92, 88, 88, 92, 89, 90, 91, 33, 89, 92, 91, 90, 89, 91, 86, 97, 87, 90, 33, 34, 47, 48, 108, 85, 98, 97, 85, 97, 86, 35, 90, 34, 46, 47, 108, 45, 46, 108, 88, 84, 85, 86, 87, 88, 80, 79, 108, 42, 45, 108, 113, 80, 108, 41, 79, 80, 35, 89, 90, 36, 89, 35, 98, 83, 113, 98, 113, 99, 83, 98, 85, 83, 85, 84, 81, 80, 113, 113, 82, 81, 83, 82, 113, 79, 42, 108, 42, 79, 41, 40, 80, 81, 41, 80, 40, 36, 37, 88, 36, 88, 89, 88, 85, 86, 37, 84, 88, 43, 45, 42, 44, 45, 43, 39, 82, 83, 81, 82, 39, 40, 81, 39, 38, 83, 84, 38, 84, 37, 39, 83, 38], "vertices": [698.52, -218, 650.46, -221.43, 618.45, -212.22, 615.05, -298.94, 619.11, -385.77, 635.25, -461.49, 636.77, -493.1, 615.61, -504.62, 562.91, -481.76, 499.69, -444.21, 453.08, -399.92, 428.37, -357.66, 404.68, -381.4, 356.99, -411.47, 302.39, -434.4, 253.81, -443.51, 235.71, -428.58, 249.76, -381.94, 259.78, -347.95, 207.28, -345.53, 159.83, -335.51, 114.91, -318.09, 89.11, -294.26, 107.78, -287.2, 127.44, -279.61, 152.34, -271.68, 134.45, -266.54, 118.42, -270.13, 109.62, -279.75, 102.07, -287.23, 95.55, -288.39, 91.26, -286.31, 84.09, -274.29, 79.76, -265.2, 55.64, -267.86, 23.89, -262.12, -17.14, -244.2, -38.95, -190.49, -48.24, -100.66, -41.7, -3.82, -21.01, 86.18, 6.33, 147.28, -9.9, 226.25, -24.63, 300.92, -16.01, 368.59, 31.82, 351.58, 49.89, 385.84, 85.68, 419.28, 152.62, 449.59, 208.86, 464.93, 268.62, 512.72, 341.73, 546.57, 389.07, 544.38, 401.3, 505.82, 446.71, 478.84, 491.27, 517.22, 528.48, 514.73, 527.48, 481.26, 533.1, 442.88, 562.68, 418.18, 606.53, 382.02, 644.54, 342.72, 660.96, 353.34, 693.22, 349.58, 732.17, 318.19, 765.94, 275.1, 764.37, 241.04, 686.26, 247.12, 642.13, 247.18, 669.89, 225.57, 678.54, 112.11, 677.35, 16.81, 669.43, -43.13, 684.4, -88.25, 713.2, -134.8, 745.18, -174.76, 744.33, -193.04, 101.91, -262.46, 118.59, -262.04, 12.84, 150.69, 5.06, 110.83, -4.7, 58.47, -8.1, -0.12, -9.1, -67.22, 1.65, -137.32, 35.53, -138.18, 68.17, -150.88, 84.57, -159.33, 72.6, -191.3, 70.13, -219.08, 74.32, -239.9, 91.06, -250.72, 117.18, -252.19, 160.53, -251.19, 195.7, -242.48, 217.6, -225.88, 241.97, -206.8, 96.48, -142.02, 132.45, -100.92, 182.27, -78.17, 243.34, -72.3, 295.64, -90.08, 301.64, -133.79, 286.54, -171.51, 201.88, -252.89, 228.18, -237.49, 239.55, -224.55, 231.24, -215.2, 171.39, 209.47, 349.75, 225.81, 549.1, 188.68, 566.25, 3.59, 350.31, 20.25, 161.1, -13.46, 328.45, -331.4, 387.03, -310.41, 450.83, -286.87, 486.67, -265.3, 556.43, -223.24], "hull": 77, "edges": [0, 152, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152], "width": 211, "height": 189}}, "archer13": {"archer13": {"type": "mesh", "uvs": [0.73875, 0.02041, 0.88089, 0.04828, 0.9455, 0.11615, 0.96429, 0.17354, 0.98459, 0.23553, 1, 0.28261, 1, 0.32405, 1, 0.38978, 1, 0.46409, 0.94832, 0.55323, 0.92293, 0.63536, 0.9261, 0.68519, 0.92769, 0.74556, 0.94355, 0.82066, 0.972, 0.87884, 1, 0.93609, 1, 1, 0.80859, 1, 0.6185, 0.95082, 0.46378, 0.86674, 0.34221, 0.77386, 0.28032, 0.70836, 0.20296, 0.63015, 0.14328, 0.56367, 0.07476, 0.48741, 0.05293, 0.4249, 0.03695, 0.37914, 0, 0.27331, 0, 0.22669, 0, 0.15901, 0, 0.12043, 0, 0.07114, 0.0975, 0.03042, 0.2251, 0, 0.35594, 0, 0.55784, 0], "triangles": [17, 15, 16, 15, 17, 14, 17, 18, 14, 14, 18, 13, 18, 19, 13, 13, 19, 12, 19, 20, 12, 12, 20, 11, 20, 21, 11, 21, 10, 11, 21, 22, 10, 10, 22, 9, 22, 23, 9, 23, 24, 9, 9, 24, 7, 8, 9, 7, 6, 7, 24, 5, 6, 24, 4, 24, 3, 35, 2, 3, 2, 0, 1, 35, 0, 2, 35, 3, 34, 34, 24, 25, 30, 32, 34, 32, 33, 34, 34, 3, 24, 34, 25, 26, 27, 28, 34, 24, 4, 5, 34, 26, 27, 34, 28, 29, 34, 29, 30, 30, 31, 32], "vertices": [3, 36, -65.55, 46.32, 0.96522, 37, -129.26, 76.32, 0.02478, 53, 352.7, 160.41, 0.01, 3, 36, -54.21, 72.58, 0.92817, 37, -112.03, 99.16, 0.06183, 53, 339.91, 134.82, 0.01, 3, 36, -26.09, 84.74, 0.8529, 37, -81.84, 104.33, 0.1371, 53, 311.16, 124.25, 0.01, 3, 36, -2.25, 88.42, 0.73305, 37, -57.8, 102.27, 0.25695, 53, 287.15, 121.89, 0.01, 3, 36, 23.5, 92.41, 0.53135, 37, -31.84, 100.06, 0.45865, 53, 261.22, 119.35, 0.01, 3, 36, 43.06, 95.43, 0.36228, 37, -12.12, 98.37, 0.62772, 53, 241.52, 117.42, 0.01, 3, 36, 60.29, 95.59, 0.22544, 37, 4.67, 94.46, 0.76456, 53, 224.3, 118.22, 0.01, 3, 36, 87.64, 95.86, 0.06433, 37, 31.3, 88.26, 0.92567, 53, 196.99, 119.48, 0.01, 3, 36, 118.55, 96.15, 0.00355, 37, 61.41, 81.24, 0.98645, 53, 166.11, 120.9, 0.01, 2, 37, 95.36, 63.56, 0.99, 53, 129.5, 132.11, 0.01, 2, 37, 127.58, 51.26, 0.99, 53, 95.59, 138.35, 0.01, 2, 37, 147.9, 47.12, 0.99, 53, 74.85, 138.73, 0.01, 2, 37, 172.42, 41.7, 0.99, 53, 49.76, 139.59, 0.01, 2, 37, 203.51, 37.45, 0.99, 53, 18.41, 138.12, 0.01, 2, 37, 228.27, 37.06, 0.99, 53, -6.01, 134, 0.01, 2, 37, 252.63, 36.67, 0.99, 53, -30.04, 129.96, 0.01, 2, 37, 278.53, 30.63, 0.99, 53, -56.6, 131.18, 0.01, 2, 37, 270.53, -3.67, 0.99, 53, -54.97, 166.36, 0.01, 2, 37, 242.67, -33.09, 0.99, 53, -32.92, 200.36, 0.01, 2, 37, 202.14, -52.87, 0.99, 53, 3.33, 227.19, 0.01, 3, 36, 248.57, -23.64, 0.00356, 37, 159.44, -65.89, 0.98644, 53, 42.96, 247.75, 0.01, 3, 36, 221.43, -35.29, 0.0272, 37, 130.31, -70.79, 0.9628, 53, 70.7, 257.87, 0.01, 3, 36, 189.03, -49.83, 0.12614, 37, 95.4, -77.27, 0.86386, 53, 103.86, 270.59, 0.01, 3, 36, 161.48, -61.08, 0.31216, 37, 65.97, -81.69, 0.67784, 53, 131.99, 280.28, 0.01, 3, 36, 129.88, -73.99, 0.61657, 37, 32.21, -86.76, 0.37343, 53, 164.26, 291.41, 0.01, 3, 36, 103.92, -78.26, 0.8338, 37, 5.98, -84.77, 0.1562, 53, 190.43, 294.23, 0.01, 3, 36, 84.91, -81.38, 0.94012, 37, -13.23, -83.32, 0.04988, 53, 209.58, 296.29, 0.01, 2, 36, 40.95, -88.6, 0.99, 53, 253.87, 301.05, 0.01, 2, 36, 21.56, -88.79, 0.99, 53, 273.24, 300.15, 0.01, 2, 36, -6.6, -89.05, 0.99, 53, 301.37, 298.86, 0.01, 2, 36, -22.64, -89.21, 0.99, 53, 317.4, 298.12, 0.01, 2, 36, -43.15, -89.41, 0.99, 53, 337.88, 297.17, 0.01, 2, 36, -60.26, -71.63, 0.99, 53, 353.98, 278.47, 0.01, 2, 36, -73.14, -48.27, 0.99, 53, 365.54, 254.43, 0.01, 2, 36, -73.37, -24.2, 0.99, 53, 364.43, 230.38, 0.01, 3, 36, -73.73, 12.95, 0.98832, 37, -145.08, 45.83, 0.00168, 53, 362.71, 193.27, 0.01], "hull": 36, "edges": [0, 70, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70], "width": 37, "height": 83}}, "archer15": {"archer15": {"type": "mesh", "uvs": [0.30843, 0.03725, 0.37263, 0.07833, 0.4275, 0.11344, 0.48058, 0.14741, 0.58889, 0.2013, 0.69567, 0.22824, 0.81466, 0.22054, 0.90619, 0.16665, 0.96522, 0.19572, 1, 0.21284, 1, 0.28213, 0.96632, 0.35333, 0.93279, 0.42421, 0.91077, 0.47076, 0.93232, 0.52786, 0.97179, 0.63244, 1, 0.76717, 0.96937, 0.80044, 0.9094, 0.86559, 0.84702, 0.93336, 0.78568, 1, 0.75751, 1, 0.73381, 1, 0.69047, 0.96458, 0.64823, 0.93006, 0.60047, 0.89103, 0.54312, 0.84417, 0.50855, 0.80799, 0.46258, 0.75989, 0.41651, 0.71169, 0.35549, 0.64785, 0.30015, 0.61891, 0.23837, 0.58661, 0.18616, 0.55931, 0.16079, 0.6179, 0.14167, 0.66205, 0.12394, 0.703, 0.10951, 0.73634, 0.09615, 0.76718, 0.07352, 0.76947, 0.05801, 0.77103, 0.03793, 0.70041, 0.02297, 0.64778, 0.01049, 0.60391, 0, 0.56701, 0, 0.51447, 0, 0.44768, 0.01578, 0.42412, 0.03425, 0.39655, 0.05281, 0.36885, 0.07479, 0.33604, 0.09435, 0.32493, 0.11596, 0.31267, 0.14453, 0.29645, 0.17202, 0.28083, 0.19683, 0.26675, 0.1587, 0.18591, 0.12971, 0.10507, 0.13429, 0, 0.18158, 0, 0.25023, 0, 0.27322, 0.15517, 0.36621, 0.25294, 0.46306, 0.31648, 0.58801, 0.38736, 0.70618, 0.41913, 0.78172, 0.40936, 0.85921, 0.32626, 0.72361, 0.51934, 0.74976, 0.69776, 0.37008, 0.40203, 0.82574, 0.36215, 0.84662, 0.51934, 0.87761, 0.68799], "triangles": [40, 41, 39, 38, 39, 37, 37, 39, 41, 37, 41, 36, 35, 36, 42, 35, 42, 49, 35, 49, 51, 49, 50, 51, 42, 36, 41, 49, 42, 48, 35, 51, 34, 34, 51, 52, 42, 43, 48, 47, 48, 45, 34, 52, 33, 33, 52, 53, 45, 48, 43, 43, 44, 45, 53, 54, 33, 45, 46, 47, 19, 20, 69, 69, 20, 21, 69, 21, 22, 19, 73, 18, 19, 69, 73, 69, 24, 68, 68, 64, 65, 18, 73, 17, 17, 15, 16, 17, 73, 15, 69, 72, 73, 69, 66, 72, 69, 68, 66, 73, 14, 15, 73, 13, 14, 73, 72, 13, 68, 65, 66, 66, 71, 72, 72, 67, 13, 72, 71, 67, 13, 67, 12, 67, 7, 12, 11, 7, 8, 11, 12, 7, 64, 5, 65, 65, 5, 66, 66, 6, 71, 66, 5, 6, 71, 6, 67, 11, 8, 10, 67, 6, 7, 8, 9, 10, 22, 23, 69, 23, 24, 69, 26, 64, 25, 24, 25, 68, 68, 25, 64, 26, 27, 64, 64, 63, 4, 64, 4, 5, 27, 28, 64, 28, 63, 64, 28, 29, 63, 30, 70, 29, 29, 70, 63, 30, 31, 70, 31, 32, 70, 33, 55, 32, 70, 61, 62, 70, 32, 61, 32, 55, 61, 33, 54, 55, 70, 62, 63, 4, 63, 3, 62, 2, 63, 63, 2, 3, 61, 55, 59, 61, 0, 62, 62, 1, 2, 62, 0, 1, 56, 57, 59, 55, 56, 59, 59, 57, 58, 59, 60, 61, 61, 60, 0], "vertices": [2, 50, 220.55, -66.61, 0.99, 56, -570.65, 195.79, 0.01, 2, 50, 177.79, -68.16, 0.99514, 10, 242.68, 114.58, 0.00486, 2, 50, 141.25, -69.48, 0.95665, 10, 225.44, 82.33, 0.04335, 3, 50, 105.9, -70.76, 0.82771, 10, 208.77, 51.13, 0.17181, 51, -76.29, 110.72, 0.00048, 3, 50, 34.87, -77.16, 0.17968, 10, 178.58, -13.48, 0.66582, 51, -8.53, 88.51, 0.1545, 2, 10, 155.32, -78.79, 0.32019, 51, 59.1, 73.27, 0.67981, 2, 10, 138.78, -153.88, 0.01123, 51, 135.64, 65.87, 0.98877, 1, 51, 196.01, 72.36, 1, 2, 51, 232.95, 60.34, 0.99, 55, -73.18, 160.75, 0.01, 2, 51, 254.72, 53.25, 0.99, 55, -50.71, 156.36, 0.01, 2, 51, 252.56, 35.64, 0.99, 55, -50.71, 138.62, 0.01, 2, 51, 228.74, 20.2, 0.99, 55, -72.47, 120.4, 0.01, 2, 51, 205.03, 4.83, 0.99, 55, -94.13, 102.25, 0.01, 3, 10, 61.69, -198.77, 0.00036, 51, 189.46, -5.27, 0.98964, 55, -108.36, 90.34, 0.01, 3, 10, 44.16, -208.78, 0.00327, 51, 201.5, -21.47, 0.98673, 55, -94.44, 75.72, 0.01, 3, 10, 12.05, -227.1, 0.00718, 51, 223.55, -51.15, 0.98282, 55, -68.94, 48.95, 0.01, 3, 10, -25.81, -236.51, 0.00921, 51, 237.44, -87.6, 0.98079, 55, -50.72, 14.45, 0.01, 2, 10, -29.33, -215.26, 0.01604, 51, 216.77, -93.65, 0.98396, 3, 56, -182.43, -16.27, 0.01, 10, -36.22, -173.65, 0.07427, 51, 176.28, -105.49, 0.91573, 3, 56, -222.72, -33.61, 0.01, 10, -43.39, -130.36, 0.21812, 51, 134.17, -117.8, 0.77188, 3, 56, -262.35, -50.67, 0.02, 10, -50.43, -87.8, 0.41629, 51, 92.76, -129.91, 0.56371, 3, 56, -280.55, -50.67, 0.02, 10, -46.06, -70.13, 0.50126, 51, 74.7, -127.69, 0.47874, 3, 56, -295.86, -50.67, 0.02, 10, -42.39, -55.27, 0.57992, 51, 59.51, -125.83, 0.40008, 3, 56, -323.86, -41.61, 0.01, 10, -26.86, -30.27, 0.76355, 51, 32.82, -113.42, 0.22645, 3, 56, -351.14, -32.77, 0.01, 10, -11.73, -5.9, 0.94755, 51, 6.81, -101.33, 0.04245, 3, 50, -21.88, 90.21, 0.03323, 56, -382, -22.78, 0.01, 10, 5.37, 21.65, 0.95677, 3, 50, 17.05, 89.1, 0.30038, 56, -419.04, -10.78, 0.01, 10, 25.91, 54.73, 0.68962, 3, 50, 41.08, 86.48, 0.53882, 56, -441.38, -1.52, 0.01, 10, 40.26, 74.19, 0.45118, 3, 50, 73.04, 83, 0.8035, 56, -471.08, 10.79, 0.01, 10, 59.35, 100.06, 0.1865, 3, 50, 105.07, 79.51, 0.94005, 56, -500.84, 23.13, 0.01, 10, 78.47, 125.99, 0.04995, 4, 50, 147.49, 74.89, 0.98455, 56, -540.26, 39.48, 0.01, 10, 103.8, 160.33, 0.00192, 63, -16.52, -161.83, 0.00353, 3, 50, 183.88, 77.81, 0.95094, 56, -576, 46.88, 0.01, 63, -2.6, -128.08, 0.03906, 3, 50, 224.51, 81.08, 0.79849, 56, -615.92, 55.16, 0.01, 63, 12.95, -90.4, 0.19151, 3, 50, 258.85, 83.84, 0.43246, 56, -649.64, 62.14, 0.01, 63, 26.08, -58.56, 0.55754, 3, 50, 270.36, 102.83, 0.15788, 56, -666.03, 47.15, 0.01, 63, 14.39, -39.67, 0.83212, 3, 50, 279.04, 117.15, 0.05253, 56, -678.38, 35.84, 0.01, 63, 5.58, -25.44, 0.93747, 3, 50, 287.1, 130.43, 0.00874, 56, -689.84, 25.36, 0.01, 63, -2.59, -12.23, 0.98126, 3, 50, 293.65, 141.24, 0.00035, 56, -699.16, 16.82, 0.01, 63, -9.24, -1.48, 0.98965, 2, 56, -707.79, 8.93, 0.01, 63, -15.4, 8.46, 0.99, 2, 56, -722.41, 8.34, 0.01, 63, -13.25, 22.94, 0.99, 2, 56, -732.42, 7.94, 0.01, 63, -11.79, 32.85, 0.99, 2, 56, -745.4, 26.02, 0.01, 63, 8.39, 42.24, 0.99, 2, 56, -755.06, 39.5, 0.01, 63, 23.43, 49.23, 0.99, 2, 56, -763.12, 50.73, 0.01, 63, 35.96, 55.06, 0.99, 2, 56, -769.9, 60.17, 0.01, 63, 46.5, 59.97, 0.99, 2, 56, -769.9, 73.62, 0.01, 63, 59.72, 57.47, 0.99, 2, 56, -769.9, 90.72, 0.01, 63, 76.52, 54.29, 0.99, 2, 56, -759.71, 96.75, 0.01, 63, 80.55, 43.15, 0.99, 2, 56, -747.77, 103.81, 0.01, 63, 85.27, 30.11, 0.99, 2, 56, -735.78, 110.9, 0.01, 63, 90, 17.02, 0.99, 3, 50, 343.94, 49.17, 0.0012, 56, -721.58, 119.3, 0.01, 63, 95.62, 1.5, 0.9888, 3, 50, 332.61, 42.9, 0.05097, 56, -708.95, 122.14, 0.01, 63, 96.06, -11.45, 0.93903, 3, 50, 320.1, 35.97, 0.16372, 56, -694.99, 125.28, 0.01, 63, 96.55, -25.74, 0.82628, 3, 50, 303.55, 26.8, 0.37933, 56, -676.54, 129.44, 0.01, 63, 97.2, -44.65, 0.61067, 3, 50, 287.62, 17.98, 0.62989, 56, -658.77, 133.43, 0.01, 63, 97.83, -62.84, 0.36011, 3, 50, 273.25, 10.02, 0.90366, 56, -642.74, 137.04, 0.01, 63, 98.39, -79.26, 0.08634, 2, 50, 302.71, -2.93, 0.99, 56, -667.38, 157.73, 0.01, 2, 50, 326.49, -17.54, 0.99, 56, -686.11, 178.43, 0.01, 2, 50, 331.2, -44.18, 0.99, 56, -683.15, 205.33, 0.01, 2, 50, 301.88, -52.76, 0.99, 56, -652.6, 205.33, 0.01, 2, 50, 259.31, -65.21, 0.99, 56, -608.25, 205.33, 0.01, 2, 50, 233.9, -31.25, 0.99, 56, -593.4, 165.6, 0.01, 2, 50, 169.23, -24.09, 0.99816, 10, 200.28, 129.34, 0.00184, 2, 50, 104.61, -26.04, 0.93426, 10, 169.47, 72.51, 0.06574, 3, 50, 22.04, -31.28, 0.13899, 10, 132.47, -1.49, 0.73897, 51, -14.89, 41.3, 0.12204, 2, 10, 106.25, -73.64, 0.0933, 51, 59.89, 23.94, 0.9067, 2, 10, 96.96, -121.62, 0.00788, 51, 108.64, 20.48, 0.99212, 2, 10, 105.6, -175.32, 9e-05, 51, 160.91, 35.5, 0.99991, 3, 56, -302.45, 72.37, 0.01, 10, 78.64, -78.42, 0.00179, 51, 67.95, -2.9, 0.98821, 3, 56, -285.55, 26.7, 0.02, 10, 30.25, -83.85, 0.22875, 51, 79.15, -50.29, 0.75125, 3, 50, 156.11, 11.84, 0.98987, 56, -530.83, 102.41, 0.01, 63, 43.56, -182.79, 0.00013, 2, 10, 101.87, -152.12, 0.00201, 51, 138.33, 29.01, 0.99799, 3, 56, -222.98, 72.37, 0.01, 10, 59.57, -155.56, 0.00565, 51, 146.82, -12.57, 0.98435, 3, 56, -202.96, 29.2, 0.01, 10, 12.85, -164.63, 0.04909, 51, 161.44, -57.86, 0.94091], "hull": 61, "edges": [0, 120, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120], "width": 129, "height": 51}, "archer16": {"type": "mesh", "path": "archer15", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-89.39, -218.05, 65.7, 409.06, 314.22, 347.6, 159.13, -279.51], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 129, "height": 51}}, "archer16": {"archer16": {"type": "mesh", "uvs": [0.87694, 0.05484, 1, 0.14761, 1, 0.22646, 0.93059, 0.33777, 0.87694, 0.39807, 0.94226, 0.48619, 0.94925, 0.66476, 0.85828, 0.75289, 0.82096, 0.86652, 0.72998, 0.95929, 0.60635, 1, 0.46873, 1, 0.34277, 1, 0.13749, 0.95233, 0, 0.75057, 0, 0.63693, 0.05482, 0.48899, 0.07218, 0.44213, 0.06285, 0.32617, 0, 0.20094, 0, 0.14529, 0.08151, 0.06644, 0.28678, 0, 0.41041, 0, 0.54571, 0, 0.67867, 0, 0.20281, 0.39807, 0.40575, 0.36096, 0.62268, 0.35632, 0.77197, 0.36328], "triangles": [26, 14, 15, 5, 7, 29, 5, 29, 4, 7, 5, 6, 29, 7, 28, 26, 16, 17, 16, 26, 15, 13, 14, 26, 27, 28, 11, 8, 9, 7, 12, 26, 27, 13, 26, 12, 28, 7, 11, 11, 12, 27, 11, 7, 10, 9, 10, 7, 19, 20, 21, 0, 1, 2, 18, 19, 21, 3, 0, 2, 28, 24, 25, 29, 28, 25, 0, 29, 25, 27, 22, 23, 27, 23, 24, 28, 27, 24, 3, 29, 0, 26, 21, 22, 26, 22, 27, 18, 21, 26, 4, 29, 3, 17, 18, 26], "vertices": [3, 13, 5.81, 56.63, 0.93269, 14, -148.46, 91.84, 0.05731, 56, -643.53, -127.68, 0.01, 3, 13, 7.82, 109.41, 0.86472, 14, -134.46, 142.76, 0.12528, 56, -601.44, -159.6, 0.01, 3, 13, 30.04, 124.96, 0.82696, 14, -109.28, 152.84, 0.16304, 56, -601.44, -186.72, 0.01, 3, 13, 75.03, 127.48, 0.65921, 14, -64.91, 145.02, 0.33079, 56, -625.18, -225.01, 0.01, 3, 13, 102.54, 124.34, 0.4189, 14, -38.84, 135.69, 0.5711, 56, -643.53, -245.75, 0.01, 3, 13, 114.57, 160.02, 0.18083, 14, -18.99, 167.69, 0.80917, 56, -621.19, -276.07, 0.01, 3, 13, 163.52, 197.21, 0.05531, 14, 37.16, 192.72, 0.93469, 56, -618.8, -337.5, 0.01, 3, 13, 206.19, 189.11, 0.01617, 14, 76.86, 175.09, 0.97383, 56, -649.91, -367.81, 0.01, 3, 13, 245.54, 201.07, 0.00054, 14, 117.89, 177.76, 0.98946, 56, -662.67, -406.9, 0.01, 2, 14, 159.08, 160.72, 0.99, 56, -693.79, -438.81, 0.01, 2, 14, 187.78, 126.67, 0.99, 56, -736.07, -452.82, 0.01, 2, 14, 205.27, 82.97, 0.99, 56, -783.14, -452.82, 0.01, 2, 14, 221.26, 42.97, 0.99, 56, -826.21, -452.82, 0.01, 3, 13, 403.76, 26.5, 2e-05, 14, 232.11, -28.3, 0.98998, 56, -896.42, -436.42, 0.01, 3, 13, 373.87, -51.82, 0.02815, 14, 185.13, -97.74, 0.96185, 56, -943.44, -367.01, 0.01, 3, 13, 341.85, -74.24, 0.07348, 14, 148.84, -112.26, 0.91652, 56, -943.44, -327.92, 0.01, 3, 13, 289.4, -88.07, 0.26441, 14, 94.62, -113.75, 0.72559, 56, -924.69, -277.03, 0.01, 3, 13, 272.79, -92.45, 0.39324, 14, 77.45, -114.23, 0.59676, 56, -918.76, -260.91, 0.01, 3, 13, 241.94, -117.93, 0.73008, 14, 41.6, -132, 0.25992, 56, -921.95, -221.02, 0.01, 3, 13, 218.98, -160.25, 0.92797, 14, 9.59, -167.96, 0.06203, 56, -943.44, -177.94, 0.01, 3, 13, 203.29, -171.23, 0.9482, 14, -8.19, -175.07, 0.0418, 56, -943.44, -158.8, 0.01, 3, 13, 165.09, -163.94, 0.97648, 14, -43.73, -159.26, 0.01352, 56, -915.56, -131.67, 0.01, 2, 13, 106.1, -119.54, 0.99, 56, -845.36, -108.82, 0.01, 2, 13, 81.86, -84.9, 0.99, 56, -803.08, -108.82, 0.01, 2, 13, 55.32, -46.99, 0.99, 56, -756.81, -108.82, 0.01, 2, 13, 29.25, -9.74, 0.99, 56, -711.34, -108.82, 0.01, 3, 13, 234.76, -64.54, 0.49971, 14, 46.79, -78.38, 0.49029, 56, -874.08, -245.75, 0.01, 3, 13, 184.5, -15, 0.50788, 14, 9.16, -18.68, 0.48212, 56, -804.67, -232.99, 0.01, 3, 13, 140.64, 44.87, 0.4038, 14, -19.88, 49.62, 0.5862, 56, -730.48, -231.39, 0.01, 3, 13, 113.32, 88.07, 0.47269, 14, -36.62, 97.91, 0.51731, 56, -679.43, -233.79, 0.01], "hull": 26, "edges": [0, 50, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50], "width": 68, "height": 69}}, "archer17": {"archer17": {"type": "mesh", "uvs": [1, 0.0569, 1, 0.1568, 1, 0.24004, 0.96729, 0.3101, 0.94004, 0.36847, 0.87778, 0.42146, 0.81988, 0.47074, 0.78139, 0.52719, 0.72744, 0.60631, 0.70962, 0.6768, 0.68354, 0.77993, 0.65284, 0.82628, 0.61353, 0.88561, 0.57955, 0.9369, 0.49405, 1, 0.40624, 1, 0.30687, 1, 0.21906, 1, 0.15065, 0.98101, 0.06886, 0.95831, 0, 0.89885, 0, 0.77279, 0.03117, 0.69258, 0.06655, 0.60155, 0.11311, 0.54477, 0.17797, 0.46568, 0.23443, 0.39683, 0.27915, 0.34231, 0.3317, 0.28479, 0.38316, 0.22846, 0.42473, 0.18296, 0.4796, 0.13958, 0.54921, 0.08454, 0.58418, 0.0569, 0.69278, 0, 0.86609, 0, 1, 0], "triangles": [8, 9, 26, 26, 9, 25, 10, 11, 9, 11, 25, 9, 12, 25, 11, 24, 25, 12, 19, 20, 21, 13, 24, 12, 22, 24, 13, 24, 22, 23, 22, 18, 21, 18, 19, 21, 22, 17, 18, 22, 16, 17, 22, 15, 16, 13, 15, 22, 14, 15, 13, 35, 36, 0, 35, 0, 1, 3, 35, 1, 2, 3, 1, 35, 3, 34, 4, 5, 3, 34, 5, 33, 3, 5, 34, 5, 32, 33, 6, 32, 5, 31, 32, 6, 7, 31, 6, 7, 30, 31, 7, 29, 30, 8, 29, 7, 28, 29, 8, 8, 27, 28, 8, 26, 27], "vertices": [2, 12, -47.23, 24.97, 0.99, 56, -516.68, 75.3, 0.01, 2, 12, -21.07, 46.94, 0.99, 56, -516.68, 41.14, 0.01, 2, 12, 0.73, 65.25, 0.99, 56, -516.68, 12.67, 0.01, 3, 12, 26.48, 71.85, 0.98961, 56, -528.19, -11.29, 0.01, 13, -155.68, 84.37, 0.00039, 3, 12, 47.93, 77.35, 0.98506, 56, -537.79, -31.26, 0.01, 13, -133.82, 87.96, 0.00494, 3, 12, 75.91, 72.22, 0.95856, 56, -559.7, -49.38, 0.01, 13, -106.41, 80.4, 0.03144, 3, 12, 101.92, 67.45, 0.86937, 56, -580.08, -66.23, 0.01, 13, -80.91, 73.36, 0.12063, 3, 12, 125.42, 69.5, 0.68652, 56, -593.63, -85.54, 0.01, 13, -57.33, 73.34, 0.30348, 3, 12, 158.35, 72.36, 0.28943, 56, -612.62, -112.6, 0.01, 13, -24.27, 73.3, 0.70057, 3, 12, 180.84, 83.06, 0.06658, 56, -618.89, -136.7, 0.01, 13, -0.92, 81.98, 0.92342, 3, 12, 213.76, 98.72, 0.00036, 56, -628.07, -171.97, 0.01, 13, 33.24, 94.69, 0.98964, 2, 56, -638.88, -187.83, 0.01, 13, 52.42, 94.92, 0.99, 2, 56, -652.72, -208.12, 0.01, 13, 76.98, 95.23, 0.99, 2, 56, -664.68, -225.66, 0.01, 13, 98.21, 95.49, 0.99, 2, 56, -694.77, -247.24, 0.01, 13, 133.15, 83.21, 0.99, 2, 56, -725.68, -247.24, 0.01, 13, 150.87, 57.89, 0.99, 2, 56, -760.66, -247.24, 0.01, 13, 170.93, 29.23, 0.99, 2, 56, -791.57, -247.24, 0.01, 13, 188.65, 3.91, 0.99, 2, 56, -815.65, -240.74, 0.01, 13, 197.14, -19.54, 0.99, 2, 56, -844.44, -232.98, 0.01, 13, 207.29, -47.58, 0.99, 3, 12, 399.65, -59.35, 0.00149, 56, -868.68, -212.65, 0.01, 13, 204.53, -79.1, 0.98851, 3, 12, 366.64, -87.08, 0.01704, 56, -868.68, -169.53, 0.01, 13, 169.22, -103.82, 0.97296, 3, 12, 338.58, -96.32, 0.04805, 56, -857.71, -142.1, 0.01, 13, 140.45, -110.56, 0.94195, 3, 12, 306.73, -106.81, 0.11503, 56, -845.25, -110.97, 0.01, 13, 107.8, -118.22, 0.87497, 3, 12, 281.32, -106.75, 0.1977, 56, -828.86, -91.55, 0.01, 13, 82.5, -115.92, 0.7923, 3, 12, 245.93, -106.67, 0.39455, 56, -806.03, -64.5, 0.01, 13, 47.24, -112.73, 0.59545, 3, 12, 215.11, -106.6, 0.61986, 56, -786.16, -40.96, 0.01, 13, 16.56, -109.95, 0.37014, 3, 12, 190.71, -106.54, 0.78751, 56, -770.42, -22.31, 0.01, 13, -7.74, -107.75, 0.20249, 3, 12, 163.75, -105.03, 0.91653, 56, -751.92, -2.64, 0.01, 13, -34.47, -103.88, 0.07347, 3, 12, 137.35, -103.55, 0.97604, 56, -733.81, 16.63, 0.01, 13, -60.64, -100.09, 0.01396, 3, 12, 116.02, -102.35, 0.98894, 56, -719.17, 32.19, 0.01, 13, -81.78, -97.03, 0.00106, 2, 12, 92.24, -97.11, 0.99, 56, -699.86, 47.03, 0.01, 2, 12, 62.07, -90.45, 0.99, 56, -675.36, 65.85, 0.01, 2, 12, 46.91, -87.11, 0.99, 56, -663.05, 75.3, 0.01, 2, 12, 7.42, -70.35, 0.99, 56, -624.82, 94.76, 0.01, 2, 12, -31.82, -23.64, 0.99, 56, -563.81, 94.76, 0.01, 2, 12, -62.13, 12.45, 0.99, 56, -516.68, 94.76, 0.01], "hull": 37, "edges": [0, 72, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72], "width": 70, "height": 68}}, "archer18": {"archer18": {"type": "mesh", "uvs": [0.44908, 0.00636, 0.49153, 0.01457, 0.56112, 0.02803, 0.6543, 0.03706, 0.75124, 0.02932, 0.78136, 0.09508, 0.81336, 0.20595, 0.85053, 0.33857, 0.88453, 0.46037, 0.91069, 0.49262, 0.93946, 0.62338, 0.94462, 0.66465, 0.9473, 0.68608, 1, 0.81863, 1, 0.89387, 0.92769, 0.95656, 0.81915, 1, 0.77007, 1, 0.73415, 1, 0.59684, 1, 0.45953, 1, 0.32121, 0.9661, 0.20191, 0.93686, 0.0916, 0.88499, 0, 0.84192, 0, 0.77385, 0.06983, 0.65204, 0.06722, 0.61801, 0.09599, 0.51053, 0.11691, 0.48724, 0.13914, 0.38156, 0.15197, 0.34165, 0.18753, 0.23109, 0.25684, 0.09674, 0.32876, 0, 0.41617, 0, 0.43123, 0.13762, 0.40958, 0.23045, 0.37758, 0.30136, 0.32017, 0.35293, 0.27311, 0.37098, 0.19593, 0.36583, 0.25334, 0.51151, 0.29852, 0.51796, 0.42088, 0.52956, 0.43876, 0.50636, 0.52912, 0.50765, 0.5423, 0.53085, 0.59124, 0.52441, 0.64489, 0.43287, 0.70889, 0.41353, 0.7936, 0.42255, 0.85195, 0.48186, 0.57618, 0.59532, 0.60159, 0.72424, 0.66277, 0.81191, 0.76066, 0.82352, 0.82842, 0.80418, 0.88207, 0.72038, 0.88866, 0.61852, 0.63888, 0.77768, 0.54888, 0.76163, 0.5263, 0.78226, 0.43312, 0.76292, 0.41241, 0.73971, 0.27123, 0.71393, 0.22228, 0.69588, 0.70418, 0.31812, 0.6976, 0.24205, 0.28911, 0.45221, 0.2524, 0.8132, 0.77289, 0.90474], "triangles": [36, 68, 49, 2, 36, 1, 1, 36, 0, 8, 52, 7, 52, 51, 7, 49, 68, 67, 68, 2, 3, 2, 68, 36, 7, 51, 67, 51, 50, 67, 67, 6, 7, 67, 68, 6, 68, 5, 6, 5, 3, 4, 5, 68, 3, 36, 34, 35, 34, 36, 33, 36, 35, 0, 52, 50, 51, 49, 50, 48, 54, 48, 50, 46, 47, 44, 46, 44, 45, 64, 44, 47, 64, 43, 44, 66, 42, 65, 65, 42, 43, 66, 27, 28, 28, 29, 66, 66, 29, 42, 53, 47, 48, 47, 46, 48, 39, 45, 44, 48, 46, 49, 42, 69, 43, 44, 43, 69, 42, 29, 41, 41, 29, 30, 30, 31, 41, 69, 41, 40, 69, 42, 41, 49, 46, 37, 44, 69, 39, 39, 38, 45, 45, 38, 46, 38, 37, 46, 49, 37, 36, 69, 40, 39, 49, 67, 50, 39, 40, 32, 32, 40, 41, 41, 31, 32, 38, 39, 33, 39, 32, 33, 38, 33, 37, 37, 33, 36, 57, 15, 16, 16, 17, 71, 17, 18, 71, 19, 55, 18, 71, 55, 56, 71, 18, 55, 20, 62, 19, 19, 62, 60, 19, 60, 55, 60, 62, 61, 21, 63, 20, 20, 63, 62, 22, 70, 21, 21, 70, 64, 21, 64, 63, 64, 70, 65, 16, 71, 57, 15, 57, 13, 14, 15, 13, 12, 58, 11, 57, 58, 13, 22, 23, 70, 71, 56, 57, 13, 58, 12, 23, 25, 26, 23, 66, 70, 23, 26, 66, 23, 24, 25, 59, 57, 56, 70, 66, 65, 56, 55, 60, 58, 57, 59, 60, 59, 56, 62, 63, 61, 61, 54, 60, 50, 59, 60, 63, 47, 61, 47, 63, 64, 61, 53, 54, 61, 47, 53, 65, 43, 64, 53, 48, 54, 50, 60, 54, 11, 59, 10, 11, 58, 59, 50, 52, 59, 66, 26, 27, 59, 9, 10, 59, 52, 9, 9, 52, 8], "vertices": [3, 2, -99.47, 294.82, 0.00058, 9, 222.92, 88.78, 0.22438, 10, 140.17, 65.39, 0.77504, 2, 9, 218.7, 62.28, 0.12699, 10, 130.15, 40.49, 0.87301, 2, 9, 211.8, 18.83, 0.00209, 10, 113.72, -0.32, 0.99791, 1, 10, 95.71, -55.96, 1, 1, 10, 84.57, -115.72, 1, 3, 2, 108.54, 254.28, 0.00277, 9, 178.77, -118.49, 0.00573, 10, 50.87, -126.81, 0.9915, 3, 2, 128.57, 203.61, 0.03587, 9, 127.75, -137.64, 0.08766, 10, -3.12, -134.09, 0.87647, 3, 2, 151.84, 143.01, 0.18259, 9, 66.76, -159.85, 0.25893, 10, -67.54, -142.13, 0.55848, 3, 2, 173.12, 87.34, 0.45442, 9, 10.73, -180.16, 0.26731, 10, -126.69, -149.42, 0.27827, 3, 2, 189.5, 72.61, 0.54616, 9, -4.29, -196.27, 0.23675, 10, -144.92, -161.78, 0.21709, 3, 2, 207.51, 12.85, 0.76378, 9, -64.35, -213.24, 0.13448, 10, -207.26, -164.91, 0.10174, 3, 2, 210.74, -6.01, 0.82319, 9, -83.27, -216.15, 0.10166, 10, -226.34, -163.53, 0.07515, 3, 2, 212.42, -15.8, 0.85474, 9, -93.09, -217.65, 0.08374, 10, -236.25, -162.8, 0.06152, 3, 2, 245.41, -76.38, 0.96532, 9, -154.23, -249.59, 0.01872, 10, -302.98, -180.28, 0.01596, 3, 2, 245.41, -110.76, 0.97827, 9, -188.61, -248.99, 0.01101, 10, -336.36, -172.03, 0.01072, 4, 2, 200.14, -139.42, 0.97973, 9, -216.47, -203.23, 0.00449, 10, -353.3, -121.21, 0.00578, 56, -157.21, -336.67, 0.01, 3, 2, 132.19, -159.27, 0.96928, 10, -356.26, -50.48, 0.00071, 56, -225.15, -356.52, 0.03, 3, 2, 101.47, -159.27, 0.96994, 10, -348.88, -20.66, 6e-05, 56, -255.88, -356.52, 0.03, 2, 2, 78.98, -159.27, 0.97, 56, -278.36, -356.52, 0.03, 3, 2, -6.97, -159.27, 0.97728, 9, -232.71, 4.2, 0.00272, 56, -364.32, -356.52, 0.02, 3, 2, -92.93, -159.27, 0.90293, 9, -231.22, 90.14, 0.07707, 56, -450.27, -356.52, 0.02, 3, 2, -179.51, -143.77, 0.75155, 9, -214.22, 176.44, 0.23845, 56, -536.86, -341.02, 0.01, 3, 2, -254.19, -130.41, 0.63422, 9, -199.56, 250.88, 0.35578, 56, -611.54, -327.66, 0.01, 2, 2, -323.25, -106.71, 0.574, 9, -174.66, 319.52, 0.426, 2, 2, -380.59, -87.02, 0.55547, 9, -153.98, 376.5, 0.44453, 3, 2, -380.59, -55.92, 0.55141, 9, -122.88, 375.96, 0.44842, 10, -132.82, 422.49, 0.00017, 3, 2, -336.88, -0.25, 0.50115, 9, -67.98, 331.29, 0.49218, 10, -89.28, 366.69, 0.00668, 3, 2, -338.51, 15.3, 0.47911, 9, -52.4, 332.65, 0.51048, 10, -73.79, 364.55, 0.0104, 3, 2, -320.5, 64.42, 0.40738, 9, -3.61, 313.79, 0.56456, 10, -30.43, 335.27, 0.02806, 3, 2, -307.4, 75.06, 0.37638, 9, 6.81, 300.51, 0.58477, 10, -23.25, 320, 0.03885, 3, 2, -293.49, 123.36, 0.26626, 9, 54.85, 285.76, 0.64135, 10, 20.3, 294.9, 0.09238, 3, 2, -285.46, 141.6, 0.22835, 9, 72.95, 277.41, 0.65226, 10, 36.07, 282.72, 0.1194, 3, 2, -263.2, 192.13, 0.13392, 9, 123.08, 254.28, 0.6451, 10, 79.78, 248.98, 0.22097, 3, 2, -219.81, 253.52, 0.0455, 9, 183.71, 209.83, 0.53728, 10, 128.97, 192.13, 0.41722, 3, 2, -174.79, 297.73, 0.01208, 9, 227.14, 164.04, 0.4117, 10, 161.07, 137.81, 0.57622, 3, 2, -120.07, 297.73, 0.00232, 9, 226.18, 109.33, 0.28931, 10, 147.94, 84.68, 0.70837, 4, 2, -110.64, 234.84, 0.00788, 9, 163.14, 101, 0.37313, 10, 84.62, 90.63, 0.60899, 56, -467.99, 37.59, 0.01, 4, 2, -124.19, 192.42, 0.03173, 9, 120.96, 115.28, 0.58987, 10, 46.69, 113.97, 0.3684, 56, -481.54, -4.83, 0.01, 4, 2, -144.22, 160.01, 0.07922, 9, 88.9, 135.88, 0.69558, 10, 20.04, 141.2, 0.21519, 56, -501.57, -37.24, 0.01, 4, 2, -180.17, 136.44, 0.15176, 9, 65.96, 172.22, 0.69223, 10, 5.79, 181.75, 0.14601, 56, -537.51, -60.81, 0.01, 4, 2, -209.63, 128.19, 0.19326, 9, 58.23, 201.82, 0.67034, 10, 4.86, 212.33, 0.1264, 56, -566.97, -69.05, 0.01, 3, 2, -257.94, 130.55, 0.22793, 9, 61.43, 250.09, 0.65471, 10, 18.75, 258.66, 0.11736, 4, 2, -222, 63.97, 0.34602, 9, -5.77, 215.31, 0.60308, 10, -54.52, 239.76, 0.0409, 56, -579.35, -133.28, 0.01, 4, 2, -193.72, 61.03, 0.34331, 9, -9.21, 187.08, 0.61164, 10, -64.17, 213.01, 0.03505, 56, -551.07, -136.22, 0.01, 4, 2, -117.12, 55.72, 0.32645, 9, -15.84, 110.59, 0.64403, 10, -87.7, 139.93, 0.00952, 56, -474.47, -141.53, 0.02, 4, 2, -105.93, 66.33, 0.26958, 9, -5.43, 99.21, 0.70057, 10, -80.09, 126.51, 0.00985, 56, -463.27, -130.92, 0.02, 3, 2, -49.36, 65.74, 0.18092, 9, -7, 42.67, 0.79908, 56, -406.71, -131.51, 0.02, 3, 2, -41.11, 55.13, 0.24656, 9, -17.75, 34.61, 0.73344, 56, -398.46, -142.12, 0.02, 4, 2, -10.48, 58.08, 0.16295, 9, -15.34, 3.92, 0.80683, 10, -111.02, 35.83, 0.00021, 56, -367.82, -139.17, 0.03, 4, 2, 23.11, 99.91, 0.0379, 9, 25.9, -30.38, 0.83871, 10, -78.47, -6.81, 0.09339, 56, -334.24, -97.34, 0.03, 4, 2, 63.17, 108.75, 0.12, 9, 34.04, -70.6, 0.54177, 10, -79.51, -47.83, 0.30823, 56, -294.17, -88.5, 0.03, 4, 2, 116.2, 104.63, 0.25614, 9, 29, -123.55, 0.33949, 10, -96.24, -98.31, 0.38438, 56, -241.15, -92.62, 0.02, 4, 2, 152.73, 77.52, 0.44638, 9, 1.26, -159.6, 0.27462, 10, -131.33, -127.27, 0.269, 56, -204.62, -119.73, 0.01, 3, 2, -19.9, 25.67, 0.60767, 9, -47.58, 13.91, 0.36233, 56, -377.25, -171.58, 0.03, 4, 2, -4, -33.25, 0.96944, 9, -106.76, -0.97, 0.00051, 10, -201.23, 51.47, 5e-05, 56, -361.34, -230.5, 0.03, 3, 2, 34.3, -73.31, 0.96858, 10, -249.32, 23.91, 0.00142, 56, -323.05, -270.56, 0.03, 4, 2, 95.58, -78.61, 0.9491, 9, -153.86, -99.74, 0.01176, 10, -269.18, -34.3, 0.00913, 56, -261.77, -275.86, 0.03, 4, 2, 138, -69.78, 0.93268, 9, -145.76, -142.31, 0.02783, 10, -270.78, -77.61, 0.01949, 56, -219.35, -267.03, 0.02, 4, 2, 171.59, -31.48, 0.86519, 9, -108.05, -176.56, 0.07373, 10, -241.67, -119.4, 0.05108, 56, -185.76, -228.73, 0.01, 4, 2, 175.71, 15.07, 0.72552, 9, -61.58, -181.49, 0.15189, 10, -197.47, -134.58, 0.11259, 56, -181.64, -182.18, 0.01, 3, 2, 19.35, -57.67, 0.96902, 10, -230.54, 34.67, 0.00098, 56, -338, -254.92, 0.03, 3, 2, -36.99, -50.33, 0.94137, 9, -123.27, 32.32, 0.03863, 56, -394.34, -247.58, 0.02, 3, 2, -51.13, -59.76, 0.92113, 9, -132.45, 46.62, 0.05887, 56, -408.48, -257.01, 0.02, 3, 2, -109.46, -50.92, 0.76667, 9, -122.6, 104.79, 0.21333, 56, -466.81, -248.17, 0.02, 3, 2, -122.42, -40.32, 0.71258, 9, -111.77, 117.56, 0.26742, 56, -479.77, -237.57, 0.02, 4, 2, -210.8, -28.53, 0.56671, 9, -98.45, 205.73, 0.42064, 10, -147, 251.1, 0.00265, 56, -568.15, -225.78, 0.01, 4, 2, -241.44, -20.28, 0.5305, 9, -89.67, 236.22, 0.45475, 10, -131.64, 278.86, 0.00475, 56, -598.79, -217.53, 0.01, 4, 2, 60.23, 152.35, 0.03681, 9, 77.69, -68.41, 0.30467, 10, -36.48, -55.43, 0.63852, 56, -297.12, -44.9, 0.02, 4, 2, 56.1, 187.11, 0.01066, 9, 112.52, -64.89, 0.07358, 10, -1.74, -59.78, 0.89576, 56, -301.24, -10.14, 0.02, 4, 2, -199.61, 91.07, 0.26772, 9, 20.94, 192.45, 0.65511, 10, -33.58, 211.51, 0.06717, 56, -556.96, -106.17, 0.01, 4, 2, -222.59, -73.9, 0.62899, 9, -143.61, 218.3, 0.3608, 10, -188.21, 273.43, 0.00021, 56, -579.94, -271.15, 0.01, 4, 2, 103.24, -115.73, 0.96561, 9, -191.1, -106.75, 0.00189, 10, -307.05, -32.83, 0.0025, 56, -254.11, -312.98, 0.03], "hull": 36, "edges": [0, 70, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70], "width": 125, "height": 91}, "archer18_1": {"type": "mesh", "uvs": [0.47561, 0.01787, 0.54112, 0.03175, 0.57737, 0.03644, 0.61487, 0.04129, 0.68672, 0.04042, 0.72822, 0.02974, 0.76458, 0.05061, 0.79653, 0.13206, 0.81913, 0.22563, 0.83977, 0.3017, 0.8609, 0.3818, 0.88595, 0.474, 0.90642, 0.48118, 0.94231, 0.63752, 0.93865, 0.67618, 0.97456, 0.75851, 1, 0.8308, 1, 0.88599, 0.95727, 0.92552, 0.88643, 0.96916, 0.83323, 0.986, 0.78898, 1, 0.6317, 1, 0.51899, 1, 0.37239, 0.9807, 0.23313, 0.94857, 0.10412, 0.89737, 0, 0.84214, 0, 0.77889, 0.02423, 0.73772, 0.06967, 0.65138, 0.06528, 0.61523, 0.09533, 0.5078, 0.12419, 0.48353, 0.13854, 0.41013, 0.15194, 0.34155, 0.18026, 0.2438, 0.22614, 0.14837, 0.27995, 0.06613, 0.33149, 0, 0.39901, 0, 0.62905, 0.76092, 0.61638, 0.5233, 0.60079, 0.27634, 0.60879, 0.40307, 0.6231, 0.64938, 0.63045, 0.88786, 0.73819, 0.24698, 0.76451, 0.37646, 0.77328, 0.48726, 0.79569, 0.64077, 0.80933, 0.76759, 0.82687, 0.88373, 0.2597, 0.82899, 0.28114, 0.70618, 0.31525, 0.51395, 0.33376, 0.37646, 0.36105, 0.25098], "triangles": [20, 21, 52, 23, 46, 22, 52, 21, 46, 21, 22, 46, 46, 23, 41, 20, 52, 19, 25, 53, 24, 53, 54, 24, 24, 54, 41, 23, 24, 41, 45, 54, 55, 45, 55, 42, 45, 41, 54, 19, 52, 18, 25, 26, 53, 17, 18, 16, 27, 29, 26, 29, 30, 26, 26, 30, 53, 46, 51, 52, 46, 41, 51, 16, 18, 15, 18, 52, 15, 15, 51, 14, 15, 52, 51, 27, 28, 29, 53, 30, 54, 14, 51, 13, 11, 13, 50, 41, 50, 51, 50, 13, 51, 41, 45, 50, 11, 12, 13, 45, 49, 50, 45, 42, 49, 50, 49, 11, 49, 10, 11, 43, 1, 2, 1, 43, 0, 43, 57, 0, 57, 40, 0, 49, 48, 10, 44, 47, 48, 48, 9, 10, 9, 47, 8, 9, 48, 47, 47, 43, 4, 43, 3, 4, 43, 2, 3, 40, 38, 39, 47, 4, 7, 47, 7, 8, 6, 4, 5, 4, 6, 7, 44, 42, 56, 42, 55, 56, 44, 57, 43, 44, 56, 57, 33, 54, 30, 32, 33, 31, 54, 33, 55, 33, 30, 31, 42, 44, 49, 33, 34, 55, 55, 34, 56, 56, 34, 35, 44, 48, 49, 44, 43, 47, 35, 36, 56, 56, 36, 57, 57, 36, 37, 37, 38, 57, 57, 38, 40], "vertices": [2, 9, 214.27, 71.38, 0.17729, 10, 127.86, 50.35, 0.82271, 2, 9, 207.22, 30.48, 0.02648, 10, 111.85, 12.06, 0.97352, 1, 10, 104.33, -9.46, 1, 2, 9, 202.06, -15.6, 6e-05, 10, 96.54, -31.71, 0.99994, 2, 9, 201.67, -60.58, 1e-05, 10, 86.13, -75.47, 0.99999, 1, 10, 84.63, -101.86, 1, 2, 10, 69.91, -121.67, 0.99916, 2, 98.98, 271.52, 0.00084, 3, 9, 158.61, -128.58, 0.00783, 10, 28.97, -132.14, 0.98009, 2, 118.98, 234.3, 0.01208, 3, 9, 115.6, -141.98, 0.07096, 10, -15.94, -135.61, 0.87025, 2, 133.13, 191.54, 0.05878, 3, 9, 80.62, -154.3, 0.15588, 10, -52.79, -139.81, 0.69874, 2, 146.05, 156.78, 0.14538, 3, 9, 43.79, -166.89, 0.21123, 10, -91.5, -143.86, 0.49732, 2, 159.28, 120.17, 0.29146, 3, 9, 1.39, -181.83, 0.1872, 10, -136.16, -148.97, 0.28991, 2, 174.96, 78.04, 0.52289, 3, 9, -2.12, -194.59, 0.17297, 10, -142.43, -160.62, 0.25796, 2, 187.77, 74.75, 0.56908, 3, 9, -73.94, -215.81, 0.08143, 10, -217.18, -165.28, 0.11788, 2, 210.24, 3.3, 0.8007, 3, 9, -91.57, -213.21, 0.05842, 10, -233.78, -158.81, 0.08997, 2, 207.95, -14.36, 0.85161, 3, 9, -129.58, -235.04, 0.01714, 10, -275.7, -171.6, 0.04252, 2, 230.43, -51.99, 0.94034, 3, 9, -162.89, -250.38, 0.00473, 10, -311.59, -179.13, 0.02756, 2, 246.36, -85.02, 0.96771, 3, 9, -188.1, -249.94, 0.00196, 10, -336.08, -173.08, 0.02371, 2, 246.36, -110.25, 0.97433, 3, 9, -205.7, -222.88, 0.00063, 10, -347.2, -142.77, 0.02004, 2, 219.61, -128.31, 0.97933, 3, 10, -355.91, -94.93, 0.01235, 2, 175.26, -148.26, 0.97765, 56, -182.09, -345.51, 0.01, 3, 10, -355.38, -60.76, 0.0072, 2, 141.96, -155.95, 0.9828, 56, -215.39, -353.2, 0.01, 3, 10, -354.94, -32.33, 0.00361, 2, 114.26, -162.35, 0.97639, 56, -243.09, -359.6, 0.02, 3, 9, -236.19, -18.52, 0.00078, 2, 15.8, -162.35, 0.96922, 56, -341.55, -359.6, 0.03, 3, 9, -234.96, 52.03, 0.03974, 2, -54.76, -162.35, 0.93026, 56, -412.11, -359.6, 0.03, 3, 9, -224.55, 143.63, 0.19526, 2, -146.52, -153.53, 0.78474, 56, -503.87, -350.78, 0.02, 3, 9, -208.35, 230.54, 0.35076, 2, -233.71, -138.85, 0.63924, 56, -591.05, -336.1, 0.01, 2, 9, -183.55, 310.88, 0.44259, 2, -314.46, -115.45, 0.55741, 2, 9, -157.19, 375.61, 0.46717, 2, -379.64, -90.21, 0.53283, 3, 9, -128.28, 375.11, 0.47071, 10, -138.28, 422.87, 0.00021, 2, -379.64, -61.3, 0.52909, 3, 9, -109.74, 359.62, 0.47691, 10, -123.66, 403.63, 0.00094, 2, -364.48, -42.49, 0.52216, 3, 9, -70.78, 330.49, 0.51496, 10, -92.18, 366.54, 0.00751, 2, -336.03, -3.03, 0.47754, 3, 9, -54.21, 332.95, 0.53321, 10, -75.49, 365.24, 0.0115, 2, -338.78, 13.49, 0.45529, 3, 9, -5.45, 313.29, 0.58124, 10, -32.34, 335.19, 0.02833, 2, -319.97, 62.58, 0.39043, 3, 9, 5.32, 295.03, 0.60457, 10, -25.91, 314.99, 0.04201, 2, -301.9, 73.68, 0.35342, 3, 9, 38.71, 285.47, 0.64827, 10, 4.5, 298.22, 0.08249, 2, -292.92, 107.22, 0.26923, 3, 9, 69.9, 276.53, 0.66801, 10, 32.9, 282.55, 0.12694, 2, -284.53, 138.56, 0.20505, 3, 9, 114.25, 258.03, 0.66203, 10, 72.01, 254.61, 0.20986, 2, -266.8, 183.23, 0.1281, 3, 9, 157.36, 228.56, 0.60583, 10, 107.46, 216.27, 0.329, 2, -238.08, 226.85, 0.06517, 3, 9, 194.35, 194.23, 0.5061, 10, 135.85, 174.54, 0.46929, 2, -204.4, 264.43, 0.02461, 3, 9, 224.01, 161.44, 0.41924, 10, 157.44, 135.97, 0.57301, 2, -172.13, 294.65, 0.00775, 3, 9, 223.27, 119.18, 0.33521, 10, 147.3, 94.94, 0.66299, 2, -129.86, 294.65, 0.0018, 3, 10, -224.84, 38.63, 0.00157, 2, 14.14, -53.09, 0.96843, 56, -343.21, -250.34, 0.03, 4, 9, -18.2, -12.71, 0.7149, 10, -117.52, 20.26, 0.01356, 2, 6.21, 55.5, 0.24154, 56, -351.14, -141.75, 0.03, 4, 9, 94.81, -4.92, 0.55218, 10, -5.62, 2.64, 0.43777, 2, -3.55, 168.36, 5e-05, 56, -360.9, -28.89, 0.01, 4, 9, 36.82, -8.92, 0.94936, 10, -63.05, 11.68, 0.02756, 2, 1.46, 110.45, 0.00308, 56, -355.89, -86.8, 0.02, 4, 9, -75.89, -15.92, 0.00026, 10, -174.47, 30.01, 0.00312, 2, 10.42, -2.11, 0.96662, 56, -346.93, -199.36, 0.03, 4, 9, -184.94, -18.63, 0.00048, 10, -281.37, 51.7, 9e-05, 2, 15.02, -111.1, 0.96943, 56, -342.33, -308.35, 0.03, 4, 9, 106.73, -91.16, 0.07117, 10, -13.24, -84.09, 0.88415, 2, 82.46, 181.78, 0.03469, 56, -274.88, -15.47, 0.01, 4, 9, 47.28, -106.6, 0.2796, 10, -74.64, -85.87, 0.53517, 2, 98.94, 122.61, 0.17523, 56, -258.41, -74.64, 0.01, 4, 9, -3.44, -111.2, 0.30248, 10, -125.12, -79.04, 0.28086, 2, 104.43, 71.97, 0.40667, 56, -252.92, -125.28, 0.01, 4, 9, -73.83, -124.01, 0.11241, 10, -196.59, -75.82, 0.10329, 2, 118.46, 1.82, 0.7743, 56, -238.89, -195.43, 0.01, 4, 9, -131.93, -131.55, 0.02391, 10, -254.9, -70.2, 0.03877, 2, 127, -56.14, 0.92732, 56, -230.35, -253.39, 0.01, 4, 9, -185.19, -141.6, 0.00223, 10, -309.06, -68.12, 0.01402, 2, 137.98, -109.21, 0.97375, 56, -219.37, -306.46, 0.01, 4, 9, -154, 212.96, 0.36784, 10, -199.54, 270.55, 0.00013, 2, -217.07, -84.2, 0.62202, 56, -574.42, -281.45, 0.01, 4, 9, -98.12, 198.57, 0.44198, 10, -148.28, 244.04, 0.00337, 2, -203.65, -28.08, 0.54465, 56, -561, -225.32, 0.01, 4, 9, -10.66, 175.69, 0.63555, 10, -68.12, 202.23, 0.03634, 2, -182.3, 59.77, 0.31811, 56, -539.65, -137.48, 0.01, 4, 9, 51.97, 163.01, 0.716, 10, -9.91, 175.89, 0.12247, 2, -170.71, 122.61, 0.15154, 56, -528.06, -74.64, 0.01, 4, 9, 109.01, 144.93, 0.64053, 10, 41.66, 145.54, 0.29666, 2, -153.63, 179.95, 0.05281, 56, -510.97, -17.3, 0.01], "hull": 41, "edges": [0, 80, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80], "width": 125, "height": 91}}, "archer19": {"archer19": {"type": "mesh", "uvs": [0.91897, 0.03793, 1, 0.10904, 1, 0.2347, 0.97127, 0.30488, 0.89596, 0.39948, 0.86685, 0.45675, 0.85685, 0.50258, 0.84718, 0.54684, 0.77158, 0.58, 0.76447, 0.62538, 0.75657, 0.67579, 0.73981, 0.72586, 0.65635, 0.73334, 0.66278, 0.78475, 0.67237, 0.85186, 0.65871, 0.90335, 0.66674, 0.96761, 0.59888, 1, 0.45819, 1, 0.26023, 1, 0.09171, 1, 0, 1, 0, 0.906, 0.03125, 0.8483, 0.07054, 0.77574, 0.14277, 0.68258, 0.0793, 0.66712, 0.06527, 0.64289, 0.09771, 0.58876, 0.13424, 0.52783, 0.15884, 0.48678, 0.20827, 0.47658, 0.20197, 0.45232, 0.20168, 0.39231, 0.24625, 0.33641, 0.26157, 0.28923, 0.28038, 0.23128, 0.29501, 0.1862, 0.33166, 0.0889, 0.37623, 0.033, 0.4472, 0, 0.56476, 0, 0.67146, 0, 0.77502, 0, 0.29356, 0.52909, 0.38335, 0.55159, 0.49622, 0.55255, 0.62613, 0.55774, 0.72047, 0.56307, 0.23715, 0.69648, 0.34077, 0.70177, 0.44132, 0.71136, 0.54959, 0.71949], "triangles": [32, 33, 34, 44, 31, 32, 34, 35, 44, 44, 32, 34, 44, 46, 45, 44, 6, 46, 6, 47, 46, 50, 44, 45, 51, 45, 46, 52, 46, 47, 6, 48, 47, 9, 48, 8, 26, 27, 28, 10, 48, 9, 44, 25, 29, 28, 29, 25, 26, 28, 25, 31, 29, 30, 29, 31, 44, 49, 25, 44, 49, 44, 50, 50, 45, 51, 51, 46, 52, 48, 10, 47, 10, 12, 47, 11, 12, 10, 52, 47, 12, 12, 13, 52, 15, 13, 14, 49, 24, 25, 19, 49, 50, 19, 24, 49, 18, 51, 52, 13, 18, 52, 15, 18, 13, 50, 51, 18, 17, 15, 16, 20, 21, 22, 23, 24, 19, 20, 23, 19, 20, 22, 23, 19, 50, 18, 17, 18, 15, 0, 1, 2, 2, 43, 0, 3, 4, 43, 2, 3, 43, 4, 42, 43, 4, 5, 42, 36, 6, 35, 44, 35, 6, 5, 41, 42, 41, 39, 40, 41, 5, 39, 5, 38, 39, 5, 6, 38, 6, 37, 38, 37, 6, 36, 6, 8, 48, 7, 8, 6], "vertices": [2, 3, -47.99, 76.48, 0.99999, 5, -404.13, 121.8, 1e-05, 3, 3, -31.91, 125.54, 0.99912, 5, -382.7, 168.77, 3e-05, 62, 368.08, 326.87, 0.00085, 4, 3, 24.86, 152.46, 0.98996, 5, -323.29, 189.22, 8e-05, 62, 373.6, 264.28, 0.00982, 4, -177.06, 152.44, 0.00014, 4, 3, 62.25, 155.51, 0.97182, 5, -285.79, 188.09, 0.00012, 62, 363.46, 228.16, 0.02359, 4, -139.66, 155.45, 0.00447, 4, 3, 119.9, 144.34, 0.86209, 5, -229.74, 170.59, 0.00028, 62, 332.95, 177.99, 0.08641, 4, -82.03, 144.23, 0.05122, 4, 3, 151.54, 144.46, 0.7187, 5, -198.29, 167.2, 0.00044, 62, 322.07, 148.28, 0.16437, 4, -50.39, 144.33, 0.1165, 4, 3, 174.22, 150.1, 0.59747, 5, -175.12, 170.29, 0.00058, 62, 319.48, 125.05, 0.23542, 4, -27.7, 149.95, 0.16654, 4, 3, 196.13, 155.55, 0.51704, 5, -152.74, 173.27, 0.00066, 62, 316.98, 102.61, 0.28764, 4, -5.79, 155.38, 0.19465, 4, 3, 226.08, 131.1, 0.30475, 5, -125.69, 145.64, 0.00087, 62, 283.64, 83.03, 0.45664, 4, 24.14, 130.9, 0.23774, 4, 3, 247.99, 137.86, 0.14352, 5, -103.17, 149.92, 0.0012, 62, 282.36, 60.14, 0.66941, 4, 46.05, 137.64, 0.18587, 4, 3, 272.32, 145.36, 0.05729, 5, -78.15, 154.67, 0.00153, 62, 280.94, 34.71, 0.84527, 4, 70.39, 145.12, 0.09591, 4, 3, 298.26, 149.09, 0.02304, 5, -51.96, 155.5, 0.0017, 62, 275.42, 9.09, 0.93571, 4, 96.34, 148.83, 0.03954, 3, 3, 318.17, 115.86, 0.00034, 62, 237.34, 1.98, 0.99822, 4, 116.21, 115.57, 0.00144, 4, 3, 340.12, 129.56, 3e-05, 5, -12.53, 131.43, 0.05065, 62, 242.55, -23.36, 0.94922, 4, 138.18, 129.25, 0.00011, 4, 3, 368.54, 147.94, 3e-05, 5, 17.75, 146.55, 0.18451, 62, 249.92, -56.4, 0.81537, 4, 166.61, 147.61, 9e-05, 4, 3, 394.5, 153.27, 3e-05, 5, 44.15, 148.96, 0.30082, 62, 245.89, -82.6, 0.69907, 4, 192.58, 152.92, 8e-05, 4, 3, 421.94, 170.39, 3e-05, 5, 73.32, 162.93, 0.38132, 62, 252.41, -114.28, 0.61857, 4, 220.03, 170.02, 8e-05, 4, 3, 450.01, 149, 2e-05, 5, 98.84, 138.55, 0.45712, 62, 222.59, -133.16, 0.54279, 4, 248.09, 148.6, 7e-05, 4, 3, 477.87, 90.28, 1e-05, 5, 120, 77.1, 0.78217, 62, 157.85, -138.87, 0.2178, 4, 275.89, 89.85, 2e-05, 3, 5, 149.77, -9.38, 0.93785, 62, 66.74, -146.9, 0.06209, 4, 315.01, 7.18, 6e-05, 3, 5, 175.12, -83, 0.62947, 62, -10.81, -153.74, 0.37033, 4, 348.31, -63.19, 0.0002, 3, 5, 188.91, -123.06, 0.57427, 62, -53.02, -157.46, 0.4255, 4, 366.43, -101.49, 0.00024, 3, 5, 144.47, -138.36, 0.52074, 62, -57.14, -110.64, 0.47903, 4, 323.95, -121.59, 0.00024, 3, 5, 112.49, -134.1, 0.43229, 62, -45.3, -80.63, 0.56748, 4, 291.7, -120.89, 0.00023, 3, 5, 72.27, -128.74, 0.23789, 62, -30.4, -42.9, 0.7619, 4, 251.14, -119.99, 0.00021, 3, 3, 396.92, -109.4, 6e-05, 62, -1.25, 6.43, 0.99352, 4, 194.76, -109.76, 0.00642, 3, 5, 19.61, -142.6, 0.00028, 62, -31.14, 11.56, 0.98311, 4, 200.32, -139.57, 0.01661, 4, 3, 394.33, -150.26, 0.00014, 5, 10.26, -152.67, 0.00029, 62, -38.66, 23.06, 0.97811, 4, 192.14, -150.61, 0.02147, 4, 3, 363.45, -148.31, 0.00467, 5, -20.21, -147.31, 0.00021, 62, -26.1, 51.33, 0.9025, 4, 161.26, -148.64, 0.09262, 4, 3, 328.69, -146.12, 0.02398, 5, -54.51, -141.27, 0.00013, 62, -11.97, 83.16, 0.72319, 4, 126.51, -146.42, 0.25271, 4, 3, 305.28, -144.65, 0.04495, 5, -77.61, -137.2, 9e-05, 62, -2.45, 104.6, 0.60902, 4, 103.09, -144.92, 0.34594, 4, 3, 290.89, -126.2, 0.09888, 5, -89.87, -117.27, 6e-05, 62, 19.85, 111.69, 0.43258, 4, 88.72, -126.46, 0.46848, 4, 3, 281.17, -134.03, 0.15566, 5, -100.39, -123.97, 4e-05, 62, 15.89, 123.52, 0.32778, 4, 79, -134.28, 0.51651, 4, 3, 254.12, -147.01, 0.25947, 5, -128.72, -133.86, 3e-05, 62, 13.12, 153.39, 0.20624, 4, 51.93, -147.23, 0.53426, 4, 3, 220.04, -140.38, 0.43139, 5, -161.85, -123.49, 2e-05, 62, 31.18, 183.04, 0.10917, 4, 17.86, -140.57, 0.45942, 4, 3, 195.69, -144.1, 0.59955, 5, -186.46, -124.48, 1e-05, 62, 36.16, 207.17, 0.05574, 4, -6.49, -144.27, 0.34469, 4, 3, 165.79, -148.66, 0.76648, 5, -216.69, -125.69, 1e-05, 62, 42.27, 236.79, 0.02211, 4, -36.4, -148.81, 0.21141, 3, 3, 142.53, -152.21, 0.86045, 62, 47.03, 259.84, 0.00909, 4, -59.66, -152.34, 0.13045, 3, 3, 91.32, -157.76, 0.96683, 62, 59.62, 309.79, 0.0003, 4, -110.88, -157.84, 0.03287, 2, 3, 57.24, -151.14, 0.99004, 4, -144.95, -151.19, 0.00996, 2, 3, 28.28, -128.58, 0.9989, 4, -173.89, -128.61, 0.0011, 1, 3, 5.01, -79.51, 1, 1, 3, -16.12, -34.97, 1, 1, 3, -36.62, 8.26, 1, 4, 3, 297.72, -79.35, 0.05078, 5, -77.87, -71.46, 3e-05, 62, 61.41, 88.99, 0.35814, 4, 95.59, -79.61, 0.59105, 4, 3, 290.11, -37.05, 0.01651, 5, -80.74, -28.58, 1e-05, 62, 103.72, 81.43, 0.09911, 4, 88.02, -37.3, 0.88437, 3, 5, -97.26, 20.89, 1e-05, 62, 155.71, 85.53, 0.03237, 4, 66.15, 10.04, 0.96762, 4, 3, 244.82, 65.62, 0.10558, 5, -114.34, 78.48, 0.00037, 62, 215.72, 88.22, 0.34122, 4, 42.82, 65.4, 0.55283, 4, 3, 228.55, 106.14, 0.26025, 5, -126.01, 120.56, 0.00077, 62, 259.37, 89.39, 0.44373, 4, 26.58, 105.94, 0.29524, 3, 3, 384.51, -67.03, 5e-05, 62, 42.8, 3.34, 0.99237, 4, 182.39, -67.37, 0.00758, 2, 62, 90.72, 4.9, 0.93618, 4, 164.31, -22.96, 0.06382, 2, 62, 137.41, 4.21, 0.91653, 4, 148.77, 21.08, 0.08347, 2, 62, 187.6, 4.55, 0.98082, 4, 131.05, 68.03, 0.01918], "hull": 44, "edges": [0, 86, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86], "width": 92, "height": 100}}, "archer20": {"archer20": {"type": "mesh", "uvs": [0.60196, 0.0468, 0.67125, 0.13246, 0.71647, 0.23796, 0.7532, 0.33725, 0.78145, 0.41793, 0.78566, 0.47129, 0.75733, 0.51347, 0.79695, 0.52094, 0.79267, 0.57926, 0.79688, 0.6289, 0.79403, 0.6624, 0.71335, 0.67849, 0.71758, 0.69835, 0.8209, 0.69096, 0.92278, 0.71211, 0.95569, 0.80045, 0.97277, 0.88057, 1, 0.8876, 1, 1, 0.79223, 1, 0.45644, 1, 0.22801, 1, 0.08753, 1, 0.04073, 0.96121, 0.08877, 0.85809, 0.10937, 0.789, 0.12767, 0.74594, 0.12884, 0.70588, 0.07631, 0.69284, 0.06492, 0.64376, 0.0661, 0.58369, 0.08557, 0.50757, 0.11528, 0.49958, 0.06048, 0.45649, 0.02969, 0.38737, 0, 0.30624, 0, 0.17802, 0.03441, 0.09366, 0.12078, 0.04034, 0.24394, 0, 0.34159, 0, 0.43641, 0, 0.52133, 0, 0.63378, 0.54391, 0.49899, 0.56687, 0.36993, 0.56981, 0.24887, 0.55773, 0.17921, 0.53566, 0.2579, 0.70595, 0.41209, 0.70803, 0.57085, 0.70711], "triangles": [3, 43, 44, 44, 45, 2, 48, 46, 45, 49, 45, 44, 48, 45, 49, 6, 4, 5, 6, 43, 4, 8, 6, 7, 9, 11, 8, 8, 11, 43, 8, 43, 6, 10, 11, 9, 30, 47, 29, 28, 29, 47, 27, 47, 46, 27, 28, 47, 27, 46, 48, 50, 44, 43, 50, 43, 11, 50, 11, 12, 49, 44, 50, 22, 23, 24, 26, 27, 48, 48, 25, 26, 49, 21, 48, 21, 25, 48, 24, 25, 21, 22, 24, 21, 20, 49, 50, 21, 49, 20, 19, 12, 13, 15, 19, 13, 15, 13, 14, 19, 15, 16, 50, 12, 19, 20, 50, 19, 16, 17, 18, 19, 16, 18, 38, 35, 36, 38, 34, 35, 36, 37, 38, 32, 33, 34, 34, 39, 32, 4, 43, 3, 38, 39, 34, 40, 47, 32, 0, 1, 41, 1, 45, 46, 3, 44, 2, 39, 40, 32, 46, 47, 40, 41, 42, 0, 46, 40, 41, 41, 1, 46, 2, 45, 1, 32, 30, 31, 30, 32, 47], "vertices": [2, 6, 8.59, 120.81, 0.98924, 52, 255.08, 313.84, 0.01076, 3, 6, 54.57, 141.23, 0.96253, 52, 275.37, 267.8, 0.03715, 7, -149.68, 141.23, 0.00032, 3, 6, 107.86, 149.86, 0.87771, 52, 283.85, 214.49, 0.10666, 7, -96.38, 149.86, 0.01563, 3, 6, 157.53, 155.59, 0.70138, 52, 289.43, 164.8, 0.23559, 7, -46.71, 155.59, 0.06303, 3, 6, 197.76, 159.58, 0.5137, 52, 293.31, 124.56, 0.38058, 7, -6.48, 159.58, 0.10572, 3, 6, 223.17, 156.27, 0.40629, 52, 289.91, 99.16, 0.47297, 7, 18.92, 156.27, 0.12074, 3, 6, 240.62, 140.61, 0.2599, 52, 274.21, 81.76, 0.6199, 7, 36.38, 140.61, 0.1202, 3, 6, 247.41, 156.22, 0.18924, 52, 289.79, 74.92, 0.71174, 7, 43.17, 156.22, 0.09902, 3, 6, 274.44, 148.93, 0.1172, 52, 282.43, 47.91, 0.81381, 7, 70.2, 148.93, 0.06899, 3, 6, 298.1, 145.97, 0.05755, 52, 279.4, 24.26, 0.91061, 7, 93.86, 145.97, 0.03185, 3, 6, 313.6, 141.62, 0.04313, 52, 275.01, 8.78, 0.93545, 7, 109.35, 141.62, 0.02141, 3, 6, 314.46, 106.88, 0.01131, 52, 240.26, 8.02, 0.97497, 7, 110.21, 106.88, 0.01373, 2, 52, 240.1, -1.66, 0.99723, 8, 25.63, 103.67, 0.00277, 2, 52, 283.32, -6.89, 0.97144, 8, 39.81, 144.83, 0.02856, 2, 52, 323.22, -25.39, 0.94673, 8, 66.27, 179.95, 0.05327, 2, 52, 328.27, -69.62, 0.87509, 8, 110.58, 175.61, 0.12491, 2, 52, 327.61, -108.65, 0.78708, 8, 148.61, 166.77, 0.21292, 2, 52, 338.14, -114.24, 0.77753, 8, 156.28, 175.89, 0.22247, 2, 52, 327.35, -166.99, 0.74541, 8, 205.59, 154.27, 0.25459, 2, 52, 241.86, -149.5, 0.53939, 8, 170.54, 74.35, 0.46061, 2, 52, 103.69, -121.23, 0.09993, 8, 113.91, -54.8, 0.90007, 2, 52, 9.69, -102, 0.7932, 8, 75.38, -142.67, 0.2068, 2, 52, -48.11, -90.18, 0.95716, 8, 51.68, -196.71, 0.04284, 2, 52, -63.65, -68.03, 0.97058, 8, 26.77, -207.25, 0.02942, 2, 52, -33.98, -23.68, 0.98623, 8, -10.36, -168.93, 0.01377, 3, 6, 316.22, -152.25, 0.00215, 52, -18.87, 7.01, 0.9955, 7, 111.98, -152.25, 0.00235, 3, 6, 297.52, -140.64, 0.02938, 52, -7.2, 25.67, 0.93484, 7, 93.28, -140.64, 0.03579, 3, 6, 278.81, -136.36, 0.10057, 52, -2.88, 44.37, 0.79134, 7, 74.57, -136.36, 0.10808, 3, 6, 268.32, -156.76, 0.16242, 52, -23.24, 54.91, 0.69735, 7, 64.08, -156.76, 0.14023, 3, 6, 244.33, -156.8, 0.21457, 52, -23.21, 78.9, 0.62596, 7, 40.09, -156.8, 0.15948, 3, 6, 216.23, -150.63, 0.32396, 52, -16.96, 106.99, 0.49777, 7, 11.98, -150.63, 0.17827, 3, 6, 182.1, -135.41, 0.50494, 52, -1.64, 141.08, 0.3306, 7, -22.14, -135.41, 0.16446, 3, 6, 180.81, -122.42, 0.60393, 52, 11.35, 142.33, 0.25364, 7, -23.43, -122.42, 0.14243, 3, 6, 156.03, -140.9, 0.82543, 52, -7.06, 167.16, 0.11333, 7, -48.21, -140.9, 0.06124, 3, 6, 121.02, -147.04, 0.92432, 52, -13.09, 202.19, 0.05398, 7, -83.22, -147.04, 0.02169, 3, 6, 80.46, -151.58, 0.97844, 52, -17.52, 242.76, 0.01873, 7, -123.78, -151.58, 0.00283, 2, 6, 20.25, -139.45, 0.99853, 52, -5.21, 302.93, 0.00147, 1, 6, -16.5, -117.3, 1, 1, 6, -34.37, -76.69, 1, 1, 6, -43.09, -22.16, 1, 1, 6, -34.99, 18.04, 1, 2, 6, -27.12, 57.08, 0.99967, 52, 191.45, 349.73, 0.00033, 2, 6, -20.07, 92.04, 0.99728, 52, 226.4, 342.58, 0.00272, 3, 6, 244.66, 86.86, 0.19911, 52, 220.45, 77.87, 0.5119, 7, 40.41, 86.86, 0.28899, 3, 6, 244.25, 29.19, 0.055, 52, 162.78, 78.44, 0.12734, 7, 40.01, 29.19, 0.81766, 3, 6, 234.92, -24.22, 0.07172, 52, 109.39, 87.93, 0.06601, 7, 30.68, -24.22, 0.86227, 3, 6, 219.2, -72.92, 0.35406, 52, 60.74, 103.79, 0.27525, 7, 14.96, -72.92, 0.3707, 3, 6, 203.06, -99.51, 0.47432, 52, 34.19, 120.01, 0.30535, 7, -1.18, -99.51, 0.22033, 3, 6, 289.55, -83.23, 0.03341, 52, 50.22, 33.47, 0.80852, 7, 85.31, -83.23, 0.15807, 2, 52, 113.47, 19.52, 0.44821, 7, 99.08, -19.95, 0.55179, 3, 6, 316.07, 45.5, 0.00031, 52, 178.88, 6.58, 0.94429, 7, 111.83, 45.5, 0.0554], "hull": 43, "edges": [0, 84, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84], "width": 84, "height": 96}}, "archer21": {"archer16": {"type": "mesh", "uvs": [0.89114, 0.0517, 1, 0.14713, 1, 0.23328, 0.91834, 0.33259, 0.86848, 0.40102, 0.94283, 0.47716, 0.93672, 0.68898, 0.78045, 1, 0.20554, 1, 0, 0.7873, 0, 0.64896, 0.05891, 0.48548, 0.07942, 0.42856, 0.06754, 0.33126, 0, 0.13748, 0.10907, 0.06728, 0.26233, 0, 0.40847, 0, 0.5227, 0, 0.63513, 0, 0.22473, 0.37154, 0.33368, 0.35136, 0.4549, 0.34196, 0.62968, 0.35424, 0.80854, 0.37012], "triangles": [13, 14, 15, 20, 13, 15, 21, 20, 15, 12, 13, 20, 6, 4, 5, 8, 9, 10, 20, 11, 12, 20, 10, 11, 20, 8, 10, 23, 6, 7, 22, 23, 7, 8, 21, 22, 8, 20, 21, 22, 7, 8, 0, 1, 2, 3, 0, 2, 22, 17, 18, 21, 16, 17, 21, 17, 22, 15, 16, 21, 23, 18, 19, 23, 19, 0, 24, 23, 0, 22, 18, 23, 3, 24, 0, 4, 24, 3, 24, 6, 23, 4, 6, 24], "vertices": [3, 16, -24.5, 122.31, 0.98811, 17, -140.17, 115.62, 0.00189, 55, -0.46, -46.7, 0.01, 3, 16, 0.55, 165.16, 0.97187, 17, -117.94, 160, 0.01813, 55, 49.17, -46.66, 0.01, 3, 16, 29.64, 170.86, 0.951, 17, -89.29, 167.57, 0.039, 55, 68.79, -68.87, 0.01, 3, 16, 68.53, 150.03, 0.84475, 17, -49.13, 149.3, 0.14525, 55, 70.47, -112.96, 0.01, 3, 16, 94.91, 137.82, 0.64171, 17, -22.01, 138.82, 0.34829, 55, 73.27, -141.9, 0.01, 3, 16, 115.73, 167.82, 0.44933, 17, -3.18, 170.1, 0.54067, 55, 109.67, -144.7, 0.01, 3, 16, 187.63, 179.79, 0.23422, 17, 67.8, 186.69, 0.75578, 55, 156.34, -200.7, 0.01, 3, 16, 302.91, 147.93, 0.04104, 17, 184.89, 162.34, 0.94896, 55, 187.1, -316.27, 0.01, 2, 17, 235.11, -27.75, 0.99, 55, 39.73, -446.42, 0.01, 2, 17, 182.32, -114.41, 0.99, 55, -61.4, -438.11, 0.01, 3, 16, 235.76, -137.23, 0.00032, 17, 136.31, -126.56, 0.98968, 55, -92.9, -402.44, 0.01, 3, 16, 176.7, -128.28, 0.04058, 17, 76.79, -121.45, 0.94942, 55, -115.02, -346.95, 0.01, 3, 16, 156.14, -125.17, 0.10107, 17, 56.07, -119.67, 0.88893, 55, -122.73, -327.63, 0.01, 3, 16, 124.07, -135.59, 0.26662, 17, 24.75, -132.14, 0.72338, 55, -147.93, -305.23, 0.01, 3, 16, 63.1, -171.09, 0.49131, 17, -33.8, -171.5, 0.49869, 55, -209.37, -270.56, 0.01, 3, 16, 32.23, -139.13, 0.58173, 17, -66.68, -141.6, 0.40827, 55, -197.39, -227.76, 0.01, 3, 16, -0.57, -92.15, 0.78606, 17, -102.44, -96.84, 0.20394, 55, -173.43, -175.72, 0.01, 3, 16, -10.19, -43.1, 0.93828, 17, -115.21, -48.52, 0.05172, 55, -135.97, -142.64, 0.01, 3, 16, -17.7, -4.77, 0.98829, 17, -125.19, -10.75, 0.00171, 55, -106.68, -116.78, 0.01, 2, 16, -25.1, 32.96, 0.99, 55, -77.86, -91.32, 0.01, 3, 16, 127.33, -80.17, 0.1672, 17, 24.41, -76.63, 0.8228, 55, -98.46, -280.03, 0.01, 3, 16, 113.34, -44.95, 0.2029, 17, 8.18, -42.38, 0.7871, 55, -75.13, -250.16, 0.01, 3, 16, 102.19, -4.89, 0.35676, 17, -5.53, -3.12, 0.63324, 55, -46.19, -220.3, 0.01, 3, 16, 94.84, 54.58, 0.70297, 17, -16.71, 55.75, 0.28703, 55, 1.41, -183.9, 0.01, 3, 16, 88.43, 115.66, 0.70851, 17, -27.06, 116.29, 0.28149, 55, 50.87, -147.5, 0.01], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38], "width": 68, "height": 69}, "archer21": {"type": "mesh", "uvs": [0.745, 0.07666, 0.70056, 0.25274, 0.72654, 0.31162, 0.77086, 0.32447, 0.83905, 0.34424, 0.91708, 0.48902, 0.91726, 0.57156, 1, 0.77508, 1, 0.84589, 0.88864, 0.93415, 0.70069, 1, 0.55745, 1, 0.37742, 1, 0.17973, 0.92012, 0.12623, 0.80021, 0.09626, 0.6375, 0.10735, 0.53826, 0.11085, 0.50688, 0.07522, 0.41633, 0, 0.32851, 0, 0.27485, 0.08717, 0.17461, 0.2661, 0.05934, 0.4316, 0, 0.57435, 0, 0.64519, 0, 0.67549, 0.36653, 0.51531, 0.37067, 0.39453, 0.39288, 0.27466, 0.4415], "triangles": [17, 18, 29, 29, 16, 17, 29, 15, 16, 14, 15, 29, 9, 6, 7, 12, 13, 14, 26, 5, 6, 5, 26, 4, 8, 9, 7, 12, 29, 28, 12, 14, 29, 26, 2, 3, 26, 3, 4, 26, 6, 27, 6, 11, 27, 6, 10, 11, 12, 28, 11, 9, 10, 6, 1, 25, 0, 18, 19, 20, 1, 24, 25, 26, 1, 2, 27, 23, 24, 28, 22, 23, 1, 27, 24, 26, 27, 1, 27, 28, 23, 21, 22, 28, 21, 18, 20, 21, 29, 18, 28, 29, 21, 28, 27, 11], "vertices": [3, 16, 35.79, 149.54, 0.97512, 17, -81.78, 146.69, 0.01488, 55, 53.5, -84.95, 0.01, 3, 16, 86.8, 115.31, 0.77584, 17, -28.66, 115.83, 0.21416, 55, 49.75, -146.27, 0.01, 3, 16, 108.47, 116.4, 0.54578, 17, -7.11, 118.32, 0.44422, 55, 61.63, -164.42, 0.01, 3, 16, 117.55, 128.5, 0.41967, 17, 1.18, 130.98, 0.57033, 55, 76.67, -166.14, 0.01, 3, 16, 131.53, 147.12, 0.33219, 17, 13.93, 150.47, 0.65781, 55, 99.8, -168.8, 0.01, 3, 16, 186.4, 154.14, 0.17639, 17, 68.23, 161.02, 0.81361, 55, 133.59, -212.6, 0.01, 3, 16, 212.69, 144.55, 0.10719, 17, 95.08, 153.14, 0.88281, 55, 138.6, -240.13, 0.01, 3, 16, 286.78, 146.15, 0.01029, 17, 168.91, 159.53, 0.97971, 55, 177.42, -303.25, 0.01, 3, 16, 309.31, 137.87, 0.00456, 17, 191.93, 152.72, 0.98544, 55, 181.66, -326.88, 0.01, 3, 16, 324.84, 93.37, 0.00023, 17, 210.3, 109.32, 0.98977, 55, 151.11, -362.76, 0.01, 2, 17, 214.28, 44.05, 0.99, 55, 94.56, -395.6, 0.01, 2, 17, 201, -0.87, 0.99, 55, 48.46, -403.88, 0.01, 2, 17, 184.31, -57.32, 0.99, 55, -9.48, -414.28, 0.01, 2, 17, 140.01, -111.64, 0.99, 55, -77.9, -399.05, 0.01, 3, 16, 196.24, -124.98, 0.00106, 17, 96.07, -116.89, 0.98894, 55, -102.3, -362.14, 0.01, 3, 16, 141.08, -115.16, 0.06501, 17, 40.4, -110.65, 0.92499, 55, -121.7, -309.58, 0.01, 3, 16, 110.76, -100.15, 0.22806, 17, 9.17, -97.63, 0.76194, 55, -124.07, -275.83, 0.01, 3, 16, 101.17, -95.41, 0.3335, 17, -0.71, -93.52, 0.6565, 55, -124.82, -265.15, 0.01, 3, 16, 68.33, -95.75, 0.67911, 17, -33.45, -95.98, 0.31089, 55, -141.72, -237, 0.01, 3, 16, 31.91, -108.58, 0.87279, 17, -68.97, -111.13, 0.11721, 55, -171.19, -212.05, 0.01, 3, 16, 14.83, -102.3, 0.8993, 17, -86.42, -105.98, 0.0907, 55, -174.41, -194.14, 0.01, 3, 16, -7.23, -63.83, 0.96389, 17, -110.92, -69.01, 0.02611, 55, -152.35, -155.66, 0.01, 2, 16, -23.73, 4.57, 0.99, 55, -101.67, -106.85, 0.01, 2, 16, -23.95, 62.31, 0.99, 55, -51.96, -77.49, 0.01, 2, 16, -7.85, 106.12, 0.99, 55, -6.02, -69.24, 0.01, 3, 16, 0.14, 127.87, 0.9891, 17, -115.95, 122.76, 0.0009, 55, 16.78, -65.14, 0.01, 3, 16, 120.18, 94.31, 0.49919, 17, 6.01, 97.03, 0.49081, 55, 48.49, -185.69, 0.01, 3, 16, 103.43, 44.66, 0.6709, 17, -7.49, 46.4, 0.3191, 55, -2.81, -196.33, 0.01, 3, 16, 96.88, 4.99, 0.92606, 17, -11.47, 6.4, 0.06394, 55, -40.35, -210.72, 0.01, 3, 16, 98.83, -37.48, 0.41116, 17, -6.78, -35.86, 0.57884, 55, -76.02, -233.87, 0.01], "hull": 26, "edges": [0, 50, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50], "width": 65, "height": 68}}, "archer22": {"archer22": {"type": "mesh", "uvs": [0.41006, 0.04144, 0.46401, 0.06851, 0.50908, 0.11549, 0.53181, 0.13917, 0.57013, 0.1996, 0.6075, 0.25854, 0.64018, 0.31007, 0.66744, 0.35307, 0.72171, 0.39192, 0.80011, 0.44804, 0.84703, 0.49582, 0.87996, 0.52935, 0.91956, 0.56968, 0.96364, 0.62804, 1, 0.6762, 1, 0.73443, 1, 0.798, 1, 0.84821, 0.91218, 0.9336, 0.80927, 0.97188, 0.64736, 1, 0.54244, 1, 0.46852, 1, 0.36024, 0.92595, 0.31879, 0.86746, 0.27947, 0.81197, 0.23328, 0.7468, 0.20123, 0.70157, 0.17348, 0.66241, 0.14004, 0.60195, 0.11508, 0.55682, 0.08316, 0.49911, 0.06017, 0.44885, 0.03896, 0.40248, 0, 0.3173, 0, 0.22215, 0, 0.14987, 0, 0.0657, 0.11046, 0, 0.18361, 0, 0.26289, 0, 0.32745, 0], "triangles": [13, 14, 15, 9, 26, 27, 24, 13, 15, 11, 25, 26, 16, 24, 15, 12, 24, 25, 12, 25, 11, 24, 12, 13, 21, 23, 24, 19, 24, 16, 17, 18, 16, 16, 18, 19, 21, 22, 23, 21, 24, 20, 19, 20, 24, 36, 37, 38, 39, 35, 36, 33, 34, 35, 35, 39, 33, 39, 32, 33, 39, 31, 32, 38, 39, 36, 31, 1, 30, 40, 41, 0, 39, 40, 0, 0, 31, 39, 7, 29, 30, 0, 1, 31, 2, 30, 1, 2, 4, 30, 3, 4, 2, 5, 30, 4, 6, 30, 5, 7, 30, 6, 8, 28, 29, 8, 27, 28, 7, 8, 29, 9, 27, 8, 26, 9, 10, 11, 26, 10], "vertices": [2, 15, -15.98, 53.49, 0.99, 55, -144.91, 47.42, 0.01, 2, 15, -2.75, 60.4, 0.99, 55, -131.89, 40.13, 0.01, 2, 15, 15.11, 62.61, 0.99, 55, -120.26, 26.41, 0.01, 2, 15, 24.11, 63.72, 0.99, 55, -114.4, 19.48, 0.01, 2, 15, 45.01, 62.61, 0.99, 55, -103.88, 1.39, 0.01, 2, 15, 65.39, 61.53, 0.99, 55, -93.63, -16.26, 0.01, 2, 15, 83.21, 60.58, 0.99, 55, -84.66, -31.69, 0.01, 3, 15, 98.08, 59.79, 0.98936, 55, -77.18, -44.57, 0.01, 16, -65.1, 57.18, 0.00064, 3, 15, 114.64, 65.04, 0.97386, 55, -63.72, -55.54, 0.01, 16, -48.83, 63.25, 0.01614, 3, 15, 138.55, 72.62, 0.87818, 55, -44.29, -71.4, 0.01, 16, -25.33, 72, 0.11182, 3, 15, 156.82, 75.09, 0.73659, 55, -32.22, -85.34, 0.01, 16, -7.2, 75.38, 0.25341, 3, 15, 169.64, 76.82, 0.60886, 55, -23.75, -95.12, 0.01, 16, 5.52, 77.74, 0.38114, 3, 15, 185.07, 78.9, 0.45744, 55, -13.56, -106.89, 0.01, 16, 20.82, 80.59, 0.53256, 3, 15, 205.99, 79.25, 0.28337, 55, -1.81, -124.21, 0.01, 16, 41.71, 81.97, 0.70663, 3, 15, 223.26, 79.54, 0.1885, 55, 7.89, -138.5, 0.01, 16, 58.94, 83.12, 0.8015, 3, 15, 239.52, 71.04, 0.11341, 55, 9.68, -156.75, 0.01, 16, 75.6, 75.44, 0.87659, 3, 15, 257.26, 61.76, 0.04923, 55, 11.63, -176.68, 0.01, 16, 93.78, 67.05, 0.94077, 3, 15, 271.28, 54.43, 0.02385, 55, 13.17, -192.42, 0.01, 16, 108.14, 60.43, 0.96615, 3, 15, 285.88, 24.3, 0.00158, 55, -4.04, -221.14, 0.01, 16, 124.22, 31.06, 0.98842, 2, 55, -26.11, -235.42, 0.01, 16, 125.39, 4.8, 0.99, 2, 55, -61.83, -247.82, 0.01, 16, 118.05, -32.28, 0.99, 2, 55, -85.53, -250.15, 0.01, 16, 108.07, -53.91, 0.99, 2, 55, -102.23, -251.78, 0.01, 16, 101.05, -69.15, 0.99, 2, 55, -128.97, -230.97, 0.01, 16, 69.58, -81.71, 0.99, 3, 15, 205.01, -85.42, 0.00569, 55, -140.13, -213.55, 0.01, 16, 48.9, -82.54, 0.98431, 3, 15, 185.39, -85.23, 0.04295, 55, -150.72, -197.03, 0.01, 16, 29.29, -83.32, 0.94705, 3, 15, 162.34, -85.01, 0.16601, 55, -163.16, -177.62, 0.01, 16, 6.26, -84.25, 0.82399, 3, 15, 146.34, -84.85, 0.30292, 55, -171.79, -164.15, 0.01, 16, -9.72, -84.89, 0.68708, 3, 15, 132.49, -84.72, 0.43547, 55, -179.26, -152.49, 0.01, 16, -23.57, -85.45, 0.55453, 3, 15, 112.09, -82.62, 0.62999, 55, -188.68, -134.28, 0.01, 16, -44.04, -84.36, 0.36001, 3, 15, 96.87, -81.06, 0.75241, 55, -195.7, -120.68, 0.01, 16, -59.32, -83.56, 0.23759, 3, 15, 77.4, -79.06, 0.86463, 55, -204.69, -103.3, 0.01, 16, -78.86, -82.53, 0.12537, 3, 15, 60.95, -76.35, 0.92513, 55, -211.43, -88.05, 0.01, 16, -95.42, -80.64, 0.06487, 3, 15, 45.78, -73.85, 0.95904, 55, -217.64, -73.98, 0.01, 16, -110.7, -78.89, 0.03096, 3, 15, 17.9, -69.25, 0.98514, 55, -229.07, -48.14, 0.01, 16, -138.78, -75.69, 0.00486, 3, 15, -8.66, -55.37, 0.98998, 55, -231.99, -18.31, 0.01, 16, -165.99, -63.14, 2e-05, 2, 15, -28.84, -44.82, 0.99, 55, -234.21, 4.35, 0.01, 2, 15, -52.33, -32.54, 0.99, 55, -236.8, 30.73, 0.01, 2, 15, -59.05, -0.73, 0.99, 55, -213.87, 53.78, 0.01, 2, 15, -51.36, 13.99, 0.99, 55, -197.34, 55.4, 0.01, 2, 15, -43.02, 29.94, 0.99, 55, -179.43, 57.16, 0.01, 2, 15, -36.23, 42.92, 0.99, 55, -164.85, 58.59, 0.01], "hull": 42, "edges": [0, 82, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82], "width": 45, "height": 63}}, "archer23": {"archer23": {"type": "mesh", "uvs": [0.62341, 0, 0.63046, 0.00045, 0.65106, 0.00455, 0.66526, 0.00739, 0.70603, 0.01552, 0.75578, 0.02544, 0.78884, 0.03204, 0.82719, 0.03969, 0.85168, 0.04457, 0.88627, 0.04761, 0.92911, 0.04961, 0.9611, 0.05111, 0.98437, 0.0522, 0.99185, 0.10563, 0.99257, 0.13393, 0.99432, 0.18302, 0.99579, 0.22409, 0.99705, 0.25952, 0.99765, 0.29935, 0.99827, 0.34004, 0.99896, 0.38558, 0.99945, 0.41834, 1, 0.45471, 1, 0.49989, 1, 0.5456, 1, 0.59851, 0.99719, 0.6274, 0.99283, 0.67227, 0.98876, 0.71425, 0.98551, 0.74761, 0.98099, 0.76876, 0.97291, 0.80653, 0.96351, 0.85049, 0.95366, 0.89655, 0.94507, 0.93672, 0.94302, 0.94136, 0.93392, 0.94801, 0.93351, 0.94718, 0.92273, 0.91265, 0.91853, 0.88958, 0.91075, 0.84684, 0.89295, 0.87512, 0.87933, 0.89678, 0.86378, 0.91578, 0.84477, 0.94058, 0.81983, 0.9759, 0.80282, 0.99998, 0.80241, 1, 0.78104, 1, 0.75929, 0.97614, 0.73889, 0.95376, 0.72159, 0.93024, 0.70417, 0.90654, 0.68853, 0.88945, 0.67448, 0.87409, 0.65974, 0.85799, 0.64733, 0.84991, 0.61625, 0.83295, 0.60998, 0.836, 0.58833, 0.8532, 0.57397, 0.87055, 0.55224, 0.89681, 0.53109, 0.92237, 0.51506, 0.95185, 0.49934, 0.98074, 0.482, 0.98061, 0.47032, 0.95391, 0.45235, 0.90493, 0.43908, 0.86877, 0.42605, 0.83322, 0.41157, 0.79559, 0.40641, 0.7871, 0.38111, 0.81132, 0.35827, 0.83238, 0.32508, 0.87071, 0.30186, 0.89752, 0.28193, 0.92054, 0.27617, 0.93091, 0.25838, 0.93483, 0.25239, 0.92447, 0.2522, 0.89246, 0.25349, 0.84972, 0.25458, 0.81368, 0.25576, 0.77501, 0.25533, 0.75977, 0.25149, 0.75996, 0.22518, 0.76879, 0.19681, 0.77831, 0.17574, 0.78675, 0.16187, 0.78672, 0.15758, 0.77922, 0.15551, 0.76754, 0.16606, 0.73988, 0.17615, 0.70682, 0.15477, 0.71198, 0.11938, 0.72975, 0.09283, 0.74307, 0.07335, 0.75257, 0.04798, 0.79255, 0.03932, 0.80873, 0.02551, 0.84135, 0.01546, 0.84641, 0.00735, 0.84153, 0.00081, 0.82953, 0.00112, 0.80742, 0.00836, 0.77013, 0.01545, 0.73361, 0.01713, 0.72192, 0.02402, 0.69645, 0.03005, 0.67413, 0.03715, 0.64788, 0.03573, 0.64492, 0.04217, 0.6299, 0.04879, 0.6057, 0.05116, 0.59244, 0.0649, 0.56764, 0.07962, 0.54553, 0.09658, 0.52006, 0.10866, 0.50186, 0.12501, 0.47724, 0.14595, 0.45351, 0.17141, 0.42468, 0.18423, 0.41016, 0.20809, 0.3897, 0.22598, 0.37436, 0.24095, 0.36149, 0.25615, 0.34843, 0.27809, 0.32647, 0.28812, 0.31552, 0.308, 0.29118, 0.32383, 0.26769, 0.34544, 0.23562, 0.35863, 0.21605, 0.37973, 0.18177, 0.40362, 0.14296, 0.43272, 0.09568, 0.45446, 0.07398, 0.47451, 0.05398, 0.4963, 0.03223, 0.51644, 0.02484, 0.54642, 0.01385, 0.56169, 0.00825, 0.57174, 0.00654, 0.59879, 0.00206, 0.61126, 0], "triangles": [84, 125, 126, 124, 125, 84, 123, 124, 84, 127, 84, 126, 93, 121, 122, 120, 121, 93, 119, 120, 93, 118, 119, 93, 94, 117, 118, 94, 116, 117, 118, 93, 94, 93, 122, 123, 123, 85, 93, 84, 85, 123, 86, 93, 85, 87, 92, 93, 86, 87, 93, 89, 90, 91, 92, 89, 91, 92, 88, 89, 87, 88, 92, 110, 111, 112, 95, 116, 94, 115, 116, 95, 113, 114, 115, 95, 113, 115, 96, 112, 113, 96, 110, 112, 110, 97, 109, 96, 113, 95, 97, 108, 109, 110, 96, 97, 107, 108, 97, 98, 106, 107, 97, 98, 107, 105, 106, 98, 99, 105, 98, 104, 105, 99, 100, 104, 99, 102, 103, 104, 104, 101, 102, 100, 101, 104, 58, 70, 71, 71, 133, 58, 56, 57, 4, 69, 70, 60, 58, 134, 57, 56, 7, 55, 58, 59, 70, 69, 61, 68, 70, 59, 60, 54, 55, 40, 60, 61, 69, 62, 67, 68, 61, 62, 68, 63, 67, 62, 66, 67, 63, 64, 65, 66, 63, 64, 66, 40, 53, 54, 18, 27, 28, 15, 28, 30, 28, 29, 30, 31, 40, 30, 30, 40, 8, 32, 40, 31, 40, 55, 7, 41, 53, 40, 39, 40, 32, 33, 39, 32, 38, 39, 33, 34, 38, 33, 34, 37, 38, 35, 37, 34, 36, 37, 35, 41, 52, 53, 41, 43, 52, 42, 43, 41, 51, 52, 43, 44, 51, 43, 50, 51, 44, 44, 49, 50, 45, 49, 44, 45, 48, 49, 45, 47, 48, 46, 47, 45, 27, 21, 22, 22, 23, 27, 23, 24, 27, 20, 27, 19, 21, 27, 20, 25, 26, 24, 27, 24, 26, 17, 18, 16, 19, 27, 18, 11, 13, 14, 28, 16, 18, 15, 16, 28, 11, 14, 15, 13, 11, 12, 11, 15, 10, 10, 15, 30, 9, 30, 8, 10, 30, 9, 132, 133, 71, 131, 132, 71, 130, 131, 71, 129, 130, 71, 128, 129, 71, 127, 83, 84, 127, 128, 71, 71, 83, 127, 72, 83, 71, 72, 73, 83, 82, 75, 81, 83, 73, 74, 81, 76, 80, 82, 74, 75, 75, 76, 81, 77, 79, 80, 76, 77, 80, 78, 79, 77, 83, 74, 82, 57, 2, 3, 57, 3, 4, 5, 56, 4, 6, 56, 5, 7, 56, 6, 8, 40, 7, 58, 133, 134, 143, 0, 2, 144, 0, 143, 2, 0, 1, 57, 143, 2, 142, 143, 57, 140, 141, 142, 57, 140, 142, 139, 140, 57, 137, 138, 139, 57, 137, 139, 57, 136, 137, 57, 134, 136, 134, 135, 136], "vertices": [4, 41, -64.48, 27.11, 0.63889, 47, -18.08, -124.04, 0.33447, 48, -97.37, -129.65, 0.01664, 55, -526.21, 115.29, 0.01, 5, 41, -69.29, 31.66, 0.5953, 47, -19.62, -117.6, 0.37752, 48, -99.32, -123.32, 0.01717, 55, -519.59, 115.06, 0.01, 42, -196.2, 20.22, 1e-05, 5, 41, -82.39, 46.03, 0.46243, 47, -22.74, -98.4, 0.51169, 48, -103.67, -104.36, 0.01585, 55, -500.26, 112.97, 0.01, 42, -209.89, 34.03, 3e-05, 4, 41, -91.43, 55.94, 0.37063, 47, -24.89, -85.16, 0.60711, 48, -106.67, -91.29, 0.01225, 55, -486.92, 111.52, 0.01, 4, 41, -117.38, 84.4, 0.14354, 47, -31.06, -47.15, 0.84566, 48, -115.28, -53.75, 0.0008, 55, -448.64, 107.36, 0.01, 5, 41, -149.04, 119.11, 0.00854, 47, -38.6, -0.77, 0.95682, 55, -401.92, 102.29, 0.01, 44, -41.57, -178.32, 0.02063, 45, -131.42, -180.28, 0.00401, 4, 47, -43.6, 30.05, 0.85748, 55, -370.88, 98.92, 0.01, 44, -38.19, -147.28, 0.10364, 45, -129.21, -149.13, 0.02888, 4, 47, -49.41, 65.81, 0.64616, 55, -334.87, 95.01, 0.01, 44, -34.27, -111.27, 0.27606, 45, -126.65, -113, 0.06778, 4, 47, -53.12, 88.64, 0.48379, 55, -311.87, 92.52, 0.01, 44, -31.77, -88.27, 0.42591, 45, -125.01, -89.92, 0.0803, 4, 47, -60.26, 120.36, 0.25461, 55, -279.39, 90.96, 0.01, 44, -30.21, -55.8, 0.67405, 45, -124.67, -57.41, 0.06134, 4, 47, -69.97, 159.42, 0.04583, 55, -239.16, 89.94, 0.01, 44, -29.17, -15.57, 0.93461, 45, -125.15, -17.17, 0.00956, 3, 47, -77.22, 188.57, 0.00104, 55, -209.13, 89.17, 0.01, 44, -28.39, 14.47, 0.98896, 2, 55, -187.27, 88.62, 0.01, 44, -27.83, 36.32, 0.99, 2, 55, -180.25, 61.31, 0.01, 44, -0.52, 43.34, 0.99, 2, 55, -179.58, 46.85, 0.01, 44, 13.94, 44.01, 0.99, 2, 55, -177.93, 21.77, 0.01, 44, 39.02, 45.64, 0.99, 2, 55, -176.55, 0.78, 0.01, 44, 60.01, 47.01, 0.99, 3, 55, -175.36, -17.32, 0.01, 44, 78.11, 48.2, 0.98601, 45, -20.34, 50.58, 0.00399, 3, 55, -174.8, -37.67, 0.01, 44, 98.47, 48.75, 0.89825, 45, -0.02, 51.91, 0.09175, 3, 55, -174.22, -58.47, 0.01, 44, 119.26, 49.32, 0.62472, 45, 20.73, 53.26, 0.36528, 3, 55, -173.58, -81.74, 0.01, 44, 142.53, 49.96, 0.29496, 45, 43.97, 54.77, 0.69504, 3, 55, -173.11, -98.48, 0.01, 44, 159.27, 50.42, 0.15015, 45, 60.68, 55.86, 0.83985, 3, 55, -172.6, -117.07, 0.01, 44, 177.86, 50.93, 0.05961, 45, 79.23, 57.07, 0.93039, 3, 55, -172.6, -140.15, 0.01, 44, 200.94, 50.92, 0.01319, 45, 102.3, 57.93, 0.97681, 4, 55, -172.6, -163.51, 0.01, 44, 224.3, 50.92, 0.00086, 45, 125.64, 58.8, 0.96259, 46, -76.3, 32.07, 0.02655, 3, 55, -172.6, -190.55, 0.01, 45, 152.66, 59.81, 0.80413, 46, -52.44, 44.78, 0.18587, 3, 55, -175.23, -205.31, 0.01, 45, 167.51, 57.73, 0.63514, 46, -38.17, 49.39, 0.35486, 3, 55, -179.33, -228.24, 0.01, 45, 190.58, 54.49, 0.30594, 46, -16.01, 56.55, 0.68406, 3, 55, -183.16, -249.69, 0.01, 45, 212.16, 51.46, 0.09546, 46, 4.73, 63.25, 0.89454, 3, 55, -186.2, -266.74, 0.01, 45, 229.31, 49.06, 0.02463, 46, 21.21, 68.57, 0.96537, 3, 55, -190.45, -277.54, 0.01, 45, 240.26, 45.22, 0.00642, 46, 32.74, 69.9, 0.98358, 2, 55, -198.03, -296.85, 0.01, 46, 53.35, 72.27, 0.99, 2, 55, -206.86, -319.31, 0.01, 46, 77.32, 75.04, 0.99, 2, 55, -216.11, -342.85, 0.01, 46, 102.45, 77.93, 0.99, 2, 55, -224.18, -363.37, 0.01, 46, 124.36, 80.46, 0.99, 2, 55, -226.1, -365.74, 0.01, 46, 127.35, 79.87, 0.99, 2, 55, -234.64, -369.14, 0.01, 46, 134.37, 73.93, 0.99, 2, 55, -235.03, -368.72, 0.01, 46, 134.17, 73.39, 0.99, 2, 55, -245.15, -351.07, 0.01, 46, 123.36, 56.16, 0.99, 2, 55, -249.1, -339.28, 0.01, 46, 114.8, 47.14, 0.99, 2, 55, -256.4, -317.44, 0.01, 46, 98.96, 30.43, 0.99, 2, 55, -273.11, -331.9, 0.01, 46, 119.57, 22.47, 0.99, 2, 55, -285.91, -342.96, 0.01, 46, 135.35, 16.37, 0.99, 2, 55, -300.51, -352.67, 0.01, 46, 150.78, 8.05, 0.99, 3, 55, -318.36, -365.35, 0.01, 46, 170.36, -1.76, 0.98984, 49, 121.96, 230.81, 0.00016, 3, 55, -341.78, -383.39, 0.01, 46, 197.29, -13.95, 0.93849, 49, 147.44, 215.81, 0.05151, 3, 55, -357.75, -395.7, 0.01, 46, 215.66, -22.27, 0.90633, 49, 164.81, 205.58, 0.08367, 3, 55, -358.13, -395.71, 0.01, 46, 215.84, -22.6, 0.90595, 49, 164.96, 205.23, 0.08405, 3, 55, -378.2, -395.71, 0.01, 46, 225.27, -40.31, 0.87172, 49, 172.44, 186.61, 0.11828, 3, 55, -398.63, -383.51, 0.01, 46, 224.11, -64.07, 0.79978, 49, 168.75, 163.11, 0.19022, 3, 55, -417.78, -372.08, 0.01, 46, 223.02, -86.36, 0.70016, 49, 165.28, 141.07, 0.28984, 3, 55, -434.02, -360.06, 0.01, 46, 220.04, -106.34, 0.59284, 49, 160.18, 121.52, 0.39716, 3, 55, -450.38, -347.95, 0.01, 46, 217.03, -126.47, 0.46782, 49, 155.04, 101.83, 0.52218, 3, 55, -465.07, -339.21, 0.01, 46, 216.22, -143.54, 0.35911, 49, 152.41, 84.94, 0.63089, 3, 55, -478.26, -331.37, 0.01, 46, 215.5, -158.87, 0.26617, 49, 150.05, 69.78, 0.72383, 3, 55, -492.1, -323.14, 0.01, 46, 214.73, -174.95, 0.17086, 49, 147.57, 53.87, 0.81914, 3, 55, -503.76, -319.01, 0.01, 46, 216.57, -187.19, 0.10624, 49, 148.09, 41.51, 0.88376, 3, 55, -532.94, -310.34, 0.01, 46, 222.63, -217.02, 0.00761, 49, 150.93, 11.2, 0.98239, 3, 55, -538.83, -311.9, 0.01, 46, 226.77, -221.48, 0.00115, 49, 154.57, 6.32, 0.98885, 4, 55, -559.16, -320.7, 0.01, 42, 109.16, 333.6, 0.01471, 46, 244.09, -235.3, 0.00119, 49, 170.31, -9.27, 0.9741, 4, 55, -572.64, -329.56, 0.01, 42, 125.21, 331.99, 0.04124, 46, 258.25, -243.03, 0.00195, 49, 183.56, -18.47, 0.9468, 4, 55, -593.05, -342.98, 0.01, 42, 149.52, 329.54, 0.08116, 46, 279.68, -254.74, 0.00249, 49, 203.62, -32.4, 0.90635, 4, 55, -612.9, -356.04, 0.01, 42, 173.16, 327.17, 0.11391, 46, 300.54, -266.13, 0.00278, 49, 223.14, -45.96, 0.87331, 4, 55, -627.96, -371.1, 0.01, 42, 194.34, 329.37, 0.13187, 46, 320.91, -272.34, 0.00293, 49, 242.73, -54.31, 0.85519, 4, 55, -642.71, -385.86, 0.01, 42, 215.1, 331.54, 0.13934, 46, 340.87, -278.43, 0.00299, 49, 261.93, -62.5, 0.84766, 5, 55, -659, -385.8, 0.01, 42, 227.7, 321.22, 0.14382, 46, 348.47, -292.84, 0.00299, 49, 267.94, -77.63, 0.84318, 43, -29.18, 328.65, 1e-05, 5, 55, -669.97, -372.16, 0.01, 42, 227.63, 303.72, 0.15514, 46, 341.58, -308.93, 0.00292, 49, 259.38, -92.9, 0.8317, 43, -26.82, 311.31, 0.00024, 5, 55, -686.84, -347.13, 0.01, 42, 224.95, 273.65, 0.2055, 46, 327.41, -335.59, 0.00262, 49, 242.44, -117.89, 0.77956, 43, -25.28, 281.16, 0.00232, 5, 55, -699.3, -328.65, 0.01, 42, 222.97, 251.45, 0.26643, 46, 316.96, -355.26, 0.00229, 49, 229.94, -136.33, 0.71454, 43, -24.15, 258.9, 0.00674, 6, 48, 365.62, -165.33, 0.00021, 55, -711.54, -310.48, 0.01, 42, 221.03, 229.63, 0.34451, 46, 306.67, -374.61, 0.00191, 49, 217.65, -154.47, 0.62701, 43, -23.04, 237.03, 0.01636, 6, 48, 351.9, -184.47, 0.00079, 55, -725.13, -291.26, 0.01, 42, 219.46, 206.14, 0.45447, 46, 296.09, -395.64, 0.00142, 49, 204.87, -174.24, 0.49121, 43, -21.33, 213.54, 0.04211, 6, 48, 349.39, -190.47, 0.00083, 55, -729.98, -286.92, 0.01, 42, 220.49, 199.72, 0.49745, 46, 294.53, -401.95, 0.00124, 49, 202.65, -180.36, 0.43118, 43, -19.42, 207.32, 0.05931, 6, 48, 368.86, -208.87, 9e-05, 55, -753.74, -299.29, 0.01, 42, 246.74, 194.35, 0.56765, 46, 316.62, -417.11, 0.00088, 49, 223, -197.79, 0.29081, 43, 7.33, 205.66, 0.13058, 5, 55, -775.19, -310.06, 0.01, 42, 270.17, 189.19, 0.57666, 46, 336.2, -430.99, 0.00071, 49, 240.98, -213.68, 0.22197, 43, 31.25, 203.81, 0.19066, 5, 55, -806.35, -329.64, 0.01, 42, 306.71, 184.75, 0.56122, 46, 368.13, -449.29, 0.00057, 49, 270.77, -235.3, 0.16146, 43, 68.05, 204.5, 0.26675, 5, 55, -828.15, -343.34, 0.01, 42, 332.27, 181.65, 0.55164, 46, 390.47, -462.1, 0.00053, 49, 291.62, -250.42, 0.14085, 43, 93.8, 204.99, 0.29698, 5, 55, -846.86, -355.1, 0.01, 42, 354.22, 178.99, 0.54892, 46, 409.65, -473.09, 0.00052, 49, 309.51, -263.39, 0.13289, 43, 115.89, 205.4, 0.30768, 5, 55, -852.27, -360.41, 0.01, 42, 361.75, 179.7, 0.54869, 46, 416.87, -475.37, 0.00051, 49, 316.44, -266.43, 0.13174, 43, 123.26, 207.15, 0.30906, 5, 55, -868.98, -362.41, 0.01, 42, 375.99, 170.72, 0.54828, 46, 426.49, -489.18, 0.00051, 49, 324.53, -281.19, 0.1306, 43, 138.61, 200.24, 0.31061, 5, 55, -874.6, -357.11, 0.01, 42, 377.02, 163.06, 0.54796, 46, 424.46, -496.64, 0.00051, 49, 321.71, -288.39, 0.13044, 43, 140.69, 192.8, 0.31109, 5, 55, -874.79, -340.76, 0.01, 42, 366.85, 150.25, 0.54483, 46, 410.1, -504.48, 0.00051, 49, 306.6, -294.65, 0.12989, 43, 132.4, 178.7, 0.31477, 5, 55, -873.57, -318.91, 0.01, 42, 352.14, 134.06, 0.5328, 46, 390.25, -513.67, 0.00049, 49, 285.88, -301.67, 0.12741, 43, 120.09, 160.62, 0.3293, 5, 55, -872.54, -300.5, 0.01, 42, 339.74, 120.41, 0.50957, 46, 373.51, -521.42, 0.00046, 49, 268.42, -307.58, 0.12043, 43, 109.71, 145.38, 0.35953, 5, 55, -871.44, -280.74, 0.01, 42, 326.43, 105.76, 0.45055, 46, 355.55, -529.73, 0.00038, 49, 249.67, -313.93, 0.10085, 43, 98.56, 129.02, 0.43822, 5, 55, -871.84, -272.95, 0.01, 42, 321.83, 99.46, 0.39737, 46, 348.87, -533.75, 0.00031, 49, 242.59, -317.2, 0.08392, 43, 94.89, 122.14, 0.5084, 5, 55, -875.45, -273.05, 0.01, 42, 324.7, 97.27, 0.36294, 46, 350.65, -536.89, 0.00028, 49, 244.03, -320.52, 0.07412, 43, 98.03, 120.37, 0.55266, 5, 55, -900.15, -277.56, 0.01, 42, 346.72, 85.2, 0.21179, 46, 366.24, -556.57, 0.00015, 49, 257.42, -341.75, 0.03651, 43, 121.51, 111.48, 0.74156, 5, 55, -926.79, -282.43, 0.01, 42, 370.46, 72.19, 0.12727, 46, 383.05, -577.8, 9e-05, 49, 271.87, -364.66, 0.01889, 43, 146.84, 101.9, 0.84375, 5, 55, -946.58, -286.74, 0.01, 42, 388.55, 63.06, 0.10083, 46, 396.15, -593.24, 7e-05, 49, 283.25, -381.41, 0.01364, 43, 166.02, 95.38, 0.87545, 5, 55, -959.6, -286.72, 0.01, 42, 398.65, 54.84, 0.09544, 46, 402.26, -604.74, 7e-05, 49, 288.09, -393.5, 0.01258, 43, 177.17, 88.64, 0.88192, 5, 55, -963.63, -282.89, 0.01, 42, 399.36, 49.33, 0.09486, 46, 400.77, -610.1, 6e-05, 49, 286.04, -398.67, 0.01247, 43, 178.64, 83.28, 0.8826, 5, 55, -965.57, -276.92, 0.01, 42, 397.1, 43.47, 0.09457, 46, 396.41, -614.62, 6e-05, 49, 281.22, -402.69, 0.01243, 43, 177.22, 77.17, 0.88293, 5, 55, -955.67, -262.79, 0.01, 42, 380.51, 38.74, 0.09306, 46, 379.29, -612.52, 6e-05, 49, 264.42, -398.78, 0.01251, 43, 161.45, 70.17, 0.88437, 5, 55, -946.2, -245.89, 0.01, 42, 362.51, 31.59, 0.06962, 46, 359.92, -612.09, 5e-05, 49, 245.21, -396.29, 0.00958, 43, 144.61, 60.59, 0.91075, 5, 55, -966.27, -248.53, 0.01, 42, 379.75, 20.99, 0.0213, 46, 371.68, -628.57, 1e-05, 49, 255.14, -413.92, 0.00269, 43, 163.16, 52.49, 0.966, 4, 55, -999.5, -257.61, 0.01, 42, 411.28, 7.09, 0.00128, 49, 275.95, -441.38, 0.00011, 43, 196.31, 43.12, 0.98861, 2, 55, -1024.43, -264.42, 0.01, 43, 221.18, 36.08, 0.99, 2, 55, -1042.72, -269.27, 0.01, 43, 239.35, 30.8, 0.99, 2, 55, -1066.55, -289.7, 0.01, 43, 270.3, 36, 0.99, 2, 55, -1074.67, -297.97, 0.01, 43, 281.53, 38.89, 0.99, 2, 55, -1087.64, -314.64, 0.01, 43, 301.24, 46.48, 0.99, 2, 55, -1097.08, -317.22, 0.01, 43, 310.66, 43.81, 0.99, 2, 55, -1104.69, -314.73, 0.01, 43, 315.89, 37.75, 0.99, 2, 55, -1110.83, -308.6, 0.01, 43, 317.99, 29.33, 0.99, 2, 55, -1110.55, -297.3, 0.01, 43, 311.91, 19.8, 0.99, 2, 55, -1103.75, -278.25, 0.01, 43, 296.25, 6.99, 0.99, 2, 55, -1097.09, -259.58, 0.01, 43, 280.92, -5.56, 0.99, 2, 55, -1095.51, -253.61, 0.01, 43, 276.48, -9.86, 0.99, 2, 55, -1089.05, -240.59, 0.01, 43, 264.23, -17.67, 0.99, 2, 55, -1083.38, -229.19, 0.01, 43, 253.49, -24.52, 0.99, 2, 55, -1076.72, -215.77, 0.01, 43, 240.86, -32.57, 0.99, 2, 55, -1078.05, -214.26, 0.01, 43, 241.22, -34.55, 0.99, 2, 55, -1072, -206.59, 0.01, 43, 232.08, -38, 0.99, 2, 55, -1065.78, -194.22, 0.01, 43, 220.37, -45.38, 0.99, 2, 55, -1063.56, -187.45, 0.01, 43, 214.97, -50.04, 0.99, 2, 55, -1050.66, -174.77, 0.01, 43, 197.38, -54.23, 0.99, 2, 55, -1036.83, -163.47, 0.01, 43, 179.7, -56.78, 0.99, 2, 55, -1020.91, -150.46, 0.01, 43, 159.35, -59.71, 0.99, 2, 55, -1009.57, -141.16, 0.01, 43, 144.84, -61.82, 0.99, 2, 55, -994.22, -128.58, 0.01, 43, 125.19, -64.67, 0.99, 2, 55, -974.55, -116.45, 0.01, 43, 102.09, -64.91, 0.99, 2, 55, -950.65, -101.72, 0.01, 43, 74.01, -65.19, 0.99, 2, 55, -938.61, -94.3, 0.01, 43, 59.87, -65.34, 0.99, 3, 55, -916.2, -83.84, 0.01, 42, 237.09, -75.32, 0.00133, 43, 35.28, -62.73, 0.98867, 3, 55, -899.4, -76, 0.01, 42, 219.1, -70.81, 0.03324, 43, 16.85, -60.77, 0.95676, 3, 55, -885.34, -69.43, 0.01, 42, 204.04, -67.06, 0.12218, 43, 1.41, -59.14, 0.86782, 3, 55, -871.07, -62.76, 0.01, 42, 188.76, -63.24, 0.29033, 43, -14.25, -57.49, 0.69967, 3, 55, -850.47, -51.53, 0.01, 42, 165.69, -58.97, 0.60876, 43, -37.69, -56.47, 0.38124, 4, 41, 278.25, -60.41, 5e-05, 55, -841.05, -45.94, 0.01, 42, 154.85, -57.38, 0.73502, 43, -48.65, -56.4, 0.25493, 4, 41, 256.02, -57.38, 0.00295, 55, -822.38, -33.5, 0.01, 42, 132.52, -55.27, 0.89564, 43, -71.05, -57.42, 0.09141, 4, 41, 236.94, -56.54, 0.01733, 55, -807.52, -21.5, 0.01, 42, 113.41, -55.22, 0.94293, 43, -89.98, -60.03, 0.02974, 4, 41, 210.88, -55.39, 0.08155, 55, -787.23, -5.11, 0.01, 42, 87.33, -55.15, 0.90529, 43, -115.82, -63.6, 0.00316, 4, 41, 194.98, -54.69, 0.17179, 55, -774.84, 4.89, 0.01, 42, 71.41, -55.11, 0.81805, 43, -131.59, -65.77, 0.00016, 3, 41, 168.53, -54.71, 0.455, 55, -755.03, 22.4, 0.01, 42, 44.99, -56.23, 0.535, 3, 41, 138.58, -54.73, 0.85748, 55, -732.59, 42.24, 0.01, 42, 15.07, -57.49, 0.13252, 2, 41, 102.11, -54.75, 0.99, 55, -705.27, 66.4, 0.01, 2, 41, 79.47, -49.56, 0.99, 55, -684.86, 77.49, 0.01, 2, 41, 58.59, -44.76, 0.99, 55, -666.03, 87.71, 0.01, 2, 41, 35.9, -39.55, 0.99, 55, -645.58, 98.82, 0.01, 2, 41, 19.22, -29.86, 0.99, 55, -626.66, 102.6, 0.01, 3, 41, -5.59, -15.44, 0.9888, 47, 7.97, -191.86, 0.0012, 55, -598.51, 108.21, 0.01, 4, 41, -18.24, -8.1, 0.96607, 47, 1.4, -178.79, 0.02338, 48, -74.41, -183.03, 0.00055, 55, -584.17, 111.08, 0.01, 4, 41, -25.89, -2.51, 0.93097, 47, -1.95, -169.93, 0.05594, 48, -78.32, -174.4, 0.00309, 55, -574.74, 111.95, 0.01, 4, 41, -46.45, 12.59, 0.78718, 47, -10.91, -146.05, 0.19087, 48, -88.8, -151.15, 0.01195, 55, -549.33, 114.24, 0.01, 4, 41, -55.92, 19.55, 0.71248, 47, -15.04, -135.04, 0.26259, 48, -93.63, -140.44, 0.01493, 55, -537.63, 115.29, 0.01], "hull": 145, "edges": [0, 288, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 258, 260, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 270, 272, 272, 274, 274, 276, 276, 278, 278, 280, 280, 282, 282, 284, 284, 286, 286, 288], "width": 188, "height": 102}, "archer23_1": {"type": "mesh", "uvs": [0.61167, 0, 0.62431, 0, 0.6245, 0.00041, 0.66147, 0.00741, 0.69851, 0.01442, 0.73268, 0.02089, 0.76455, 0.02692, 0.79889, 0.03342, 0.8382, 0.04087, 0.86736, 0.04639, 0.897, 0.04788, 0.92646, 0.04937, 0.95084, 0.0506, 0.98615, 0.05239, 0.9893, 0.07984, 0.99491, 0.12872, 0.99562, 0.17363, 0.99621, 0.21052, 0.99714, 0.26838, 0.99778, 0.30827, 0.99849, 0.35294, 0.99925, 0.40054, 1, 0.44712, 1, 0.47903, 1, 0.51853, 1, 0.55726, 1, 0.59833, 0.99631, 0.63652, 0.99294, 0.67148, 0.98931, 0.70904, 0.9856, 0.74749, 0.97898, 0.77834, 0.97171, 0.81222, 0.96341, 0.85093, 0.95739, 0.87899, 0.9506, 0.91063, 0.94416, 0.94065, 0.93386, 0.94838, 0.92261, 0.91214, 0.91716, 0.88215, 0.91074, 0.84681, 0.90079, 0.86261, 0.88441, 0.88859, 0.86581, 0.91811, 0.85296, 0.93219, 0.84147, 0.9448, 0.82227, 0.97235, 0.803, 1, 0.79294, 1, 0.77323, 0.99674, 0.75931, 0.9786, 0.7437, 0.95826, 0.72719, 0.93674, 0.70476, 0.90752, 0.69048, 0.89181, 0.67818, 0.87829, 0.65877, 0.85694, 0.64093, 0.84617, 0.61575, 0.83291, 0.60743, 0.83791, 0.58811, 0.85344, 0.57342, 0.87118, 0.55116, 0.89809, 0.53136, 0.92201, 0.51859, 0.94545, 0.49939, 0.98067, 0.48521, 0.98171, 0.47055, 0.95472, 0.46169, 0.93027, 0.45076, 0.90013, 0.4422, 0.87649, 0.43197, 0.84827, 0.42071, 0.81949, 0.41062, 0.79367, 0.40533, 0.78814, 0.38981, 0.80304, 0.36656, 0.82536, 0.35276, 0.83861, 0.33501, 0.85914, 0.31365, 0.88386, 0.29505, 0.90538, 0.28178, 0.92073, 0.27648, 0.9309, 0.25713, 0.93532, 0.25132, 0.92011, 0.2526, 0.87857, 0.25392, 0.83549, 0.25519, 0.79386, 0.25606, 0.76535, 0.25214, 0.75996, 0.23575, 0.76538, 0.21935, 0.77079, 0.20181, 0.77658, 0.17501, 0.78699, 0.15911, 0.78682, 0.15545, 0.76761, 0.16622, 0.73939, 0.17614, 0.70689, 0.15783, 0.71106, 0.13849, 0.7205, 0.11738, 0.73081, 0.10112, 0.73875, 0.07333, 0.75236, 0.05586, 0.78024, 0.04036, 0.80667, 0.02577, 0.84091, 0.01477, 0.84719, 0.00113, 0.83492, 0.00087, 0.80921, 0.00634, 0.7807, 0.01102, 0.75632, 0.01531, 0.73395, 0.01885, 0.71876, 0.02631, 0.68651, 0.03178, 0.66292, 0.03681, 0.64116, 0.04801, 0.60995, 0.05211, 0.58629, 0.06686, 0.56459, 0.0809, 0.54394, 0.09073, 0.52948, 0.10849, 0.50189, 0.11953, 0.48475, 0.13782, 0.46328, 0.15287, 0.44562, 0.169, 0.42668, 0.18404, 0.40903, 0.20843, 0.3892, 0.22905, 0.37245, 0.24016, 0.36342, 0.26377, 0.34077, 0.27443, 0.33022, 0.29198, 0.31125, 0.31011, 0.28808, 0.32821, 0.26117, 0.34605, 0.23467, 0.36002, 0.2139, 0.38074, 0.18014, 0.39851, 0.15117, 0.41522, 0.12392, 0.43298, 0.09498, 0.45615, 0.07214, 0.48071, 0.04795, 0.49659, 0.03231, 0.52999, 0.02001, 0.56734, 0.00627], "triangles": [89, 129, 130, 92, 93, 96, 96, 93, 95, 93, 94, 95, 91, 92, 97, 92, 96, 97, 91, 97, 90, 90, 97, 89, 89, 97, 127, 97, 126, 127, 98, 99, 120, 122, 97, 98, 98, 120, 121, 98, 121, 122, 122, 123, 97, 123, 124, 97, 124, 125, 97, 97, 125, 126, 127, 128, 89, 128, 129, 89, 101, 102, 114, 101, 116, 100, 101, 115, 116, 100, 116, 118, 116, 117, 118, 99, 100, 119, 100, 118, 119, 99, 119, 120, 105, 106, 108, 109, 104, 105, 108, 106, 107, 105, 108, 109, 109, 110, 104, 104, 110, 103, 110, 111, 103, 111, 112, 103, 103, 112, 102, 112, 113, 102, 114, 102, 113, 101, 114, 115, 138, 73, 137, 131, 89, 130, 131, 132, 74, 132, 133, 74, 133, 134, 74, 134, 135, 74, 135, 136, 74, 136, 137, 74, 83, 84, 82, 82, 84, 81, 80, 81, 85, 81, 84, 85, 79, 80, 86, 87, 78, 79, 86, 80, 85, 77, 78, 87, 88, 76, 77, 86, 87, 79, 75, 76, 88, 87, 88, 77, 74, 88, 131, 131, 88, 89, 74, 75, 88, 74, 137, 73, 66, 67, 65, 65, 67, 64, 67, 68, 64, 64, 68, 63, 68, 69, 63, 62, 63, 70, 63, 69, 70, 61, 62, 71, 62, 70, 71, 60, 61, 72, 73, 59, 60, 72, 61, 71, 7, 56, 57, 58, 59, 73, 57, 58, 4, 72, 73, 60, 138, 58, 73, 40, 55, 56, 41, 53, 54, 40, 54, 55, 46, 48, 50, 48, 49, 50, 37, 38, 36, 36, 38, 35, 38, 39, 35, 35, 39, 34, 42, 53, 41, 40, 41, 54, 39, 40, 34, 34, 40, 33, 31, 40, 9, 8, 40, 56, 33, 40, 32, 32, 40, 31, 31, 11, 30, 47, 48, 46, 50, 51, 46, 46, 51, 45, 51, 52, 45, 45, 52, 44, 52, 53, 44, 43, 44, 42, 42, 44, 53, 28, 24, 27, 26, 27, 25, 25, 27, 24, 23, 24, 28, 23, 28, 21, 21, 22, 23, 21, 28, 20, 29, 30, 18, 28, 29, 18, 11, 31, 10, 10, 31, 9, 18, 30, 11, 14, 12, 13, 18, 11, 12, 15, 16, 12, 15, 12, 14, 18, 12, 17, 20, 28, 19, 19, 28, 18, 12, 16, 17, 8, 56, 7, 7, 57, 6, 6, 57, 5, 5, 57, 4, 58, 3, 4, 9, 40, 8, 139, 141, 58, 139, 140, 141, 58, 141, 144, 144, 141, 142, 142, 143, 144, 144, 145, 58, 145, 0, 58, 58, 2, 3, 2, 0, 1, 2, 58, 0, 138, 139, 58], "vertices": [4, 41, -56.63, 17.71, 0.72212, 47, -16.64, -136.19, 0.23982, 48, -95.16, -141.69, 0.02806, 55, -538.31, 117.14, 0.01, 4, 41, -65.65, 25.44, 0.64762, 47, -19.97, -124.79, 0.30959, 48, -99.21, -130.53, 0.03279, 55, -526.44, 117.32, 0.01, 4, 41, -65.64, 25.71, 0.64648, 47, -19.82, -124.57, 0.31067, 48, -99.07, -130.29, 0.03285, 55, -526.26, 117.11, 0.01, 4, 41, -89.67, 51.02, 0.40337, 47, -26.11, -90.24, 0.55453, 48, -107.56, -96.44, 0.0321, 55, -491.5, 114.04, 0.01, 4, 41, -113.75, 76.38, 0.18539, 47, -32.41, -55.85, 0.79214, 48, -116.06, -62.52, 0.01247, 55, -456.66, 110.97, 0.01, 5, 41, -135.96, 99.76, 0.05228, 47, -38.22, -24.12, 0.9352, 48, -123.91, -31.24, 0.00047, 55, -424.54, 108.14, 0.01, 44, -47.43, -200.94, 0.00206, 5, 41, -156.68, 121.58, 0.00425, 47, -43.64, 5.47, 0.94307, 55, -394.56, 105.5, 0.01, 44, -44.78, -170.96, 0.03702, 45, -134.9, -173.04, 0.00566, 4, 47, -49.48, 37.35, 0.81527, 55, -362.28, 102.65, 0.01, 44, -41.92, -138.68, 0.14478, 45, -133.26, -140.67, 0.02995, 4, 47, -56.17, 73.86, 0.58735, 55, -325.31, 99.39, 0.01, 44, -38.65, -101.71, 0.34376, 45, -131.38, -103.61, 0.05888, 4, 47, -61.13, 100.93, 0.39268, 55, -297.89, 96.97, 0.01, 44, -36.22, -74.29, 0.53773, 45, -129.99, -76.12, 0.05959, 4, 47, -68.19, 127.86, 0.20606, 55, -270.05, 96.62, 0.01, 44, -35.85, -46.46, 0.74728, 45, -130.67, -48.29, 0.03667, 4, 47, -75.21, 154.63, 0.06835, 55, -242.38, 96.26, 0.01, 44, -35.49, -18.78, 0.91205, 45, -131.35, -20.62, 0.0096, 4, 47, -81.02, 176.79, 0.01211, 55, -219.48, 95.97, 0.01, 44, -35.19, 4.12, 0.97741, 45, -131.91, 2.27, 0.00048, 2, 55, -186.31, 95.54, 0.01, 44, -34.76, 37.28, 0.99, 2, 55, -183.15, 81.56, 0.01, 44, -20.77, 40.44, 0.99, 2, 55, -177.52, 56.66, 0.01, 44, 4.13, 46.06, 0.99, 2, 55, -176.51, 33.73, 0.01, 44, 27.06, 47.07, 0.99, 2, 55, -175.68, 14.88, 0.01, 44, 45.91, 47.89, 0.99, 2, 55, -174.37, -14.67, 0.01, 44, 75.46, 49.19, 0.99, 3, 55, -173.47, -35.04, 0.01, 44, 95.83, 50.08, 0.95001, 45, -2.71, 53.13, 0.03999, 3, 55, -172.47, -57.85, 0.01, 44, 118.64, 51.08, 0.70373, 45, 20.05, 54.99, 0.28627, 3, 55, -171.4, -82.16, 0.01, 44, 142.95, 52.14, 0.36369, 45, 44.3, 56.97, 0.62631, 3, 55, -170.35, -105.95, 0.01, 44, 166.75, 53.19, 0.14823, 45, 68.04, 58.9, 0.84177, 3, 55, -170.1, -122.21, 0.01, 44, 183, 53.42, 0.0724, 45, 84.28, 59.75, 0.9176, 4, 55, -169.73, -141.95, 0.01, 44, 202.74, 53.79, 0.02425, 45, 103.99, 60.87, 0.96123, 46, -96.69, 24.48, 0.00452, 4, 55, -169.05, -160.22, 0.01, 44, 221.01, 54.47, 0.00609, 45, 122.22, 62.23, 0.94496, 46, -80.88, 33.66, 0.03895, 4, 55, -167.34, -177.05, 0.01, 44, 237.84, 56.17, 0.00085, 45, 138.98, 64.56, 0.85884, 46, -66.82, 43.08, 0.1303, 4, 55, -167.05, -188.41, 0.01, 44, 249.2, 56.45, 7e-05, 45, 150.32, 65.27, 0.74605, 46, -56.93, 48.67, 0.24388, 3, 55, -163.99, -194.62, 0.01, 45, 156.41, 68.56, 0.66139, 46, -52.89, 54.29, 0.32861, 3, 55, -159.39, -201.99, 0.01, 45, 163.61, 73.43, 0.5742, 46, -48.54, 61.81, 0.4158, 3, 55, -155.87, -214.57, 0.01, 45, 176.04, 77.42, 0.46726, 46, -39.09, 70.84, 0.52274, 3, 55, -157.32, -227.93, 0.01, 45, 189.45, 76.47, 0.35618, 46, -26.62, 75.83, 0.63382, 3, 55, -159.61, -244.29, 0.01, 45, 205.88, 74.79, 0.21852, 46, -11.1, 81.49, 0.77148, 3, 55, -162.88, -264.23, 0.01, 45, 225.93, 72.26, 0.09334, 46, 8.04, 87.97, 0.89666, 3, 55, -165.52, -279.12, 0.01, 45, 240.91, 70.18, 0.03871, 46, 22.42, 92.64, 0.95129, 3, 55, -168.65, -296.11, 0.01, 45, 258, 67.69, 0.00765, 46, 38.89, 97.86, 0.98235, 2, 55, -171.69, -312.31, 0.01, 46, 54.62, 102.8, 0.99, 2, 55, -180.44, -318.02, 0.01, 46, 63.77, 97.75, 0.99, 3, 55, -194.31, -301.83, 0.01, 45, 264.67, 42.26, 0.0014, 46, 56, 77.9, 0.9886, 3, 55, -202.23, -287.75, 0.01, 45, 250.9, 33.82, 0.00802, 46, 47.29, 64.29, 0.98198, 3, 55, -211.57, -271.16, 0.01, 45, 234.67, 23.87, 0.01208, 46, 37.04, 48.25, 0.97792, 3, 55, -219.22, -280.84, 0.01, 45, 244.63, 16.59, 0.00101, 46, 49.18, 46.05, 0.98899, 2, 55, -231.81, -296.79, 0.01, 46, 69.17, 42.43, 0.99, 2, 55, -246.11, -314.9, 0.01, 46, 91.88, 38.31, 0.99, 2, 55, -256.6, -324.25, 0.01, 46, 105.05, 33.45, 0.99, 2, 55, -265.98, -332.61, 0.01, 46, 116.85, 29.1, 0.99, 3, 55, -281.02, -349.84, 0.01, 46, 139.13, 23.92, 0.98998, 49, 93.65, 259.68, 2e-05, 3, 55, -296.12, -367.14, 0.01, 46, 161.49, 18.72, 0.98984, 49, 115.33, 252.12, 0.00016, 4, 48, 288.94, 191.81, 1e-05, 55, -348.98, -354.99, 0.01, 46, 175.6, -33.65, 0.91015, 49, 123.76, 198.54, 0.07984, 3, 55, -353.02, -359.3, 0.01, 46, 181.3, -35.19, 0.90141, 49, 129.27, 196.4, 0.08859, 4, 48, 291.17, 175.36, 0.00019, 55, -365.25, -351.7, 0.01, 46, 180.34, -49.56, 0.86587, 49, 126.78, 182.22, 0.12394, 5, 48, 287.49, 157.55, 0.00122, 55, -380.87, -342.39, 0.01, 45, 312.17, -142.66, 4e-05, 46, 179.47, -67.72, 0.79172, 49, 123.97, 164.26, 0.19702, 6, 48, 283.23, 137.42, 0.00304, 55, -398.5, -331.78, 0.01, 45, 302.22, -160.67, 0.00061, 46, 178.38, -88.27, 0.68815, 49, 120.7, 143.94, 0.2982, 42, -8.59, 443.47, 1e-05, 6, 48, 276.66, 108.93, 0.00505, 55, -423.27, -316.24, 0.01, 45, 287.62, -186, 0.00196, 46, 176.3, -117.44, 0.51824, 49, 115.51, 115.16, 0.46475, 42, 0.85, 415.79, 1e-05, 6, 48, 273.43, 91.23, 0.00456, 55, -438.94, -307.39, 0.01, 45, 279.36, -201.99, 0.00222, 46, 175.85, -135.42, 0.40948, 49, 113.14, 97.33, 0.57372, 42, 7.44, 399.04, 1e-05, 6, 48, 270.44, 76.04, 0.00316, 55, -452.3, -299.59, 0.01, 45, 272.06, -215.63, 0.00198, 46, 175.25, -150.89, 0.31947, 49, 110.89, 82.02, 0.66538, 42, 12.9, 384.56, 1e-05, 6, 48, 265.34, 52.2, 0.00043, 55, -473.16, -286.97, 0.01, 45, 260.23, -236.95, 0.00094, 46, 173.91, -175.23, 0.182, 49, 106.95, 57.96, 0.80662, 42, 21.13, 361.62, 1e-05, 5, 55, -491.5, -280.17, 0.01, 45, 254.11, -255.53, 0.00012, 46, 176.52, -194.62, 0.0882, 49, 107.48, 38.4, 0.90167, 42, 31.09, 344.78, 1e-05, 4, 55, -516.44, -272.28, 0.01, 46, 181.28, -220.34, 0.01097, 49, 109.46, 12.32, 0.97902, 42, 45.48, 322.94, 1e-05, 3, 55, -524.35, -274.77, 0.01, 46, 187.19, -226.15, 0.00168, 49, 114.72, 5.91, 0.98832, 4, 55, -542.37, -282.95, 0.01, 46, 202.88, -238.21, 0.00016, 49, 129.02, -7.76, 0.97164, 42, 72.33, 314.88, 0.0182, 4, 55, -555.96, -292.25, 0.01, 46, 217.48, -245.84, 0.00026, 49, 142.73, -16.9, 0.9397, 42, 88.75, 313.54, 0.05004, 4, 55, -576.55, -306.37, 0.01, 46, 239.61, -257.38, 0.00033, 49, 163.5, -30.74, 0.89147, 42, 113.63, 311.52, 0.0982, 4, 55, -594.84, -318.93, 0.01, 46, 259.3, -267.62, 0.00036, 49, 181.98, -43.03, 0.85577, 42, 135.75, 309.75, 0.13386, 4, 55, -606.59, -331.12, 0.01, 46, 275.58, -272.27, 0.00038, 49, 197.67, -49.39, 0.83858, 42, 152.56, 311.8, 0.15104, 4, 55, -624.31, -349.41, 0.01, 46, 300.05, -279.32, 0.00039, 49, 221.25, -59.01, 0.82628, 42, 177.84, 314.83, 0.16333, 4, 55, -637.6, -350.15, 0.01, 46, 306.94, -290.7, 0.00039, 49, 226.89, -71.07, 0.8226, 42, 188.62, 307.03, 0.16701, 4, 55, -651.48, -336.61, 0.01, 46, 301.51, -309.32, 0.00038, 49, 219.5, -89, 0.80891, 42, 190.87, 287.77, 0.18071, 4, 55, -659.85, -324.31, 0.01, 46, 294.59, -322.48, 0.00036, 49, 211.21, -101.35, 0.7873, 42, 189.62, 272.95, 0.20233, 5, 55, -670.03, -309.25, 0.01, 46, 286.08, -338.55, 0.00033, 49, 201.03, -116.41, 0.74565, 42, 188.03, 254.83, 0.24401, 43, -59.23, 257.39, 1e-05, 6, 48, 342.36, -137.79, 0.00018, 55, -677.89, -297.52, 0.01, 46, 279.42, -351, 0.0003, 49, 193.08, -128.08, 0.70156, 42, 186.74, 240.78, 0.28776, 43, -58.55, 243.29, 0.00019, 6, 48, 332.29, -150.93, 0.00087, 55, -687.01, -283.71, 0.01, 46, 271.51, -365.54, 0.00027, 49, 183.66, -141.69, 0.6361, 42, 185.11, 224.31, 0.35172, 43, -57.87, 226.75, 0.00105, 6, 48, 322.41, -164.44, 0.00199, 55, -696.55, -269.95, 0.01, 46, 263.84, -380.42, 0.00022, 49, 174.44, -155.67, 0.55104, 42, 183.84, 207.61, 0.43299, 43, -56.81, 210.04, 0.00376, 6, 48, 313.79, -175.09, 0.0027, 55, -703.79, -258.32, 0.01, 46, 256.98, -392.27, 0.00018, 49, 166.35, -166.72, 0.45124, 42, 182.13, 194.02, 0.52605, 43, -56.61, 196.34, 0.00984, 6, 48, 313.14, -178.85, 0.00258, 55, -707.12, -256.48, 0.01, 46, 256.92, -396.08, 0.00016, 49, 165.89, -170.5, 0.4108, 42, 183.56, 190.49, 0.56288, 43, -54.7, 193.05, 0.01358, 6, 48, 326.05, -184.99, 0.00119, 55, -717.16, -266.66, 0.01, 46, 270.63, -400.16, 0.00013, 49, 179.08, -176.02, 0.32329, 42, 197.77, 192.07, 0.63779, 43, -40.85, 196.59, 0.0276, 6, 48, 345.17, -196.08, 0.0002, 55, -733.9, -281.1, 0.01, 46, 291.24, -408.15, 0.00011, 49, 198.72, -186.17, 0.25229, 42, 219.87, 192.73, 0.69067, 43, -19.06, 200.32, 0.04673, 6, 48, 356.45, -203.29, 2e-05, 55, -744.4, -289.39, 0.01, 46, 303.49, -413.52, 0.0001, 49, 210.32, -192.82, 0.2242, 42, 233.24, 192.55, 0.7077, 43, -5.79, 202, 0.05798, 5, 55, -758.49, -301.49, 0.01, 46, 320.79, -420.28, 9e-05, 49, 226.81, -201.38, 0.19609, 42, 251.81, 193.06, 0.72224, 43, 12.53, 205.09, 0.07158, 5, 55, -776.51, -315.46, 0.01, 46, 341.6, -429.62, 8e-05, 49, 246.49, -212.89, 0.17492, 42, 274.61, 192.55, 0.73305, 43, 35.17, 207.76, 0.08194, 5, 55, -792.95, -327.2, 0.01, 46, 359.68, -438.62, 8e-05, 49, 263.51, -223.78, 0.16471, 42, 294.77, 191.3, 0.73974, 43, 55.32, 209.32, 0.08547, 5, 55, -804.98, -335.4, 0.01, 46, 372.57, -445.38, 8e-05, 49, 275.61, -231.88, 0.16092, 42, 309.28, 190.09, 0.74346, 43, 69.85, 210.14, 0.08553, 5, 55, -809.8, -340.71, 0.01, 46, 379.53, -447.14, 8e-05, 49, 282.34, -234.37, 0.16002, 42, 316.37, 191.17, 0.74455, 43, 76.72, 212.2, 0.08535, 5, 55, -827.86, -343.29, 0.01, 46, 390.28, -461.87, 8e-05, 49, 291.45, -250.17, 0.15898, 42, 332.01, 181.79, 0.74548, 43, 93.52, 205.09, 0.08546, 5, 55, -833.41, -335.6, 0.01, 46, 386.11, -470.38, 8e-05, 49, 286.4, -258.18, 0.15879, 42, 331.48, 172.33, 0.74508, 43, 94.31, 195.64, 0.08606, 5, 55, -832.46, -314.4, 0.01, 46, 366.95, -479.51, 8e-05, 49, 266.36, -265.21, 0.15799, 42, 317.38, 156.46, 0.73889, 43, 82.55, 177.97, 0.09305, 5, 55, -831.19, -292.57, 0.01, 46, 347.08, -488.65, 8e-05, 49, 245.64, -272.17, 0.15382, 42, 302.64, 140.31, 0.72221, 43, 70.2, 159.93, 0.11389, 5, 55, -829.33, -271.83, 0.01, 46, 327.9, -496.74, 7e-05, 49, 225.7, -278.17, 0.14122, 42, 288.12, 125.39, 0.68805, 43, 57.9, 143.13, 0.16066, 5, 55, -827.09, -258.18, 0.01, 46, 314.8, -501.18, 6e-05, 49, 212.2, -281.18, 0.11752, 42, 277.77, 116.2, 0.63009, 43, 48.93, 132.59, 0.24233, 5, 55, -829.76, -256.07, 0.01, 46, 314.19, -504.53, 5e-05, 49, 211.24, -284.45, 0.10538, 42, 278.52, 112.88, 0.59829, 43, 50.13, 129.41, 0.28628, 5, 55, -843.23, -260.13, 0.01, 46, 324.1, -514.52, 4e-05, 49, 220.03, -295.44, 0.07339, 42, 291.54, 107.54, 0.49522, 43, 63.77, 125.93, 0.42135, 5, 55, -857.67, -263.64, 0.01, 46, 333.99, -525.61, 3e-05, 49, 228.67, -307.52, 0.05361, 42, 304.96, 101.17, 0.41578, 43, 77.94, 121.49, 0.52058, 5, 55, -873.52, -267.16, 0.01, 46, 344.54, -537.94, 2e-05, 49, 237.85, -320.92, 0.03985, 42, 319.49, 93.91, 0.35067, 43, 93.34, 116.32, 0.59945, 5, 55, -898.25, -273.05, 0.01, 46, 361.36, -557.01, 2e-05, 49, 252.53, -341.67, 0.03007, 42, 342.4, 82.9, 0.30035, 43, 117.56, 108.6, 0.65956, 5, 55, -913.13, -273.21, 0.01, 46, 368.5, -570.07, 2e-05, 49, 258.23, -355.42, 0.02867, 42, 354.05, 73.65, 0.29298, 43, 130.39, 101.06, 0.66834, 5, 55, -916.71, -263.45, 0.01, 46, 361.56, -577.82, 2e-05, 49, 250.5, -362.38, 0.02854, 42, 350.68, 63.81, 0.29186, 43, 128.42, 90.85, 0.66958, 5, 55, -906.81, -248.88, 0.01, 46, 344.05, -575.92, 2e-05, 49, 233.3, -358.62, 0.02825, 42, 333.81, 58.74, 0.28441, 43, 112.42, 83.48, 0.67732, 5, 55, -897.59, -232.22, 0.01, 46, 325.01, -575.61, 1e-05, 49, 214.4, -356.28, 0.022, 42, 316.15, 51.61, 0.22421, 43, 95.92, 73.97, 0.74378, 5, 55, -914.4, -234.8, 0.01, 46, 335.19, -589.24, 1e-05, 49, 223.06, -370.92, 0.0089, 42, 330.83, 43.02, 0.10052, 43, 111.65, 67.5, 0.88058, 4, 55, -932.33, -239.98, 0.01, 49, 234.55, -385.62, 0.00323, 42, 348.02, 35.74, 0.04074, 43, 129.68, 62.69, 0.94603, 4, 55, -951.99, -245.59, 0.01, 49, 247.08, -401.77, 0.00077, 42, 366.81, 27.71, 0.01188, 43, 149.41, 57.34, 0.97735, 4, 55, -967.14, -249.9, 0.01, 49, 256.74, -414.22, 0.00014, 42, 381.29, 21.51, 0.00321, 43, 164.61, 53.22, 0.98664, 2, 55, -1004.68, -259.01, 0.01, 43, 201.47, 41.64, 0.99, 2, 55, -1064.06, -266.54, 0.01, 43, 256.21, 17.44, 0.99, 2, 55, -1083.76, -268.93, 0.01, 43, 274.33, 9.33, 0.99, 2, 55, -1105.12, -275.06, 0.01, 43, 295.79, 3.56, 0.99, 2, 55, -1115.37, -271.6, 0.01, 43, 302.78, -4.7, 0.99, 2, 55, -1122.07, -259, 0.01, 43, 302.01, -18.94, 0.99, 2, 55, -1114.55, -248.23, 0.01, 43, 290.02, -24.3, 0.99, 2, 55, -1101.85, -239.44, 0.01, 43, 274.6, -25.26, 0.99, 2, 55, -1090.98, -231.93, 0.01, 43, 261.41, -26.09, 0.99, 2, 55, -1081.01, -225.04, 0.01, 43, 249.32, -26.84, 0.99, 2, 55, -1073.76, -220.7, 0.01, 43, 240.87, -26.82, 0.99, 2, 55, -1058.41, -211.47, 0.01, 43, 222.96, -26.81, 0.99, 2, 55, -1047.19, -204.72, 0.01, 43, 209.86, -26.79, 0.99, 2, 55, -1036.83, -198.49, 0.01, 43, 197.78, -26.78, 0.99, 2, 55, -1029.52, -186.6, 0.01, 43, 185.37, -33.2, 0.99, 2, 55, -1014.24, -172.71, 0.01, 43, 165.12, -37.21, 0.99, 2, 55, -1000.56, -161.42, 0.01, 43, 147.58, -39.82, 0.99, 2, 55, -987.53, -150.68, 0.01, 43, 130.88, -42.3, 0.99, 2, 55, -978.42, -143.15, 0.01, 43, 119.18, -44.03, 0.99, 2, 55, -961.94, -128.81, 0.01, 43, 97.67, -47.82, 0.99, 2, 55, -951.71, -119.9, 0.01, 43, 84.31, -50.17, 0.99, 2, 55, -934.7, -108.68, 0.01, 43, 63.94, -51, 0.99, 2, 55, -920.7, -99.45, 0.01, 43, 47.19, -51.68, 0.99, 3, 55, -905.69, -89.55, 0.01, 42, 232.52, -64.26, 0.00267, 43, 29.23, -52.42, 0.98733, 3, 55, -891.71, -80.32, 0.01, 42, 215.85, -62.61, 0.04544, 43, 12.48, -53.1, 0.94456, 3, 55, -869.83, -70.19, 0.01, 42, 192.48, -56.69, 0.28675, 43, -11.48, -50.48, 0.70325, 3, 55, -863.89, -66.49, 0.01, 42, 185.53, -55.82, 0.40911, 43, -18.48, -50.59, 0.58089, 3, 55, -872.25, -68.97, 0.01, 42, 193.59, -59.16, 0.36795, 43, -10.03, -52.78, 0.62205, 3, 55, -862.39, -61.77, 0.01, 42, 181.4, -58.54, 0.44748, 43, -22.2, -53.86, 0.54252, 3, 55, -852.46, -56.24, 0.01, 42, 170.2, -56.58, 0.59774, 43, -33.56, -53.47, 0.39226, 3, 55, -836.13, -46.3, 0.01, 42, 151.26, -53.99, 0.80567, 43, -52.68, -53.55, 0.18433, 4, 41, 254.17, -54.8, 0.00047, 55, -819.28, -34.21, 0.01, 42, 130.56, -52.76, 0.92709, 43, -73.35, -55.21, 0.06244, 4, 41, 232.31, -54.17, 0.00948, 55, -802.48, -20.21, 0.01, 42, 108.69, -53.04, 0.96716, 43, -94.96, -58.53, 0.01336, 4, 41, 210.78, -53.56, 0.04722, 55, -785.94, -6.43, 0.01, 42, 87.16, -53.32, 0.94163, 43, -116.25, -61.8, 0.00116, 3, 41, 193.91, -53.07, 0.12087, 55, -772.97, 4.38, 0.01, 42, 70.28, -53.54, 0.86913, 3, 41, 167.91, -53.52, 0.36161, 55, -753.78, 21.92, 0.01, 42, 44.33, -55.06, 0.62839, 3, 41, 145.61, -53.9, 0.67336, 55, -737.31, 36.96, 0.01, 42, 22.06, -56.36, 0.31664, 3, 41, 124.63, -54.25, 0.91078, 55, -721.82, 51.11, 0.01, 42, 1.11, -57.59, 0.07922, 3, 41, 102.35, -54.63, 0.98737, 55, -705.37, 66.15, 0.01, 42, -21.13, -58.89, 0.00263, 2, 41, 78.23, -49.33, 0.99, 55, -683.78, 78.13, 0.01, 2, 41, 52.68, -43.71, 0.99, 55, -660.91, 90.83, 0.01, 2, 41, 36.16, -40.08, 0.99, 55, -646.12, 99.05, 0.01, 2, 41, 8.26, -24.44, 0.99, 55, -614.85, 105.79, 0.01, 4, 41, -22.94, -6.94, 0.9515, 47, -1.91, -175.25, 0.03558, 48, -77.94, -179.72, 0.00293, 55, -579.88, 113.33, 0.01], "hull": 146, "edges": [0, 290, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 258, 260, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 270, 272, 272, 274, 274, 276, 276, 278, 278, 280, 280, 282, 282, 284, 284, 286, 286, 288, 288, 290], "width": 188, "height": 102}}, "archer24": {"archer24": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 18, 589.16, 539.54, 0.99, 55, 826.96, -518.08, 0.01, 2, 18, 18.09, -358.23, 0.99, 55, -237.04, -518.08, 0.01, 2, 18, -489.86, -35.12, 0.99, 55, -237.04, 83.92, 0.01, 2, 18, 81.21, 862.64, 0.99, 55, 826.96, 83.93, 0.01], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 213, "height": 120}}, "archer25": {"archer20": {"type": "mesh", "uvs": [0.60196, 0.0468, 0.67125, 0.13246, 0.71647, 0.23796, 0.7532, 0.33725, 0.78145, 0.41793, 0.78566, 0.47129, 0.75733, 0.51347, 0.79695, 0.52094, 0.79267, 0.57926, 0.79688, 0.6289, 0.79403, 0.6624, 0.71335, 0.67849, 0.71758, 0.69835, 0.8209, 0.69096, 0.92278, 0.71211, 0.95569, 0.80045, 0.97277, 0.88057, 1, 0.8876, 1, 1, 0.79223, 1, 0.45644, 1, 0.22801, 1, 0.08753, 1, 0.04073, 0.96121, 0.08877, 0.85809, 0.10937, 0.789, 0.12767, 0.74594, 0.12884, 0.70588, 0.07631, 0.69284, 0.06492, 0.64376, 0.0661, 0.58369, 0.08557, 0.50757, 0.11528, 0.49958, 0.06048, 0.45649, 0.02969, 0.38737, 0, 0.30624, 0, 0.17802, 0.03441, 0.09366, 0.12078, 0.04034, 0.24394, 0, 0.34159, 0, 0.43641, 0, 0.52133, 0, 0.63378, 0.54391, 0.49899, 0.56687, 0.36993, 0.56981, 0.24887, 0.55773, 0.17921, 0.53566, 0.2579, 0.70595, 0.41209, 0.70803, 0.57085, 0.70711], "triangles": [50, 11, 12, 22, 23, 24, 21, 25, 48, 24, 25, 21, 22, 24, 21, 20, 49, 50, 21, 49, 20, 19, 12, 13, 15, 19, 13, 15, 13, 14, 19, 15, 16, 50, 12, 19, 20, 50, 19, 16, 17, 18, 19, 16, 18, 1, 45, 46, 32, 30, 31, 30, 32, 47, 6, 43, 4, 8, 6, 7, 9, 11, 8, 8, 11, 43, 8, 43, 6, 10, 11, 9, 30, 47, 29, 28, 29, 47, 27, 47, 46, 27, 28, 47, 27, 46, 48, 50, 44, 43, 50, 43, 11, 49, 44, 50, 26, 27, 48, 48, 25, 26, 49, 21, 48, 3, 43, 44, 44, 45, 2, 48, 46, 45, 49, 45, 44, 48, 45, 49, 38, 35, 36, 38, 34, 35, 36, 37, 38, 32, 33, 34, 34, 39, 32, 4, 43, 3, 38, 39, 34, 40, 47, 32, 0, 1, 41, 3, 44, 2, 39, 40, 32, 46, 47, 40, 41, 42, 0, 46, 40, 41, 41, 1, 46, 2, 45, 1, 6, 4, 5], "vertices": [2, 72, 8.65, 120.8, 0.99986, 73, -216.87, 76.26, 0.00014, 2, 72, 54.83, 141.13, 0.98942, 73, -176.1, 105.97, 0.01058, 2, 72, 108.86, 149.43, 0.92931, 73, -125.09, 125.62, 0.07069, 3, 72, 160.24, 154.01, 0.78181, 73, -75.87, 141.07, 0.21517, 74, -97.5, 212.5, 0.00302, 3, 72, 202.28, 155.89, 0.60672, 73, -35.2, 151.88, 0.37254, 74, -56.56, 202.78, 0.02073, 3, 72, 228.47, 150.8, 0.49989, 73, -8.53, 152.49, 0.45778, 74, -32.77, 190.7, 0.04233, 3, 72, 246.3, 132.84, 0.34384, 73, 12.73, 138.76, 0.56117, 74, -20.55, 168.54, 0.09499, 3, 72, 254.08, 147.14, 0.27852, 73, 17.27, 154.39, 0.59765, 74, -9.15, 180.16, 0.12383, 3, 72, 281.02, 137.54, 0.2006, 73, 45.64, 150.77, 0.6209, 74, 14.13, 163.55, 0.1785, 3, 72, 304.64, 132.47, 0.13743, 73, 69.8, 150.85, 0.61712, 74, 35.46, 152.2, 0.24546, 3, 72, 319.93, 127.14, 0.12051, 73, 85.87, 148.91, 0.60898, 74, 48.7, 142.88, 0.27051, 3, 72, 319.26, 91.89, 0.0585, 73, 92.75, 114.33, 0.47719, 74, 38.39, 109.16, 0.46431, 3, 72, 328.91, 91.06, 0.02546, 73, 102.35, 115.58, 0.32722, 74, 47.44, 105.72, 0.64732, 3, 72, 336.08, 134, 0.00089, 73, 100.19, 159.07, 0.1074, 74, 66.12, 145.05, 0.89171, 2, 73, 111.67, 201.52, 0.05739, 74, 96.32, 177.01, 0.94261, 2, 73, 154.41, 213.98, 0.03024, 74, 139.87, 167.77, 0.96976, 2, 73, 192.99, 219.93, 0.00742, 74, 176.67, 154.75, 0.99258, 2, 73, 196.72, 231.25, 0.00448, 74, 185.31, 162.97, 0.99552, 1, 74, 231.91, 135.99, 1, 2, 73, 247.75, 142.33, 5e-05, 74, 188.19, 60.48, 0.99995, 2, 73, 243.27, 1.38, 0.00321, 74, 117.54, -61.57, 0.99679, 2, 73, 240.22, -94.53, 0.14654, 74, 69.47, -144.62, 0.85346, 2, 73, 238.34, -153.5, 0.20501, 74, 39.92, -195.68, 0.79499, 2, 73, 219.14, -172.56, 0.21476, 74, 13.98, -203.39, 0.78524, 3, 72, 338.52, -183.73, 0.00237, 73, 170.41, -150.82, 0.30173, 74, -18.66, -161.18, 0.6959, 3, 72, 308.54, -167.21, 0.01668, 73, 137.6, -141.08, 0.44169, 74, -42.96, -137.07, 0.54163, 3, 72, 290.38, -154.13, 0.04418, 73, 117.06, -132.19, 0.5643, 74, -56.84, -119.52, 0.39152, 3, 72, 271.88, -147.55, 0.10137, 73, 97.58, -129.71, 0.68486, 74, -72.83, -108.12, 0.21377, 3, 72, 260.64, -166.39, 0.14849, 73, 90.63, -150.51, 0.72585, 74, -88.8, -123.15, 0.12566, 3, 72, 236.94, -164.53, 0.1881, 73, 67.07, -153.76, 0.72567, 74, -111.08, -114.87, 0.08623, 3, 72, 209.83, -155.78, 0.27433, 73, 38.73, -151, 0.68464, 74, -134.75, -99.02, 0.04103, 3, 72, 177.69, -137.77, 0.42862, 73, 3.48, -140.26, 0.56032, 74, -160.72, -72.89, 0.01107, 3, 72, 177.55, -124.17, 0.52419, 73, 0.44, -127, 0.47052, 74, -157.12, -59.76, 0.00529, 3, 72, 154.4, -141.39, 0.73535, 73, -18.5, -148.77, 0.2646, 74, -184.11, -69.98, 5e-05, 2, 72, 120.33, -147.15, 0.85171, 73, -50.56, -161.67, 0.14829, 2, 72, 80.28, -151.61, 0.93477, 73, -88.73, -174.58, 0.06523, 2, 72, 20.24, -139.45, 0.98906, 73, -149.98, -175.52, 0.01094, 2, 72, -16.5, -117.3, 0.99896, 73, -190.6, -161.73, 0.00104, 1, 72, -34.37, -76.69, 1, 1, 72, -43.09, -22.16, 1, 1, 72, -34.99, 18.04, 1, 1, 72, -27.12, 57.08, 1, 1, 72, -20.06, 92.04, 1, 3, 72, 249.09, 79.52, 0.24292, 73, 26.84, 87.26, 0.65621, 74, -32.48, 116.5, 0.10087, 3, 72, 246.6, 24.33, 0.05015, 73, 36.19, 32.82, 0.91742, 74, -50.01, 64.11, 0.03243, 2, 72, 232.63, -27.48, 0.06783, 73, 33.6, -20.79, 0.93217, 3, 72, 215.46, -76.21, 0.31001, 73, 27.23, -72.05, 0.6837, 74, -107.52, -24.04, 0.00629, 3, 72, 199.36, -102.32, 0.41114, 73, 17.08, -101, 0.58021, 74, -130.16, -44.73, 0.00865, 3, 72, 284.69, -95.66, 0.04591, 73, 99.02, -76.28, 0.71819, 74, -46.28, -61.73, 0.2359, 3, 72, 301.38, -31.83, 0.00042, 73, 101.7, -10.35, 0.95183, 74, -12.73, -4.92, 0.04776, 3, 72, 318.03, 30.5, 0.00466, 73, 104.65, 54.09, 0.43731, 74, 20.37, 50.46, 0.55803], "hull": 43, "edges": [0, 84, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84], "width": 84, "height": 96}}, "barb3": {"barb3": {"type": "mesh", "uvs": [0.48802, 0, 0.50061, 0, 0.59885, 0.01589, 0.96962, 0.29427, 1, 0.41002, 1, 0.4852, 0.97243, 0.56327, 0.648, 0.39193, 0.59667, 0.5952, 0.63248, 0.96649, 0.54953, 1, 0.49787, 1, 0.37678, 0.97975, 0.38653, 0.88621, 0.39685, 0.47364, 0.23831, 0.34169, 0, 0.39732, 0, 0.36942, 0.0558, 0.2423, 0.35271, 0.02888], "triangles": [15, 18, 19, 17, 18, 15, 7, 2, 3, 16, 17, 15, 0, 1, 2, 19, 0, 2, 19, 2, 7, 15, 19, 7, 7, 3, 4, 14, 15, 7, 7, 4, 5, 6, 7, 5, 8, 14, 7, 13, 14, 8, 13, 8, 9, 11, 12, 13, 13, 10, 11, 9, 10, 13], "vertices": [1, 71, 108.57, -3.47, 1, 1, 71, 108.51, -4.69, 1, 1, 71, 106.07, -14.12, 1, 1, 71, 68.68, -48.55, 1, 1, 71, 53.64, -50.87, 1, 1, 71, 43.95, -50.47, 1, 1, 71, 34, -47.38, 1, 1, 71, 57.4, -16.86, 1, 1, 71, 31.41, -10.79, 1, 1, 71, -16.59, -12.25, 1, 1, 71, -20.57, -4.03, 1, 1, 71, -20.36, 0.97, 1, 1, 71, -17.26, 12.6, 1, 1, 71, -5.24, 11.15, 1, 1, 71, 47.89, 7.92, 1, 1, 71, 65.54, 22.58, 1, 1, 71, 59.33, 45.97, 1, 1, 71, 62.93, 45.82, 1, 1, 71, 79.09, 39.73, 1, 1, 71, 105.39, 9.8, 1], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38], "width": 116, "height": 155}}, "img_v2_3b71fc62-e05e-495d-9d1b-f4ce67b9a12g52": {"img_v2_3b71fc62-e05e-495d-9d1b-f4ce67b9a12g": {"type": "mesh", "uvs": [0.59098, 0.08682, 0.67627, 0.12492, 0.69487, 0.24819, 0.76155, 0.3356, 0.84529, 0.37146, 0.92592, 0.43646, 1, 0.52387, 1, 0.6068, 0.94918, 0.70317, 0.84994, 0.77713, 0.74915, 0.86902, 0.67317, 0.9385, 0.58478, 1, 0.45142, 1, 0.38475, 0.90713, 0.30529, 0.83372, 0.23433, 0.76817, 0.17501, 0.71281, 0.11183, 0.65386, 0.05027, 0.56996, 0, 0.50146, 0, 0.36026, 0.07772, 0.24819, 0.18937, 0.20113, 0.28861, 0.12941, 0.35994, 0.06441, 0.42041, 0, 0.5057, 0, 0.49174, 0.84885, 0.4018, 0.76368, 0.31807, 0.683, 0.23278, 0.60904, 0.13199, 0.49697, 0.07617, 0.40284, 0.21418, 0.34457, 0.2731, 0.42525, 0.31342, 0.49922, 0.37079, 0.57094, 0.45918, 0.63369, 0.55997, 0.7211, 0.65456, 0.77937, 0.55532, 0.90712, 0.7569, 0.7211, 0.63285, 0.58887, 0.53516, 0.51042, 0.44367, 0.44767, 0.3925, 0.38715, 0.35684, 0.31991, 0.31342, 0.23699, 0.41731, 0.18096, 0.47468, 0.26164, 0.5243, 0.3244, 0.60184, 0.40284, 0.69953, 0.48577, 0.79877, 0.59783, 0.88095, 0.65834, 0.91352, 0.53283, 0.82048, 0.47456, 0.73519, 0.40956, 0.65611, 0.34681, 0.59408, 0.25492, 0.54291, 0.18544, 0.48399, 0.10924], "triangles": [23, 24, 48, 34, 23, 48, 35, 34, 48, 35, 48, 47, 36, 35, 46, 37, 36, 46, 31, 32, 35, 31, 35, 36, 18, 32, 31, 30, 36, 37, 31, 36, 30, 17, 18, 31, 16, 31, 30, 17, 31, 16, 22, 23, 34, 33, 21, 22, 33, 22, 34, 32, 33, 34, 32, 34, 35, 20, 21, 33, 19, 20, 33, 32, 19, 33, 18, 19, 32, 60, 0, 1, 60, 1, 2, 61, 0, 60, 51, 50, 61, 51, 61, 60, 59, 60, 2, 52, 60, 59, 51, 60, 52, 62, 26, 27, 62, 27, 0, 49, 25, 26, 49, 26, 62, 61, 62, 0, 50, 49, 62, 50, 62, 61, 47, 49, 50, 48, 24, 25, 48, 25, 49, 47, 48, 49, 46, 47, 50, 35, 47, 46, 45, 46, 50, 45, 50, 51, 45, 51, 44, 37, 46, 45, 44, 51, 52, 57, 3, 4, 57, 4, 5, 56, 57, 5, 56, 5, 6, 54, 57, 56, 54, 53, 57, 56, 6, 7, 55, 54, 56, 8, 55, 56, 7, 8, 56, 42, 54, 55, 9, 55, 8, 59, 2, 3, 58, 59, 3, 58, 3, 57, 53, 59, 58, 52, 59, 53, 43, 52, 53, 57, 53, 58, 54, 43, 53, 42, 43, 54, 9, 42, 55, 10, 42, 9, 37, 45, 38, 44, 52, 43, 38, 45, 44, 39, 44, 43, 38, 44, 39, 40, 43, 42, 39, 43, 40, 10, 40, 42, 11, 40, 10, 28, 38, 39, 41, 28, 39, 41, 39, 40, 41, 40, 11, 13, 14, 28, 13, 28, 41, 12, 41, 11, 13, 41, 12, 29, 37, 38, 30, 37, 29, 15, 16, 30, 15, 30, 29, 29, 38, 28, 14, 15, 29, 14, 29, 28], "vertices": [3, 65, 85.4, 907.22, 0.00992, 66, -449.79, 519.48, 0.084, 68, 198.01, 57.8, 0.90608, 3, 65, 230.63, 862.33, 0.02567, 66, -304.56, 474.59, 0.18507, 68, 343.24, 12.91, 0.78926, 4, 65, 262.32, 717.11, 0.05942, 66, -272.88, 329.36, 0.34212, 68, 374.93, -132.32, 0.59837, 67, 972.93, 203.54, 9e-05, 3, 65, 375.86, 614.13, 0.05858, 66, -159.34, 226.38, 0.67366, 68, 488.47, -235.3, 0.26776, 3, 65, 518.45, 571.88, 0.00858, 66, -16.75, 184.13, 0.92628, 68, 631.06, -277.55, 0.06514, 2, 66, 120.56, 107.56, 0.99811, 68, 768.36, -354.12, 0.00189, 1, 66, 246.7, 4.58, 1, 2, 65, 781.9, 294.62, 0.0011, 66, 246.7, -93.12, 0.9989, 2, 65, 695.36, 181.08, 0.02947, 66, 160.17, -206.66, 0.97053, 2, 65, 526.37, 93.95, 0.20949, 66, -8.83, -293.8, 0.79051, 3, 65, 354.73, -14.31, 0.57721, 66, -180.46, -402.06, 0.4219, 68, 467.34, -863.74, 0.00089, 2, 65, 225.35, -96.17, 0.83153, 66, -309.84, -483.91, 0.16847, 2, 65, 74.84, -168.62, 0.97895, 66, -460.35, -556.37, 0.02105, 3, 65, -152.24, -168.62, 0.96902, 68, -39.63, -1018.05, 0.00051, 67, 558.37, -682.19, 0.03047, 3, 65, -265.78, -59.2, 0.84851, 68, -153.17, -908.63, 0.01732, 67, 444.83, -572.77, 0.13417, 3, 65, -401.09, 27.28, 0.6214, 68, -288.48, -822.15, 0.04077, 67, 309.52, -486.28, 0.33783, 3, 65, -521.91, 104.51, 0.40902, 68, -409.3, -744.92, 0.04107, 67, 188.7, -409.05, 0.54991, 3, 65, -622.93, 169.72, 0.24625, 68, -510.32, -679.7, 0.02526, 67, 87.68, -343.84, 0.7285, 3, 65, -730.51, 239.17, 0.11296, 68, -617.9, -610.25, 0.00591, 67, -19.9, -274.39, 0.88113, 2, 65, -835.35, 338.02, 0.02369, 67, -124.74, -175.54, 0.97631, 2, 65, -920.95, 418.73, 0.00141, 67, -210.34, -94.83, 0.99859, 1, 67, -210.34, 71.52, 1, 2, 68, -675.99, -132.32, 0.03347, 67, -78, 203.54, 0.96653, 3, 65, -598.49, 772.56, 0.00519, 68, -485.88, -76.87, 0.25547, 67, 112.12, 258.99, 0.73934, 3, 65, -429.5, 857.05, 0.00871, 68, -316.89, 7.63, 0.62893, 67, 281.11, 343.49, 0.36236, 3, 65, -308.03, 933.63, 0.00127, 68, -195.42, 84.2, 0.87611, 67, 402.58, 420.07, 0.12262, 2, 68, -92.44, 160.09, 0.97806, 67, 505.56, 495.95, 0.02194, 2, 66, -595.02, 621.77, 0.00335, 68, 52.79, 160.09, 0.99665, 4, 65, -83.59, 9.45, 0.94988, 66, -618.78, -378.29, 0.00104, 68, 29.02, -839.97, 0.01193, 67, 627.02, -504.11, 0.03716, 4, 65, -236.74, 109.79, 0.7299, 66, -771.94, -277.95, 0.00474, 68, -124.13, -739.64, 0.05997, 67, 473.87, -403.77, 0.20539, 4, 65, -379.33, 204.85, 0.48018, 66, -914.52, -182.9, 0.00254, 68, -266.72, -644.58, 0.08506, 67, 331.28, -308.72, 0.43222, 4, 65, -524.56, 291.98, 0.24942, 66, -1059.75, -95.76, 0.00021, 68, -411.95, -557.44, 0.06296, 67, 186.05, -221.58, 0.68741, 3, 65, -696.19, 424.01, 0.03614, 68, -583.58, -425.42, 0.00744, 67, 14.42, -89.55, 0.95642, 1, 67, -80.64, 21.35, 1, 4, 65, -556.24, 603.56, 0.03296, 66, -1091.44, 215.82, 3e-05, 68, -443.63, -245.86, 0.1911, 67, 154.37, 90, 0.77591, 4, 65, -455.9, 508.51, 0.12607, 66, -991.1, 120.76, 0.00243, 68, -343.29, -340.92, 0.23681, 67, 254.71, -5.06, 0.6347, 4, 65, -387.25, 421.37, 0.24328, 66, -922.44, 33.63, 0.00762, 68, -274.64, -428.06, 0.22619, 67, 323.36, -92.19, 0.5229, 4, 65, -289.55, 336.87, 0.4064, 66, -824.74, -50.87, 0.01974, 68, -176.94, -512.55, 0.20996, 67, 421.06, -176.69, 0.3639, 4, 65, -139.04, 262.94, 0.597, 66, -674.24, -124.8, 0.05588, 68, -26.43, -586.49, 0.17685, 67, 571.57, -250.62, 0.17027, 4, 65, 32.59, 159.96, 0.77062, 66, -502.6, -227.78, 0.12026, 68, 145.2, -689.47, 0.0779, 67, 743.2, -353.6, 0.03122, 4, 65, 193.66, 91.31, 0.70477, 66, -341.53, -296.44, 0.26931, 68, 306.27, -758.12, 0.02496, 67, 904.27, -422.26, 0.00096, 2, 65, 24.67, -59.2, 0.99049, 66, -510.52, -446.95, 0.00951, 3, 65, 367.94, 159.96, 0.36705, 66, -167.26, -227.78, 0.61593, 68, 480.55, -689.47, 0.01702, 4, 65, 156.7, 315.75, 0.43015, 66, -378.5, -71.99, 0.37423, 68, 269.31, -533.68, 0.17745, 67, 867.31, -197.81, 0.01817, 4, 65, -9.66, 408.17, 0.38564, 66, -544.85, 20.42, 0.18275, 68, 102.95, -441.26, 0.34607, 67, 700.95, -105.4, 0.08554, 4, 65, -165.44, 482.1, 0.28073, 66, -700.64, 94.36, 0.06721, 68, -52.83, -367.32, 0.44301, 67, 545.17, -31.46, 0.20904, 4, 65, -252.58, 553.39, 0.17884, 66, -787.78, 165.65, 0.02844, 68, -139.97, -296.03, 0.49658, 67, 458.03, 39.83, 0.29613, 4, 65, -313.31, 632.61, 0.09648, 66, -848.51, 244.87, 0.01001, 68, -200.7, -216.82, 0.54654, 67, 397.3, 119.05, 0.34697, 4, 65, -387.25, 730.31, 0.03281, 66, -922.44, 342.57, 0.00083, 68, -274.64, -119.12, 0.56598, 67, 323.36, 216.75, 0.40037, 4, 65, -210.33, 796.32, 0.01893, 66, -745.53, 408.58, 0.0031, 68, -97.72, -53.1, 0.87692, 67, 500.28, 282.76, 0.10106, 4, 65, -112.63, 701.26, 0.07147, 66, -647.83, 313.52, 0.0472, 68, -0.02, -148.16, 0.80865, 67, 597.98, 187.7, 0.07268, 4, 65, -28.14, 627.33, 0.13142, 66, -563.33, 239.59, 0.12746, 68, 84.47, -222.1, 0.68491, 67, 682.47, 113.77, 0.05621, 4, 65, 103.89, 534.91, 0.19505, 66, -431.31, 147.17, 0.30123, 68, 216.5, -314.51, 0.4772, 67, 814.5, 21.35, 0.02652, 4, 65, 270.24, 437.21, 0.18641, 66, -264.96, 49.47, 0.5903, 68, 382.85, -412.21, 0.21886, 67, 980.85, -76.35, 0.00443, 3, 65, 439.23, 305.19, 0.12411, 66, -95.96, -82.56, 0.85062, 68, 551.84, -544.24, 0.02527, 3, 65, 579.18, 233.89, 0.06263, 66, 43.98, -153.85, 0.93733, 68, 691.79, -615.53, 4e-05, 2, 65, 634.63, 381.76, 0.00036, 66, 99.43, -5.98, 0.99964, 3, 65, 476.2, 450.41, 0.02058, 66, -59, 62.67, 0.9325, 68, 588.81, -399.01, 0.04692, 4, 65, 330.97, 526.99, 0.0888, 66, -204.22, 139.25, 0.66583, 68, 443.58, -322.44, 0.24461, 67, 1041.58, 13.43, 0.00075, 4, 65, 196.31, 600.92, 0.11155, 66, -338.89, 213.18, 0.39028, 68, 308.92, -248.5, 0.49231, 67, 906.91, 87.36, 0.00586, 4, 65, 90.68, 709.18, 0.06596, 66, -444.51, 321.44, 0.18854, 68, 203.29, -140.24, 0.73811, 67, 801.29, 195.62, 0.00739, 4, 65, 3.55, 791.04, 0.02677, 66, -531.65, 403.3, 0.0752, 68, 116.16, -58.38, 0.89407, 67, 714.16, 277.48, 0.00395, 2, 66, -631.99, 493.07, 0.00261, 68, 15.82, 31.39, 0.99739], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54], "width": 120, "height": 83}}, "zuojiao": {"zuojiao": {"type": "mesh", "uvs": [0.35116, 0.09491, 0.49387, 0.13917, 0.61104, 0.12626, 0.73634, 0.07832, 0.89065, 0.06357, 0.89993, 0.12811, 0.86744, 0.2701, 0.86383, 0.34548, 0.83435, 0.37634, 0.78131, 0.38902, 0.77819, 0.41602, 0.84198, 0.42098, 0.91653, 0.44523, 0.96333, 0.48601, 0.97685, 0.61607, 0.97788, 0.69752, 0.97893, 0.78194, 1, 0.80509, 1, 1, 0, 1, 0, 0.886, 0.04371, 0.7477, 0.07968, 0.64444, 0.10636, 0.53195, 0.13305, 0.46372, 0.15741, 0.39734, 0.082, 0.3789, 0.09708, 0.28485, 0.13189, 0.12811, 0.1841, 0, 0.22934, 0, 0.69223, 0.41153, 0.58128, 0.43459, 0.46095, 0.42374, 0.33719, 0.41153, 0.22027, 0.40339], "triangles": [33, 34, 1, 19, 20, 21, 19, 21, 33, 33, 22, 23, 33, 21, 22, 19, 33, 32, 5, 6, 3, 5, 3, 4, 3, 6, 9, 7, 8, 6, 8, 9, 6, 31, 2, 3, 35, 25, 28, 27, 28, 25, 26, 27, 25, 30, 28, 29, 0, 28, 30, 28, 0, 35, 9, 31, 3, 34, 35, 0, 34, 0, 1, 10, 31, 9, 32, 2, 31, 33, 1, 32, 16, 17, 18, 12, 13, 14, 14, 15, 12, 15, 11, 12, 15, 10, 11, 16, 10, 15, 31, 10, 16, 32, 31, 16, 23, 24, 34, 24, 35, 34, 24, 25, 35, 33, 23, 34, 32, 18, 19, 18, 32, 16, 32, 1, 2], "vertices": [2, 4, 111.41, -74.31, 0.32474, 62, 60.9, 72.32, 0.67526, 2, 4, 97.14, -19.28, 0.95897, 62, 117.46, 66.63, 0.04103, 2, 4, 74.49, 20.68, 0.73534, 62, 162.79, 74.01, 0.26466, 2, 4, 42.7, 59.78, 0.29789, 62, 210.49, 90.27, 0.70211, 2, 4, 13.38, 112.63, 0.15453, 62, 270.23, 99.45, 0.84547, 2, 4, 26.14, 122.76, 0.15036, 62, 275.31, 83.98, 0.84964, 2, 4, 63.13, 126.38, 0.11472, 62, 265.88, 48.02, 0.88528, 2, 4, 80.47, 133.11, 0.09047, 62, 266.18, 29.43, 0.90953, 2, 4, 92.29, 126, 0.08069, 62, 255.41, 20.81, 0.91931, 2, 4, 104.06, 108.63, 0.04485, 62, 235.04, 15.79, 0.95515, 2, 4, 110.57, 110.4, 0.01359, 62, 234.44, 9.06, 0.98641, 1, 62, 259.39, 10.15, 1, 1, 62, 288.96, 6.89, 1, 2, 62, 308.11, -1.41, 0.99894, 5, -49.65, 189.76, 0.00106, 2, 62, 316.33, -32.78, 0.98493, 5, -21.19, 205.3, 0.01507, 2, 62, 318.57, -52.69, 0.96891, 5, -2.41, 212.28, 0.03109, 2, 62, 320.9, -73.33, 0.95055, 5, 17.07, 219.52, 0.04945, 2, 62, 329.62, -78.24, 0.94676, 5, 19.73, 229.17, 0.05324, 2, 62, 334.04, -125.99, 0.93392, 5, 64.99, 244.98, 0.06608, 2, 62, -55.29, -162.04, 0.29479, 5, 193.9, -124.16, 0.70521, 2, 62, -57.88, -134.11, 0.30862, 5, 167.42, -133.4, 0.69138, 2, 62, -43.99, -98.66, 0.38392, 5, 129.67, -128.48, 0.61608, 2, 62, -32.33, -72.07, 0.51801, 5, 101.05, -123.58, 0.48199, 2, 62, -24.49, -43.55, 0.70851, 5, 71.49, -122.86, 0.29149, 2, 62, -15.65, -25.88, 0.83993, 5, 52.2, -118.54, 0.16007, 2, 62, -7.67, -8.74, 0.97606, 5, 33.64, -114.93, 0.02394, 1, 62, -37.45, -6.94, 1, 2, 4, 196.44, -143.75, 0.00146, 62, -33.71, 16.64, 0.99854, 2, 4, 155.79, -148.12, 0.02782, 62, -23.72, 56.29, 0.97218, 2, 4, 118.55, -143.31, 0.06309, 62, -6.3, 89.55, 0.93691, 2, 4, 110.91, -127.35, 0.08016, 62, 11.32, 91.18, 0.91984, 2, 4, 124.09, 79.61, 0.01888, 62, 200.87, 7.06, 0.98112, 2, 62, 158.2, -2.58, 0.98494, 5, -12.35, 44.56, 0.01506, 1, 5, 0.65, -0.74, 1, 2, 62, 62.64, -5.74, 0.96199, 5, 13.76, -47.41, 0.03801, 2, 62, 16.94, -7.96, 0.97814, 5, 26.95, -91.23, 0.02186], "hull": 31, "edges": [0, 60, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60], "width": 78, "height": 49}}}}], "animations": {"attack": {"slots": {"archer9": {"attachment": [{"time": 0.3667, "name": "zui2"}, {"time": 0.8333, "name": "archer9"}]}}, "bones": {"archer13": {"rotate": [{"angle": 0.18, "curve": 0, "c2": 0.82, "c3": 0}, {"time": 0.2333, "angle": 8.34, "curve": 1, "c4": 0.45}, {"time": 0.3333, "angle": 6.59, "curve": "stepped"}, {"time": 0.3667, "angle": 25.21, "curve": 0.05, "c2": 0.1, "c3": 0.388}, {"time": 0.4667, "angle": 26.94, "curve": "stepped"}, {"time": 0.7333, "angle": 26.94, "curve": 0.25, "c4": 0.89}, {"time": 0.8333, "angle": 25.21, "curve": 0.05, "c2": 0.05, "c3": 0.75}, {"time": 0.9333, "angle": 0.18}], "translate": [{"time": 0.3333, "curve": "stepped"}, {"time": 0.3667, "x": -41.56, "y": -91.13, "curve": "stepped"}, {"time": 0.7333, "x": -41.56, "y": -91.13, "curve": 0.25, "c4": 0.89}, {"time": 0.8333, "x": -21.76, "y": -108.17, "curve": 0.05, "c2": 0.05, "c3": 0.75}, {"time": 0.9333}]}, "archer12": {"rotate": [{"angle": 0.34, "curve": 0, "c2": 0.82, "c3": 0}, {"time": 0.2333, "angle": 0.57, "curve": "stepped"}, {"time": 0.3333, "angle": 0.57, "curve": "stepped"}, {"time": 0.3667, "angle": -10.51, "curve": 0.163, "c2": 0.15, "c3": 0.497, "c4": 0.51}, {"time": 0.4, "angle": -11.62, "curve": 0.134, "c2": 0.31, "c3": 0.47}, {"time": 0.4667, "angle": -15.92, "curve": "stepped"}, {"time": 0.7333, "angle": -15.92, "curve": 0.25, "c4": 0.89}, {"time": 0.8333, "angle": -12.7, "curve": 0.05, "c2": 0.05, "c3": 0.75}, {"time": 0.9333, "angle": 0.34}]}, "archer17": {"rotate": [{"angle": 3.81, "curve": 0, "c2": 0.82, "c3": 0}, {"time": 0.2333, "angle": 39.56, "curve": 1, "c4": 0.45}, {"time": 0.3333, "angle": 85.65, "curve": "stepped"}, {"time": 0.3667, "angle": 52.32, "curve": 0.05, "c2": 0.1, "c3": 0.388}, {"time": 0.7333, "angle": 22.12, "curve": 0.25, "c4": 0.89}, {"time": 0.8333, "angle": -0.59, "curve": 0.05, "c2": 0.05, "c3": 0.75}, {"time": 0.9333, "angle": 3.81}]}, "archer16": {"rotate": [{"angle": -2.6, "curve": 0, "c2": 0.82, "c3": 0}, {"time": 0.2333, "angle": 53.27, "curve": 1, "c4": 0.45}, {"time": 0.3333, "angle": 24.09, "curve": "stepped"}, {"time": 0.3667, "angle": -32.38, "curve": 0.05, "c2": 0.1, "c3": 0.388}, {"time": 0.7333, "angle": -35.1, "curve": 0.25, "c4": 0.89}, {"time": 0.8333, "angle": -9.91, "curve": 0.05, "c2": 0.05, "c3": 0.75}, {"time": 0.9333, "angle": -2.6}], "translate": [{"curve": 0, "c2": 0.82, "c3": 0}, {"time": 0.2333, "x": -22.74, "y": -31.74, "curve": 1, "c4": 0.45}, {"time": 0.3333, "x": -17.48, "y": -13.78, "curve": "stepped"}, {"time": 0.3667, "curve": 0.05, "c2": 0.1, "c3": 0.388}, {"time": 0.7333, "x": -7.37, "y": 18.8, "curve": 0.25, "c4": 0.89}, {"time": 0.8333, "x": -20.65, "y": 2.92, "curve": 0.05, "c2": 0.05, "c3": 0.75}, {"time": 0.9333}]}, "archer2": {"rotate": [{"curve": 0, "c2": 0.82, "c3": 0}, {"time": 0.2333, "angle": 3.51, "curve": 1, "c4": 0.45}, {"time": 0.3333, "angle": -2.21, "curve": "stepped"}, {"time": 0.3667, "angle": -11.13, "curve": 0.163, "c2": 0.15, "c3": 0.497, "c4": 0.51}, {"time": 0.4, "angle": -13.77, "curve": 0.134, "c2": 0.31, "c3": 0.47}, {"time": 0.4667, "angle": -12.5, "curve": "stepped"}, {"time": 0.7333, "angle": -12.5, "curve": 0.25, "c4": 0.89}, {"time": 0.8333, "angle": 0.42, "curve": 0.05, "c2": 0.05, "c3": 0.75}, {"time": 0.9333}], "translate": [{"y": 4.95, "curve": 0, "c2": 0.82, "c3": 0}, {"time": 0.2333, "x": -13.32, "y": 39.59, "curve": 1, "c4": 0.45}, {"time": 0.3333, "x": 50.45, "y": -21.82, "curve": "stepped"}, {"time": 0.3667, "x": 215.62, "y": -136.89, "curve": 0.163, "c2": 0.15, "c3": 0.497, "c4": 0.51}, {"time": 0.4, "x": 221.14, "y": -177.1, "curve": 0.25, "c2": 0.28, "c3": 0.583, "c4": 0.62}, {"time": 0.4667, "x": 230.63, "y": -155.66, "curve": "stepped"}, {"time": 0.7333, "x": 230.63, "y": -155.66, "curve": 0.25, "c4": 0.89}, {"time": 0.8333, "x": 184.1, "y": -110.22, "curve": 0.05, "c2": 0.05, "c3": 0.75}, {"time": 0.9333, "y": 4.95}]}, "archer5": {"rotate": [{"angle": 3.39}], "translate": [{"curve": 0.25, "c3": 0.444}, {"time": 0.2, "x": 44.89, "y": -2.75, "curve": "stepped"}, {"time": 0.3333, "x": 44.89, "y": -2.75, "curve": "stepped"}, {"time": 0.3667}]}, "archer8": {"rotate": [{"angle": -2.93}], "translate": [{"curve": 0.25, "c3": 0.444}, {"time": 0.2, "x": -34.11, "y": 2.09, "curve": "stepped"}, {"time": 0.3333, "x": -34.11, "y": 2.09, "curve": "stepped"}, {"time": 0.3667}]}, "archer20": {"rotate": [{"angle": -1.51, "curve": 0, "c2": 0.82, "c3": 0}, {"time": 0.2333, "angle": 23, "curve": 1, "c4": 0.45}, {"time": 0.3333, "angle": 54.44, "curve": "stepped"}, {"time": 0.3667, "angle": 8.29}, {"time": 0.4, "angle": 13.3}, {"time": 0.4667, "angle": 24.75, "curve": "stepped"}, {"time": 0.7333, "angle": 24.75}, {"time": 0.9333, "angle": -1.51}], "translate": [{"time": 0.3333, "curve": "stepped"}, {"time": 0.3667, "x": 63.31, "y": 32.9, "curve": "stepped"}, {"time": 0.7333, "x": 63.31, "y": 32.9}, {"time": 0.9333}]}, "archer19": {"rotate": [{"angle": 2.28, "curve": 0, "c2": 0.82, "c3": 0}, {"time": 0.2333, "angle": 103.95, "curve": 1, "c4": 0.45}, {"time": 0.3333, "angle": 99.56, "curve": "stepped"}, {"time": 0.3667, "angle": 44.67}, {"time": 0.4, "angle": 50.97}, {"time": 0.4667, "angle": 42.34, "curve": "stepped"}, {"time": 0.7333, "angle": 42.34}, {"time": 0.9333, "angle": 2.28}], "translate": [{"curve": 0, "c2": 0.82, "c3": 0}, {"time": 0.2333, "x": 163.87, "y": 14.28, "curve": 1, "c4": 0.45}, {"time": 0.3333, "x": 93.57, "y": -39.25, "curve": "stepped"}, {"time": 0.3667, "x": 144.21, "y": -76.16, "curve": "stepped"}, {"time": 0.7333, "x": 144.21, "y": -76.16}, {"time": 0.9333}]}, "archer22": {"rotate": [{"time": 0.3333, "curve": "stepped"}, {"time": 0.3667, "angle": -39.06}, {"time": 0.4667, "angle": -52.04, "curve": "stepped"}, {"time": 0.7333, "angle": -52.04}, {"time": 0.9333}]}, "archer6": {"rotate": [{"angle": -6.72}]}, "archer10": {"translate": [{"time": 0.3333, "curve": "stepped"}, {"time": 0.3667, "x": 166.41, "curve": "stepped"}, {"time": 0.8333, "x": 166.41, "curve": 0.05, "c2": 0.05, "c3": 0.75}, {"time": 0.9333}]}, "archer68": {"rotate": [{"angle": 0.31}, {"time": 0.2, "angle": 23.05}, {"time": 0.3333, "angle": -5.63, "curve": "stepped"}, {"time": 0.3667, "angle": -5.24}, {"time": 0.5667, "angle": -22.58}, {"time": 0.8, "angle": 19.93}, {"time": 0.9333, "angle": 0.31}]}, "archer72": {"rotate": [{"angle": -2.01, "curve": "stepped"}, {"time": 0.2, "angle": -2.01}, {"time": 0.3333, "angle": -21.38, "curve": "stepped"}, {"time": 0.3667, "angle": -32.43}, {"time": 0.5667, "angle": -6.89}, {"time": 0.8, "angle": 34.66}, {"time": 0.9333, "angle": -2.01}], "translate": [{"curve": 0.25, "c3": 0.444}, {"time": 0.3333, "x": 49.93, "y": 168.99, "curve": "stepped"}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -8.89, "y": -64.09, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "archer78": {"rotate": [{"angle": -1.67}, {"time": 0.2, "angle": 8.14}, {"time": 0.3333, "angle": 13.46, "curve": "stepped"}, {"time": 0.3667, "angle": 6.33}, {"time": 0.5667, "angle": -13.01}, {"time": 0.8, "angle": 3.71}, {"time": 0.9333, "angle": -1.67}]}, "archer75": {"rotate": [{"angle": -7.47}, {"time": 0.2, "angle": 6.39}, {"time": 0.3333, "angle": 34.16, "curve": "stepped"}, {"time": 0.3667, "angle": 23.25}, {"time": 0.5667, "angle": 3.11}, {"time": 0.8, "angle": -16.23}, {"time": 0.9333, "angle": -7.47}]}, "archer73": {"rotate": [{"angle": 1.49}, {"time": 0.2, "angle": 7.58}, {"time": 0.3333, "angle": 10.51, "curve": "stepped"}, {"time": 0.3667, "angle": -0.08}, {"time": 0.5667, "angle": -7.06}, {"time": 0.8, "angle": 14.96}, {"time": 0.9333, "angle": 1.49}]}, "archer80": {"rotate": [{"angle": -9.62}, {"time": 0.2, "angle": -0.85}, {"time": 0.3333, "angle": 7.62, "curve": "stepped"}, {"time": 0.3667, "angle": 22.88}, {"time": 0.5667, "angle": -15.2}, {"time": 0.8, "angle": -24.07}, {"time": 0.9333, "angle": -9.62}]}, "archer70": {"rotate": [{"angle": -11.41}, {"time": 0.2, "angle": -27.75}, {"time": 0.3333, "angle": 14.86, "curve": "stepped"}, {"time": 0.3667, "angle": 18.29}, {"time": 0.5667, "angle": -19.06}, {"time": 0.8, "angle": -23.45}, {"time": 0.9333, "angle": -11.41}]}, "archer67": {"rotate": [{"angle": 4.1, "curve": "stepped"}, {"time": 0.2, "angle": 4.1}, {"time": 0.3333, "angle": -4.36, "curve": "stepped"}, {"time": 0.3667, "angle": -2.25}, {"time": 0.5667, "angle": 17.09}, {"time": 0.8, "angle": 28.19}, {"time": 0.9333, "angle": 4.1}], "translate": [{"curve": 0.25, "c3": 0.444}, {"time": 0.3333, "x": -25.47, "y": -82.71, "curve": "stepped"}, {"time": 0.3667, "x": -36.12, "y": -65.19, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 25.12, "y": 21.05, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "archer88": {"translate": [{"curve": 0.25, "c3": 0.444}, {"time": 0.2, "x": 1005.15, "y": -2087.42, "curve": 0.25, "c4": 0.6}, {"time": 0.3333, "x": 747.64, "y": -3484.42, "curve": "stepped"}, {"time": 0.3667, "x": 76.23, "y": 1055.7, "curve": 0.05, "c2": 0.1, "c3": 0.388}, {"time": 0.7333, "x": -858.85, "y": 1625.75, "curve": 0.25, "c4": 0.89}, {"time": 0.8333, "x": 367.99, "y": -157.64, "curve": 0.05, "c2": 0.05, "c3": 0.75}, {"time": 0.9333}]}, "archer18": {"rotate": [{"angle": -0.45, "curve": 0, "c2": 0.82, "c3": 0}, {"time": 0.2333, "angle": 4.9, "curve": 1, "c4": 0.45}, {"time": 0.3333, "angle": 12.56, "curve": "stepped"}, {"time": 0.3667, "angle": 0.4, "curve": "stepped"}, {"time": 0.8333, "angle": 0.4, "curve": 0.05, "c2": 0.05, "c3": 0.75}, {"time": 0.9333, "angle": -0.45}]}, "archer52": {"rotate": [{}, {"time": 0.2, "angle": -21.39}, {"time": 0.3333, "angle": -11.02, "curve": "stepped"}, {"time": 0.3667, "angle": 3.36}, {"time": 0.7333, "angle": -12.92}, {"time": 0.9333}]}, "archer46": {"rotate": [{}, {"time": 0.2, "angle": 28.98}, {"time": 0.3333, "angle": 31.2, "curve": "stepped"}, {"time": 0.3667, "angle": 36.35}, {"time": 0.7333, "angle": -4.77}, {"time": 0.9333}]}, "archer56": {"rotate": [{}, {"time": 0.2, "angle": 7.36}, {"time": 0.3333, "angle": 13.72, "curve": "stepped"}, {"time": 0.3667, "angle": -31.08}, {"time": 0.7333, "angle": -1.79}, {"time": 0.9333}]}, "archer90": {"translate": [{"curve": 0.25, "c3": 0.444}, {"time": 0.2, "x": 1192.01, "y": 471.8, "curve": 0.25, "c4": 0.6}, {"time": 0.3333, "x": -493.68, "y": -887.02, "curve": "stepped"}, {"time": 0.3667, "x": -2562.05, "y": -810.3, "curve": 0.05, "c2": 0.1, "c3": 0.388}, {"time": 0.7333, "x": -3280.73, "y": -255.54, "curve": 0.25, "c4": 0.89}, {"time": 0.8333, "x": -2562.05, "y": -810.3, "curve": 0.05, "c2": 0.05, "c3": 0.75}, {"time": 0.9333}]}, "archer77": {"rotate": [{"angle": 4.77, "curve": "stepped"}, {"time": 0.2, "angle": 4.77}, {"time": 0.3333, "angle": -17.74, "curve": "stepped"}, {"time": 0.3667, "angle": -29.12}, {"time": 0.5667, "angle": -3.62}, {"time": 0.8, "angle": 26.4}, {"time": 0.9333, "angle": 4.77}], "translate": [{"curve": 0.25, "c3": 0.444}, {"time": 0.3333, "x": 34.33, "y": 78.32, "curve": "stepped"}, {"time": 0.3667, "x": -14.4, "y": -46.29, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -14.19, "y": -24.56, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "archer11": {"rotate": [{"angle": 0.09, "curve": "stepped"}, {"time": 0.2333, "angle": 0.09, "curve": 1, "c4": 0.45}, {"time": 0.3333, "angle": 4.08, "curve": "stepped"}, {"time": 0.3667, "angle": -26.86, "curve": 0.163, "c2": 0.15, "c3": 0.497, "c4": 0.51}, {"time": 0.4, "angle": -28.23, "curve": 0.134, "c2": 0.31, "c3": 0.47}, {"time": 0.4667, "angle": -27.93, "curve": "stepped"}, {"time": 0.7333, "angle": -27.93, "curve": 0.25, "c4": 0.89}, {"time": 0.8333, "angle": -29.09, "curve": 0.05, "c2": 0.05, "c3": 0.75}, {"time": 0.9333, "angle": 0.09}]}, "archer61": {"rotate": [{"angle": 0.81}, {"time": 0.2, "angle": 26.32}, {"time": 0.3333, "angle": 27.18, "curve": "stepped"}, {"time": 0.3667, "angle": -0.31}, {"time": 0.7333, "angle": -5.69}, {"time": 0.9333, "angle": 0.81}]}, "archer9": {"rotate": [{"angle": 5.69}]}, "archer21": {"rotate": [{"angle": 0.05, "curve": 0, "c2": 0.82, "c3": 0}, {"time": 0.2333, "angle": 16.64, "curve": "stepped"}, {"time": 0.3333, "angle": 16.64, "curve": "stepped"}, {"time": 0.3667, "angle": -5.18, "curve": "stepped"}, {"time": 0.7333, "angle": -5.18}, {"time": 0.9333, "angle": 0.05}]}, "archer101": {"rotate": [{}, {"time": 0.2, "angle": 29.59}, {"time": 0.3333, "angle": 43.33, "curve": "stepped"}, {"time": 0.3667, "angle": 57.11}, {"time": 0.7333, "angle": -2.13}, {"time": 0.9333}]}, "archer98": {"rotate": [{"angle": 3.55}, {"time": 0.2, "angle": -24.75}, {"time": 0.3333, "angle": 0.39, "curve": "stepped"}, {"time": 0.3667, "angle": 15.51}, {"time": 0.7333, "angle": 27.38}, {"time": 0.9333, "angle": 3.55}]}, "ren": {"translate": [{"x": -39.81, "y": 1574.55}]}, "archer15": {"rotate": [{"angle": -2.93}], "translate": [{"curve": 0.25, "c3": 0.444}, {"time": 0.2, "x": -34.11, "y": 2.09, "curve": "stepped"}, {"time": 0.3333, "x": -34.11, "y": 2.09, "curve": "stepped"}, {"time": 0.3667}]}, "archer24": {"rotate": [{"angle": 5.69}]}}, "deform": {"default": {"archer12": {"archer12": [{"curve": 0, "c2": 0.82, "c3": 0}, {"time": 0.2333, "offset": 48, "vertices": [-6.24121, 1.68942, -6.24048, 1.68823, 0, 0, 0, 0, -9.98615, 2.70326, -9.98462, 2.70093, -6.24127, 1.68947, -6.24072, 1.68823, 0, 0, 12.48145, -3.37836, 12.48158, -3.37927, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12.48145, -3.37836, 12.48158, -3.37927, 0, 0, -6.24127, 1.68947, -6.24072, 1.68823, -9.98615, 2.70326, -9.98462, 2.70093, 0, 0, 0, 0, -6.24121, 1.68942, -6.24048, 1.68823], "curve": 1, "c4": 0.45}, {"time": 0.3333, "offset": 36, "vertices": [11.41339, 5.61835, 17.56036, -8.75421, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.95135, 1.64182, -7.95117, 1.63379, 0, 0, 0, 0, 0, 0, 15.01703, -3.10141, 15.01743, -3.09985, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15.01703, -3.10141, 15.01743, -3.09985, 0, 0, 0, 0, 0, 0, -7.95135, 1.64182, -7.95117, 1.63379, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.23749, -14.59851, 10.97998, 6.27737], "curve": "stepped"}, {"time": 0.3667, "curve": 0.05, "c2": 0.1, "c3": 0.388}, {"time": 0.4667, "offset": 44, "vertices": [7.95636, -1.41843, 7.95657, -1.41919, 11.94098, -3.93515, 11.94106, -3.93652, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12.66248, -3.57065, 12.66258, -3.57159, 4.69983, -0.34528, 4.69991, -0.34583], "curve": "stepped"}, {"time": 0.7333, "offset": 44, "vertices": [7.95636, -1.41843, 7.95657, -1.41919, 11.94098, -3.93515, 11.94106, -3.93652, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12.66248, -3.57065, 12.66258, -3.57159, 4.69983, -0.34528, 4.69991, -0.34583], "curve": 0.25, "c4": 0.89}, {"time": 0.8333}]}, "archer15": {"archer15": [{"curve": 0, "c2": 0.82, "c3": 0}, {"time": 0.2333, "offset": 28, "vertices": [6.69379, 29.30823, -29.90018, 3.12469, -61.72581, -5.85583, -66.4761, 7.56543, -66.89075, -1.35095, -72.44346, 10.13025, -73.14606, 0.39658, -56.77886, 7.50189, -57.27136, -0.12314, -46.85737, -10.17566, -45.08484, -16.32285, -25.42531, -7.00568, -24.26575, -10.32796, -2.2597, 16.48201, -16.09138, -4.22345, -15.38544, -6.32785, -12.75104, 19.42567, -17.7534, -14.99249, -15.59924, -17.22229, -17.00806, 34.17348, -31.88309, -20.99017, -28.80438, -25.04761, -6.8822, 43.27705, -42.13733, -12.03052, -40.15955, -17.53339, -8.62634, 46.70857, -45.33445, -14.17493, -27.53845, -11.72229, -4.42651, 29.59946, -28.85358, -7.95038, 0, 0, 0, 0, 0, 0, 14.8761, 5.08337, 1.18475, -15.6768, 15.42079, 3.05927, 19.51831, 3.94226, -1.08612, -19.88322, 19.86953, 1.31049, 25.01379, 2.74756, -3.62201, -24.90274, 25.15736, -0.60419, 35.07715, 0.39685, -8.42664, -34.05243, 34.81796, -4.27502, 34.30225, -6.28479, -14.69812, -31.6239, 33.16023, -10.79315, -40.58359, 0.23285, 39.01807, -11.17102, -20.61206, -34.96083, -47.72229, 1.68494, 45.50024, -14.49329, -25.45752, -40.4003, -43.7317, 2.96942, 41.31091, -14.65491, -24.56055, -36.30527, -47.34518, 3.89929, 44.53992, -16.52441, -27.18195, -38.96069, -47.08212, 2.6759, 44.6167, -15.27466, -25.99213, -39.34856, -48.10364, 5.44287, 44.85522, -18.2146, -28.89709, -38.84016, -48.33264, -2.7522, -23.39418, -0.33698, 22.6189, -5.98486, -23.00208, -4.27988, -20.18878, -1.35443, 19.80652, -4.1405, -19.67087, -4.74176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -46.17448, -3.52893, -20.17255, -41.685, -78.77301, -6.69818, -33.82794, -71.45465, -57.26891, -11.46185, -18.8963, -55.26364, 57.13301, -12.12128, -8.22797, -30.14677, 30.91664, -4.547, 0, 0, 0, 0, -0.33795, 29.69252, -29.43697, -3.90234, 35.74329, -3.90686, -12.75934, -33.61591, 34.90509, -8.62885, 20.16223, 0.47498, -4.60327, -19.63591, 20.04648, -2.21136, -33.82619, 2.53973, 31.88879, -11.56934, -33.77008, -3.20425, -0.81586, 15.05704, -14.85014, -2.61884, -11.55225, -17.92578, -14.44806, 15.68492, -13.83584, -16.22766, -11.64404, -12.0116, -8.70056, 14.28809, -13.13956, -10.35413], "curve": "stepped"}, {"time": 0.3333, "offset": 28, "vertices": [6.69379, 29.30823, -29.90018, 3.12469, -61.72581, -5.85583, -66.4761, 7.56543, -66.89075, -1.35095, -72.44346, 10.13025, -73.14606, 0.39658, -56.77886, 7.50189, -57.27136, -0.12314, -46.85737, -10.17566, -45.08484, -16.32285, -25.42531, -7.00568, -24.26575, -10.32796, -2.2597, 16.48201, -16.09138, -4.22345, -15.38544, -6.32785, -12.75104, 19.42567, -17.7534, -14.99249, -15.59924, -17.22229, -17.00806, 34.17348, -31.88309, -20.99017, -28.80438, -25.04761, -6.8822, 43.27705, -42.13733, -12.03052, -40.15955, -17.53339, -8.62634, 46.70857, -45.33445, -14.17493, -27.53845, -11.72229, -4.42651, 29.59946, -28.85358, -7.95038, 0, 0, 0, 0, 0, 0, 14.8761, 5.08337, 1.18475, -15.6768, 15.42079, 3.05927, 19.51831, 3.94226, -1.08612, -19.88322, 19.86953, 1.31049, 25.01379, 2.74756, -3.62201, -24.90274, 25.15736, -0.60419, 35.07715, 0.39685, -8.42664, -34.05243, 34.81796, -4.27502, 34.30225, -6.28479, -14.69812, -31.6239, 33.16023, -10.79315, -40.58359, 0.23285, 39.01807, -11.17102, -20.61206, -34.96083, -47.72229, 1.68494, 45.50024, -14.49329, -25.45752, -40.4003, -43.7317, 2.96942, 41.31091, -14.65491, -24.56055, -36.30527, -47.34518, 3.89929, 44.53992, -16.52441, -27.18195, -38.96069, -47.08212, 2.6759, 44.6167, -15.27466, -25.99213, -39.34856, -48.10364, 5.44287, 44.85522, -18.2146, -28.89709, -38.84016, -48.33264, -2.7522, -23.39418, -0.33698, 22.6189, -5.98486, -23.00208, -4.27988, -20.18878, -1.35443, 19.80652, -4.1405, -19.67087, -4.74176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -46.17448, -3.52893, -20.17255, -41.685, -78.77301, -6.69818, -33.82794, -71.45465, -57.26891, -11.46185, -18.8963, -55.26364, 57.13301, -12.12128, -8.22797, -30.14677, 30.91664, -4.547, 0, 0, 0, 0, -0.33795, 29.69252, -29.43697, -3.90234, 35.74329, -3.90686, -12.75934, -33.61591, 34.90509, -8.62885, 20.16223, 0.47498, -4.60327, -19.63591, 20.04648, -2.21136, -33.82619, 2.53973, 31.88879, -11.56934, -33.77008, -3.20425, -0.81586, 15.05704, -14.85014, -2.61884, -11.55225, -17.92578, -14.44806, 15.68492, -13.83584, -16.22766, -11.64404, -12.0116, -8.70056, 14.28809, -13.13956, -10.35413], "curve": "stepped"}, {"time": 0.3667}]}, "archer18": {"archer18": [{"curve": 0, "c2": 0.82, "c3": 0}, {"time": 0.2333, "offset": 78, "vertices": [17.30761, 9.63693, 9.30688, -17.48727, 4.98029, -19.17386, 17.30859, 9.63477, 46.48352, 16.54022, 4.33276, -49.14848, 46.48474, 16.53711, 45.9115, 7.21356, -4.55121, -46.25214, 45.91272, 7.20801, 45.9115, 7.21356, 45.91272, 7.20801, 76.91562, 6.10645, 4.64624, -77.01762, 76.91553, 6.10303, 94.30058, -0.3718, -2.16046, -94.27663, 94.30042, -0.375, 104.61162, -8.73557, -10.71783, -104.42729, 104.61121, -8.73767, 70.4846, -10.50815, -11.84399, -70.27272, 70.48438, -10.51208, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41.68834, -3.55511, -4.34491, -41.61351, -13.91351, -39.45889, 41.68884, -3.55713, 41.68834, -3.55511, -4.34491, -41.61351, -13.91351, -39.45889, 41.68884, -3.55713, 41.68834, -3.55511, -4.34491, -41.61351, -13.91351, -39.45889, 41.68884, -3.55713, 41.68834, -3.55511, -4.34491, -41.61351, -13.91351, -39.45889, 41.68884, -3.55713, 41.68834, -3.55511, -4.34491, -41.61351, -13.91351, -39.45889, 41.68884, -3.55713, 0, 0, 0, 0, 0, 0, 41.68834, -3.55511, -4.34491, -41.61351, -13.91351, -39.45889, 41.68884, -3.55713, 41.68834, -3.55511, -4.34491, -41.61351, -13.91351, -39.45889, 41.68884, -3.55713, 62.02298, 0.07178, -1.10449, -62.01328, -15.51135, -60.05266, 62.02283, 0.06897, 62.02298, 0.07178, -1.10449, -62.01328, -15.51135, -60.05266, 62.02283, 0.06897, 62.02298, 0.07178, -1.10449, -62.01328, 62.02283, 0.06897, 62.02298, 0.07178, -1.10449, -62.01328, 62.02283, 0.06897, 50.35049, 1.65991, 0.70532, -50.37336, -11.03986, -49.15504, 50.35425, 1.64978, 31.3202, 4.63962, 4.04517, -31.40303, -3.37537, -31.48354, 31.32495, 4.62842, 12.78652, 8.57083, 8.32837, -12.94725, 5.08606, -14.53181, 12.79016, 8.56128, -4.36848, 12.7605, 12.84259, 4.12519, 13.45062, 1.0206, -4.36401, 12.75122, 0, 0, 0, 0, 0, 0, 0, 0, 57.81524, 1.00946, -0.08588, -57.82439, 57.81787, 0.99915, 56.93067, 3.12338, 2.04248, -56.98011, -11.27704, -55.89192, 56.93445, 3.11169, 44.2856, 7.30746, -4.05084, -44.70357, 44.28857, 7.2959, 17.98521, 13.47659, 13.13281, -18.23804, 8.5282, -20.79619, 17.98889, 13.46558, 4.1871, 16.48849, 16.40558, -4.49944, 14.90948, -8.19733, 4.18982, 16.47693, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49.22267, 5.67383, -6.87225, -49.07181, 49.22534, 5.66345, 62.02298, 0.07178, -1.10449, -62.01328, 62.02283, 0.06897, 62.02298, 0.07178, -1.10449, -62.01328, 62.02283, 0.06897, 62.02298, 0.07178, -1.10449, -62.01328, 62.02283, 0.06897, 62.02298, 0.07178, -1.10449, -62.01328, 62.02283, 0.06897, 70.4846, -10.50815, -11.84399, -70.27272, -27.87787, -65.58498, 70.48438, -10.51208, 70.4846, -10.50815, -11.84399, -70.27272, -27.87787, -65.58498, 70.48438, -10.51208, 22.1238, 8.60995, 8.18909, -22.28312, 2.77637, -23.57764, 22.12415, 8.60815, 22.1238, 8.60995, 8.18909, -22.28312, 2.77637, -23.57764, 22.12415, 8.60815, 41.68834, -3.55511, -4.34491, -41.61351, -13.91351, -39.45889, 41.68884, -3.55713, 70.4846, -10.50815, -11.84399, -70.27272, -27.87787, -65.58498, 70.48438, -10.51208, 34.5658, -1.97452, -2.63037, -34.52234, -10.59424, -32.96231, 34.56763, -1.9812], "curve": "stepped"}, {"time": 0.3333, "offset": 78, "vertices": [17.30761, 9.63693, 9.30688, -17.48727, 4.98029, -19.17386, 17.30859, 9.63477, 46.48352, 16.54022, 4.33276, -49.14848, 46.48474, 16.53711, 45.9115, 7.21356, -4.55121, -46.25214, 45.91272, 7.20801, 45.9115, 7.21356, 45.91272, 7.20801, 76.91562, 6.10645, 4.64624, -77.01762, 76.91553, 6.10303, 94.30058, -0.3718, -2.16046, -94.27663, 94.30042, -0.375, 104.61162, -8.73557, -10.71783, -104.42729, 104.61121, -8.73767, 70.4846, -10.50815, -11.84399, -70.27272, 70.48438, -10.51208, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41.68834, -3.55511, -4.34491, -41.61351, -13.91351, -39.45889, 41.68884, -3.55713, 41.68834, -3.55511, -4.34491, -41.61351, -13.91351, -39.45889, 41.68884, -3.55713, 41.68834, -3.55511, -4.34491, -41.61351, -13.91351, -39.45889, 41.68884, -3.55713, 41.68834, -3.55511, -4.34491, -41.61351, -13.91351, -39.45889, 41.68884, -3.55713, 41.68834, -3.55511, -4.34491, -41.61351, -13.91351, -39.45889, 41.68884, -3.55713, 0, 0, 0, 0, 0, 0, 41.68834, -3.55511, -4.34491, -41.61351, -13.91351, -39.45889, 41.68884, -3.55713, 41.68834, -3.55511, -4.34491, -41.61351, -13.91351, -39.45889, 41.68884, -3.55713, 62.02298, 0.07178, -1.10449, -62.01328, -15.51135, -60.05266, 62.02283, 0.06897, 62.02298, 0.07178, -1.10449, -62.01328, -15.51135, -60.05266, 62.02283, 0.06897, 62.02298, 0.07178, -1.10449, -62.01328, 62.02283, 0.06897, 62.02298, 0.07178, -1.10449, -62.01328, 62.02283, 0.06897, 50.35049, 1.65991, 0.70532, -50.37336, -11.03986, -49.15504, 50.35425, 1.64978, 31.3202, 4.63962, 4.04517, -31.40303, -3.37537, -31.48354, 31.32495, 4.62842, 12.78652, 8.57083, 8.32837, -12.94725, 5.08606, -14.53181, 12.79016, 8.56128, -4.36848, 12.7605, 12.84259, 4.12519, 13.45062, 1.0206, -4.36401, 12.75122, 0, 0, 0, 0, 0, 0, 0, 0, 57.81524, 1.00946, -0.08588, -57.82439, 57.81787, 0.99915, 56.93067, 3.12338, 2.04248, -56.98011, -11.27704, -55.89192, 56.93445, 3.11169, 44.2856, 7.30746, -4.05084, -44.70357, 44.28857, 7.2959, 17.98521, 13.47659, 13.13281, -18.23804, 8.5282, -20.79619, 17.98889, 13.46558, 4.1871, 16.48849, 16.40558, -4.49944, 14.90948, -8.19733, 4.18982, 16.47693, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49.22267, 5.67383, -6.87225, -49.07181, 49.22534, 5.66345, 62.02298, 0.07178, -1.10449, -62.01328, 62.02283, 0.06897, 62.02298, 0.07178, -1.10449, -62.01328, 62.02283, 0.06897, 62.02298, 0.07178, -1.10449, -62.01328, 62.02283, 0.06897, 62.02298, 0.07178, -1.10449, -62.01328, 62.02283, 0.06897, 70.4846, -10.50815, -11.84399, -70.27272, -27.87787, -65.58498, 70.48438, -10.51208, 70.4846, -10.50815, -11.84399, -70.27272, -27.87787, -65.58498, 70.48438, -10.51208, 22.1238, 8.60995, 8.18909, -22.28312, 2.77637, -23.57764, 22.12415, 8.60815, 22.1238, 8.60995, 8.18909, -22.28312, 2.77637, -23.57764, 22.12415, 8.60815, 41.68834, -3.55511, -4.34491, -41.61351, -13.91351, -39.45889, 41.68884, -3.55713, 70.4846, -10.50815, -11.84399, -70.27272, -27.87787, -65.58498, 70.48438, -10.51208, 34.5658, -1.97452, -2.63037, -34.52234, -10.59424, -32.96231, 34.56763, -1.9812], "curve": "stepped"}, {"time": 0.3667, "offset": 126, "vertices": [-20.63281, 1.70456, -5.17426, 20.04664, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -35.11204, 22.68375, 9.8764, 40.61902, 11.55908, 40.17249, -35.11157, 22.68466, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.12109, 9.90927, 10.71375, -0.63309, 10.67859, -1.0786, 4.12085, 9.90985, 4.01577, 14.26691, 14.79437, 0.89937, 14.81937, 0.28265, 4.01587, 14.26755, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -28.37288, 17.69879, 7.38474, 32.61534, 8.73642, 32.27953, -28.3728, 17.69958], "curve": 0.05, "c2": 0.1, "c3": 0.388}, {"time": 0.4667, "offset": 78, "vertices": [-34.32773, -6.87796, -21.67944, 27.49018, -23.10071, 26.30693, -34.33008, -6.87769, -40.62703, 2.8667, -17.80353, 36.63047, -40.6311, 2.86688, -40.62703, 2.8667, -17.80353, 36.63047, -40.6311, 2.86688, -40.62703, 2.8667, -40.6311, 2.86688, -96.21268, 21.84735, -24.09814, 95.67345, -96.22095, 21.84882, -74.19617, 35.76385, -1.71906, 82.34746, -74.20142, 35.76508, -67.60567, 33.20892, -1.01221, 75.31441, -67.61401, 33.21021, -45.60164, 32.70279, 8.50296, 55.46766, -45.60449, 32.70313, -46.4093, 17.39667, -2.85867, 50.13486, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -10.08522, 41.46884, 32.40347, 27.77254, 30.89166, 29.44485, -10.08936, 41.46936, -26.89102, 52.90549, 34.98834, 47.93569, 32.40857, 49.71635, -26.89624, 52.90643, -33.97672, 48.65891, 27.99332, 52.32981, 25.19092, 53.73491, -33.98242, 48.65961, -36.72369, 32.88736, 12.68799, 47.63585, 10.15503, 48.23931, -36.72778, 32.88818, -23.02959, 31.46387, 17.6207, 34.78199, 15.75983, 35.6638, -23.03589, 31.46472, -16.89853, 15.87662, 6.50082, 22.25633, 5.31647, 22.56838, -35.11204, 22.68375, 9.8764, 40.61902, 11.55908, 40.17249, -35.11157, 22.68466, -35.97383, 21.4588, 2.83859, 41.79152, 0.62805, 41.88285, -35.97876, 21.45959, -43.80962, 28.64127, 5.69296, 52.03035, 2.93787, 52.25813, -43.81714, 28.64264, -44.75594, 24.8634, 1.89621, 51.16303, -0.80792, 51.19171, -44.76099, 24.86438, -45.3251, 19.38553, -3.24503, 49.18961, -45.32788, 19.38611, -45.3251, 19.38553, -3.24503, 49.18961, -45.32788, 19.38611, -22.15642, 17.84076, 5.87082, 27.8337, 4.39301, 28.10481, -22.15967, 17.84119, -39.6669, 7.30441, -11.45337, 38.67342, -13.47931, 38.01459, -39.67163, 7.30469, -18.73228, 17.28232, 6.93643, 23.08118, 5.65436, 22.40307, -18.73682, 17.28333, -26.58469, 23.58081, 9.23883, 32.39965, 7.60837, 31.44566, -26.58789, 23.58206, -36.64323, 10.16901, -7.52979, 37.27514, -9.48755, 36.82537, -36.64526, 10.16913, -25.7812, -3.43927, -14.74316, 21.42764, -25.78638, -3.43854, -46.57011, 18.49609, -4.60199, 49.8966, -7.2301, 49.58375, -46.57617, 18.49677, -46.41653, 4.12033, -19.60852, 42.27236, -46.42041, 4.12073, -34.37366, -2.31049, -17.6283, 29.59958, -19.16632, 28.62724, -34.37817, -2.31012, -56.10207, -10.97281, -35.19199, 45.04882, -37.52155, 43.12741, -56.10864, -10.97235, -66.09949, -15.38358, -43.65271, 51.96421, -46.33545, 49.58641, -66.10742, -15.38342, -52.0723, -9.34778, -31.91849, 42.19159, -34.10156, 40.44718, -52.07666, -9.3476, -67.36891, 19.95175, -16.35498, 68.33102, -67.37378, 19.95227, -95.18568, 35.08975, -11.82611, 100.75576, -95.19092, 35.09082, -103.02104, 42.27225, -8.97144, 110.99435, -103.02783, 42.27307, -82.14462, 48.68326, 6.19965, 95.28548, -82.14966, 48.68427, -70.43159, 52.17206, 14.61493, 86.42281, -70.43823, 52.17334, -53.72186, 37.14655, 8.78665, 64.7197, 5.35699, 65.09329, -53.72754, 37.14764, -45.27109, 33.57529, 13.88593, 54.87149, 14.05331, 54.84784, -45.27368, 33.57629, -40.06935, 27.88843, 6.71643, 48.35491, 4.15356, 48.64209, -40.07104, 27.88861, -28.15639, 32.6925, 16.39496, 39.9096, 14.26459, 40.71956, -28.15918, 32.69293, -24.3567, 21.358, 8.01031, 31.38849, 6.34167, 31.76759, -24.35937, 21.35852, -47.01323, 43.09338, 17.1268, 61.432, 13.85938, 62.25046, -47.01929, 43.0943, -38.31183, -8.49255, -24.92352, 30.31075, -26.4892, 28.95226, -38.31445, -8.49231], "curve": "stepped"}, {"time": 0.7333, "offset": 78, "vertices": [-34.32773, -6.87796, -21.67944, 27.49018, -23.10071, 26.30693, -34.33008, -6.87769, -40.62703, 2.8667, -17.80353, 36.63047, -40.6311, 2.86688, -40.62703, 2.8667, -17.80353, 36.63047, -40.6311, 2.86688, -40.62703, 2.8667, -40.6311, 2.86688, -96.21268, 21.84735, -24.09814, 95.67345, -96.22095, 21.84882, -74.19617, 35.76385, -1.71906, 82.34746, -74.20142, 35.76508, -67.60567, 33.20892, -1.01221, 75.31441, -67.61401, 33.21021, -45.60164, 32.70279, 8.50296, 55.46766, -45.60449, 32.70313, -46.4093, 17.39667, -2.85867, 50.13486, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -10.08522, 41.46884, 32.40347, 27.77254, 30.89166, 29.44485, -10.08936, 41.46936, -26.89102, 52.90549, 34.98834, 47.93569, 32.40857, 49.71635, -26.89624, 52.90643, -33.97672, 48.65891, 27.99332, 52.32981, 25.19092, 53.73491, -33.98242, 48.65961, -36.72369, 32.88736, 12.68799, 47.63585, 10.15503, 48.23931, -36.72778, 32.88818, -23.02959, 31.46387, 17.6207, 34.78199, 15.75983, 35.6638, -23.03589, 31.46472, -16.89853, 15.87662, 6.50082, 22.25633, 5.31647, 22.56838, -35.11204, 22.68375, 9.8764, 40.61902, 11.55908, 40.17249, -35.11157, 22.68466, -35.97383, 21.4588, 2.83859, 41.79152, 0.62805, 41.88285, -35.97876, 21.45959, -43.80962, 28.64127, 5.69296, 52.03035, 2.93787, 52.25813, -43.81714, 28.64264, -44.75594, 24.8634, 1.89621, 51.16303, -0.80792, 51.19171, -44.76099, 24.86438, -45.3251, 19.38553, -3.24503, 49.18961, -45.32788, 19.38611, -45.3251, 19.38553, -3.24503, 49.18961, -45.32788, 19.38611, -22.15642, 17.84076, 5.87082, 27.8337, 4.39301, 28.10481, -22.15967, 17.84119, -39.6669, 7.30441, -11.45337, 38.67342, -13.47931, 38.01459, -39.67163, 7.30469, -18.73228, 17.28232, 6.93643, 23.08118, 5.65436, 22.40307, -18.73682, 17.28333, -26.58469, 23.58081, 9.23883, 32.39965, 7.60837, 31.44566, -26.58789, 23.58206, -36.64323, 10.16901, -7.52979, 37.27514, -9.48755, 36.82537, -36.64526, 10.16913, -25.7812, -3.43927, -14.74316, 21.42764, -25.78638, -3.43854, -46.57011, 18.49609, -4.60199, 49.8966, -7.2301, 49.58375, -46.57617, 18.49677, -46.41653, 4.12033, -19.60852, 42.27236, -46.42041, 4.12073, -34.37366, -2.31049, -17.6283, 29.59958, -19.16632, 28.62724, -34.37817, -2.31012, -56.10207, -10.97281, -35.19199, 45.04882, -37.52155, 43.12741, -56.10864, -10.97235, -66.09949, -15.38358, -43.65271, 51.96421, -46.33545, 49.58641, -66.10742, -15.38342, -52.0723, -9.34778, -31.91849, 42.19159, -34.10156, 40.44718, -52.07666, -9.3476, -67.36891, 19.95175, -16.35498, 68.33102, -67.37378, 19.95227, -95.18568, 35.08975, -11.82611, 100.75576, -95.19092, 35.09082, -103.02104, 42.27225, -8.97144, 110.99435, -103.02783, 42.27307, -82.14462, 48.68326, 6.19965, 95.28548, -82.14966, 48.68427, -70.43159, 52.17206, 14.61493, 86.42281, -70.43823, 52.17334, -53.72186, 37.14655, 8.78665, 64.7197, 5.35699, 65.09329, -53.72754, 37.14764, -45.27109, 33.57529, 13.88593, 54.87149, 14.05331, 54.84784, -45.27368, 33.57629, -40.06935, 27.88843, 6.71643, 48.35491, 4.15356, 48.64209, -40.07104, 27.88861, -28.15639, 32.6925, 16.39496, 39.9096, 14.26459, 40.71956, -28.15918, 32.69293, -24.3567, 21.358, 8.01031, 31.38849, 6.34167, 31.76759, -24.35937, 21.35852, -47.01323, 43.09338, 17.1268, 61.432, 13.85938, 62.25046, -47.01929, 43.0943, -38.31183, -8.49255, -24.92352, 30.31075, -26.4892, 28.95226, -38.31445, -8.49231], "curve": 0.25, "c4": 0.89}, {"time": 0.8333, "offset": 126, "vertices": [-20.63281, 1.70456, -5.17426, 20.04664, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -35.11204, 22.68375, 9.8764, 40.61902, 11.55908, 40.17249, -35.11157, 22.68466, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.12109, 9.90927, 10.71375, -0.63309, 10.67859, -1.0786, 4.12085, 9.90985, 4.01577, 14.26691, 14.79437, 0.89937, 14.81937, 0.28265, 4.01587, 14.26755, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -28.37288, 17.69879, 7.38474, 32.61534, 8.73642, 32.27953, -28.3728, 17.69958], "curve": 0.05, "c2": 0.05, "c3": 0.75}, {"time": 0.9333}]}}}}, "carry": {"slots": {"archer5": {"attachment": [{"time": 0.0667, "name": "archer5_1"}, {"time": 0.1333, "name": "archer5_2"}, {"time": 0.2, "name": "archer5_3"}, {"time": 0.3333, "name": "archer5_2"}, {"time": 0.4, "name": "archer5_1"}, {"time": 0.4667, "name": "archer5"}]}, "archer8": {"attachment": [{"time": 0.0667, "name": "archer8_1"}, {"time": 0.1333, "name": "archer8_2"}, {"time": 0.2, "name": "archer8_3"}, {"time": 0.3333, "name": "archer8_2"}, {"time": 0.4, "name": "archer8_1"}, {"time": 0.4667, "name": "archer8"}]}, "archer24": {"attachment": [{"name": null}]}, "zuojiao": {"attachment": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}]}}, "bones": {"archer7": {"rotate": [{"angle": -20.32, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -35.96, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 47.96, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 2.61, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -9.61, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -20.32}], "translate": [{"x": -5.59, "y": 25.15, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 109, "y": 26.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 306.03, "y": 55.9, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 361.4, "y": 4.19, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 186.11, "y": 4.19, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 43.24, "y": 22.86, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -5.59, "y": 25.15}]}, "archer64": {"translate": [{"curve": "stepped"}, {"time": 0.0667, "x": -7.34, "y": 0.82, "curve": "stepped"}, {"time": 0.1333, "x": -18.51, "y": 2.07, "curve": "stepped"}, {"time": 0.2, "x": -25.88, "y": 2.9, "curve": "stepped"}, {"time": 0.2667, "x": -25.88, "y": 2.9, "curve": "stepped"}, {"time": 0.3333, "x": -18.53, "y": 2.08, "curve": "stepped"}, {"time": 0.4, "x": -7.37, "y": 0.82, "curve": "stepped"}, {"time": 0.4667}]}, "archer65": {"translate": [{"curve": "stepped"}, {"time": 0.0667, "x": -7.34, "y": 0.82, "curve": "stepped"}, {"time": 0.1333, "x": -18.51, "y": 2.07, "curve": "stepped"}, {"time": 0.2, "x": -25.88, "y": 2.9, "curve": "stepped"}, {"time": 0.2667, "x": -25.88, "y": 2.9, "curve": "stepped"}, {"time": 0.3333, "x": -18.53, "y": 2.08, "curve": "stepped"}, {"time": 0.4, "x": -7.37, "y": 0.82, "curve": "stepped"}, {"time": 0.4667}]}, "archer13": {"rotate": [{"angle": 4.41, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.5, "angle": -0.15, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.41}]}, "archer12": {"rotate": [{"angle": 7.53}]}, "archer2": {"translate": [{"y": 3.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "y": -11.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "y": 17.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "y": 3.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "y": -11.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "y": 17.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "y": 3.56}]}, "archer5": {"rotate": [{"angle": 3.31}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 46.39, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "archer8": {"rotate": [{"angle": -2.85}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -115.96, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "archer20": {"rotate": [{"angle": 54}]}, "archer10": {"rotate": [{"angle": 15.05, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -0.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.57, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -29.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -72.98, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -6.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 15.05}], "translate": [{"y": 9.78, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -190.05, "y": -4.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -329.78, "y": 6.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -392.67, "y": 43.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -260.08, "y": 106.24, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -145.08, "y": 70.06, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 9.78}]}, "archer16": {"rotate": [{"angle": 72.97}], "translate": [{"x": -2.57, "y": -10.63}]}, "archer46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "archer88": {"translate": [{"x": -304.26, "y": -1185.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 175.6, "y": -1769.97, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -304.26, "y": -1185.1}]}, "archer17": {"rotate": [{"angle": 30.5}]}, "archer19": {"rotate": [{"angle": -6.68}], "translate": [{"x": 18.36, "y": 71.94}]}, "archer9": {"rotate": [{"angle": 5.69}]}, "archer6": {"rotate": [{"angle": -6.72}]}, "archer11": {"rotate": [{"angle": 4.55}]}, "archer90": {"translate": [{"x": 382.93, "y": 112.63, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 1862.83, "y": -83.34, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 382.93, "y": 112.63}]}, "archer80": {"rotate": [{"angle": -9.62, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.4667, "angle": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -9.9, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1, "angle": -9.62}]}, "archer78": {"rotate": [{"angle": -1.67, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "angle": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -7.03, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1, "angle": -1.67}]}, "archer77": {"rotate": [{"angle": 4.77}], "translate": [{"x": 18.5, "y": 72.47, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 18.5, "y": 72.47}]}, "archer75": {"rotate": [{"angle": -7.47, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4, "angle": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -8.74, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1, "angle": -7.47}]}, "archer73": {"rotate": [{"angle": 1.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1667, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -3.13, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1, "angle": 1.49}]}, "archer72": {"rotate": [{"angle": -2.01}], "translate": [{"x": 18.5, "y": 72.47, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 18.5, "y": 72.47}]}, "archer70": {"rotate": [{"angle": -11.41, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -13.11, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1, "angle": -11.41}]}, "archer68": {"rotate": [{"angle": 0.31, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1333, "angle": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -3.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1, "angle": 0.31}]}, "archer67": {"rotate": [{"angle": 4.1}], "translate": [{"x": -9.68, "y": -37.93, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -9.68, "y": -37.93}]}, "archer21": {"rotate": [{"angle": 0.05}]}, "archer18": {"rotate": [{"angle": -3.93}]}, "archer61": {"rotate": [{"angle": 0.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 2.21, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 0.81}]}, "archer52": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.05, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "archer101": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "archer98": {"rotate": [{"angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 11.65, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1, "angle": 3.55}]}, "archer15": {"rotate": [{"angle": -2.85}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -115.96, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "archer24": {"rotate": [{"angle": 5.69}]}}, "deform": {"default": {"archer19": {"archer19": [{"offset": 50, "vertices": [29.29971, -7.51984, 30.08694, 3.12943, -4.22198, -29.95404, 29.29332, -7.54654, 34.29597, -21.71634, 39.70453, -8.44638, -17.77597, -36.49536, 34.27692, -21.74741, 25.09648, -40.90817, 37.7468, -29.63919, -37.8703, -29.48267, 25.06031, -40.93109, 1.86008, -51.15164, 19.51712, -47.31818, -50.63004, -7.52695, 1.81461, -51.15411, -7.30482, -58.97583, 13.64203, -57.8392, -59.42303, 0.71283, -7.35717, -58.97012, 7.9594, -34.0824, -32.98892, -11.69385, 7.92926, -34.08997, 0.37393, -15.72943, 5.81589, -14.61942, -15.59122, -2.11784, 0.36009, -15.73013, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.65785, -2.04446, 2.26492, -1.34097, -1.84824, -1.8748, 1.65612, -2.04636, -9.59277, 13.20718, 15.13028, 6.12254, -4.39181, 15.7204, -19.50644, 16.1344, 20.36295, 15.03761, -12.66525, 21.91742, -22.72079, 19.44901, 24.35477, 17.35721, -14.52385, 26.1438, -1.06073, 26.6265, 26.09552, -5.39435, 8.27985, 25.32803, 9.41519, 28.42468, 25.31342, -15.9948, 18.72619, 23.3649, 15.01956, 31.9303, 27.3634, -22.2794, 25.20063, 24.69891, 10.52824, 16.75058, 17.81543, -8.60416, 10.54322, 16.74084, 6.82907, 19.4855, 17.26202, -11.32849, 13.18834, 15.88617, 15.41138, 19.40753, 7.70767, 23.5531, 20.99744, -13.16295, 15.42916, 19.39246, 23.2359, 10.2424, 18.22931, 17.6778, 12.75748, -21.95602, 23.24511, 10.22086, 23.76955, 8.45932, 19.3493, 16.19141, 11.04501, -22.68439, 23.77737, 8.43759, 20.46471, 8.5159, 16.23059, 15.09619, 10.73398, -19.39375, 20.47247, 8.49689, 20.08981, 0.78247, 18.56607, 7.71429, 3.00705, -19.87933, 20.09064, 0.76407, 8.1993, -1.43173, 8.18588, 1.50641, -0.51334, -8.30786, 8.1981, -1.43961, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29.07297, 2.72372, 26.31503, 12.65588, 5.93338, -28.59158, 29.07557, 2.69714, 30.57253, 10.19681, 25.12466, 20.1843, 13.52679, -29.25235, 30.58175, 10.1691, 46.44272, 26.38715, 14.40375, -51.43718, 52.72552, 8.55841, 61.43883, -14.0397, 62.48911, 8.18283, -7.13422, -62.61823, 61.42662, -14.09518, 53.01184, -25.82767, 58.6828, -5.79868, -19.78439, -55.5515, 52.98892, -25.87534]}]}}}, "drawOrder": [{"offsets": [{"slot": "archer16", "offset": 14}]}]}, "carry_idle": {"slots": {"archer5": {"attachment": [{"time": 0.1, "name": "archer5_1"}, {"time": 0.1667, "name": "archer5_2"}, {"time": 0.2667, "name": "archer5_3"}, {"time": 0.4333, "name": "archer5_2"}, {"time": 0.5333, "name": "archer5_1"}, {"time": 0.6333, "name": "archer5"}]}, "archer8": {"attachment": [{"time": 0.1, "name": "archer8_1"}, {"time": 0.1667, "name": "archer8_2"}, {"time": 0.2667, "name": "archer8_3"}, {"time": 0.4333, "name": "archer8_2"}, {"time": 0.5333, "name": "archer8_1"}, {"time": 0.6333, "name": "archer8"}]}, "archer24": {"attachment": [{"name": null}]}}, "bones": {"archer64": {"translate": [{"curve": "stepped"}, {"time": 0.1, "x": -7.34, "y": 0.82, "curve": "stepped"}, {"time": 0.1667, "x": -18.51, "y": 2.07, "curve": "stepped"}, {"time": 0.2667, "x": -25.88, "y": 2.9, "curve": "stepped"}, {"time": 0.3667, "x": -25.88, "y": 2.9, "curve": "stepped"}, {"time": 0.4333, "x": -18.53, "y": 2.08, "curve": "stepped"}, {"time": 0.5333, "x": -7.37, "y": 0.82, "curve": "stepped"}, {"time": 0.6333}]}, "archer65": {"translate": [{"curve": "stepped"}, {"time": 0.1, "x": -7.34, "y": 0.82, "curve": "stepped"}, {"time": 0.1667, "x": -18.51, "y": 2.07, "curve": "stepped"}, {"time": 0.2667, "x": -25.88, "y": 2.9, "curve": "stepped"}, {"time": 0.3667, "x": -25.88, "y": 2.9, "curve": "stepped"}, {"time": 0.4333, "x": -18.53, "y": 2.08, "curve": "stepped"}, {"time": 0.5333, "x": -7.37, "y": 0.82, "curve": "stepped"}, {"time": 0.6333}]}, "archer12": {"rotate": [{"angle": 7.53}]}, "archer20": {"rotate": [{"angle": 54}]}, "archer16": {"rotate": [{"angle": 72.97}], "translate": [{"x": -2.57, "y": -10.63}]}, "archer46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer17": {"rotate": [{"angle": 30.5}]}, "archer19": {"rotate": [{"angle": -6.68}], "translate": [{"x": 18.36, "y": 71.94}]}, "archer9": {"rotate": [{"angle": 5.69}]}, "archer6": {"rotate": [{"angle": -6.72}]}, "archer11": {"rotate": [{"angle": 4.55}]}, "archer80": {"rotate": [{"angle": -9.62, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.6333, "angle": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -9.9, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.3333, "angle": -9.62}]}, "archer78": {"rotate": [{"angle": -1.67, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -7.03, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -1.67}]}, "archer77": {"rotate": [{"angle": 4.77}], "translate": [{"x": 18.5, "y": 72.47, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 18.5, "y": 72.47}]}, "archer75": {"rotate": [{"angle": -7.47, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "angle": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -8.74, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -7.47}]}, "archer73": {"rotate": [{"angle": 1.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2333, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -3.13, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": 1.49}]}, "archer72": {"rotate": [{"angle": -2.01}], "translate": [{"x": 18.5, "y": 72.47, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 18.5, "y": 72.47}]}, "archer21": {"rotate": [{"angle": 0.05}]}, "archer18": {"rotate": [{"angle": -3.93}]}, "archer61": {"rotate": [{"angle": 0.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 2.21, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": 0.81}]}, "archer52": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -2.05, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer101": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer98": {"rotate": [{"angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 11.65, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": 3.55}]}, "archer15": {"rotate": [{"angle": -2.85}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -115.96, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer24": {"rotate": [{"angle": 5.69}]}, "archer13": {"rotate": [{"angle": 0.18, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 0.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": 0.18}]}, "archer2": {"translate": [{"y": 4.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 9.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "y": 4.95}]}, "archer5": {"rotate": [{"angle": 3.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.39}]}, "archer8": {"rotate": [{"angle": -2.93, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.93}]}, "archer70": {"rotate": [{"angle": -11.41, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -13.11, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -11.41}]}, "archer68": {"rotate": [{"angle": 0.31, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -3.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": 0.31}]}, "archer67": {"rotate": [{"angle": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.1}]}}}, "carry_run": {"slots": {"archer5": {"attachment": [{"time": 0.0333, "name": "archer5_1"}, {"time": 0.0667, "name": "archer5_2"}, {"time": 0.1, "name": "archer5_3"}, {"time": 0.1667, "name": "archer5_2"}, {"time": 0.2, "name": "archer5_1"}, {"time": 0.2333, "name": "archer5"}]}, "archer8": {"attachment": [{"time": 0.0333, "name": "archer8_1"}, {"time": 0.0667, "name": "archer8_2"}, {"time": 0.1, "name": "archer8_3"}, {"time": 0.1667, "name": "archer8_2"}, {"time": 0.2, "name": "archer8_1"}, {"time": 0.2333, "name": "archer8"}]}, "archer24": {"attachment": [{"name": null}]}, "zuojiao": {"attachment": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}]}}, "bones": {"archer7": {"rotate": [{"angle": -51.44, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -66.66, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -104.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -65.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -37.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -15.86, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 43.71, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -51.44}], "translate": [{"x": -136.7, "y": 123.12, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -159.02, "y": 272.64, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -96.53, "y": 254.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 108.78, "y": 149.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 189.12, "y": 185.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 352.03, "y": 152.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 410.06, "y": 36.09, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 308.41, "y": 36.43, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -136.7, "y": 123.12}]}, "archer64": {"translate": [{"curve": "stepped"}, {"time": 0.0333, "x": -7.34, "y": 0.82, "curve": "stepped"}, {"time": 0.0667, "x": -18.51, "y": 2.07, "curve": "stepped"}, {"time": 0.1, "x": -25.88, "y": 2.9, "curve": "stepped"}, {"time": 0.1333, "x": -25.88, "y": 2.9, "curve": "stepped"}, {"time": 0.1667, "x": -18.53, "y": 2.08, "curve": "stepped"}, {"time": 0.2, "x": -7.37, "y": 0.82, "curve": "stepped"}, {"time": 0.2333}]}, "archer65": {"translate": [{"curve": "stepped"}, {"time": 0.0333, "x": -7.34, "y": 0.82, "curve": "stepped"}, {"time": 0.0667, "x": -18.51, "y": 2.07, "curve": "stepped"}, {"time": 0.1, "x": -25.88, "y": 2.9, "curve": "stepped"}, {"time": 0.1333, "x": -25.88, "y": 2.9, "curve": "stepped"}, {"time": 0.1667, "x": -18.53, "y": 2.08, "curve": "stepped"}, {"time": 0.2, "x": -7.37, "y": 0.82, "curve": "stepped"}, {"time": 0.2333}]}, "archer13": {"rotate": [{"angle": 4.41, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "angle": -0.15, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 4.41}]}, "archer12": {"rotate": [{"angle": 7.53}]}, "archer5": {"rotate": [{"angle": 3.31}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 46.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "archer8": {"rotate": [{"angle": -2.85}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -115.96, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "archer20": {"rotate": [{"angle": 54}]}, "archer16": {"rotate": [{"angle": 72.97}], "translate": [{"x": -2.57, "y": -10.63}]}, "archer46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "archer17": {"rotate": [{"angle": 30.5}]}, "archer19": {"rotate": [{"angle": -6.68}], "translate": [{"x": 18.36, "y": 71.94}]}, "archer9": {"rotate": [{"angle": 5.69}]}, "archer6": {"rotate": [{"angle": -6.72}]}, "archer11": {"rotate": [{"angle": 4.55}]}, "archer80": {"rotate": [{"angle": -9.62, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.2333, "angle": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -9.9, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.5333, "angle": -9.62}]}, "archer78": {"rotate": [{"angle": -1.67, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -7.03, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5333, "angle": -1.67}]}, "archer77": {"rotate": [{"angle": 4.77}], "translate": [{"x": 18.5, "y": 72.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 18.5, "y": 72.47}]}, "archer75": {"rotate": [{"angle": -7.47, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2, "angle": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -8.74, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5333, "angle": -7.47}]}, "archer73": {"rotate": [{"angle": 1.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -3.13, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5333, "angle": 1.49}]}, "archer72": {"rotate": [{"angle": -2.01}], "translate": [{"x": 18.5, "y": 72.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 18.5, "y": 72.47}]}, "archer21": {"rotate": [{"angle": 0.05}]}, "archer18": {"rotate": [{"angle": -3.93}]}, "archer61": {"rotate": [{"angle": 0.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 2.21, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5333, "angle": 0.81}]}, "archer52": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -2.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "archer101": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "archer98": {"rotate": [{"angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 11.65, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5333, "angle": 3.55}]}, "archer15": {"rotate": [{"angle": -2.85}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -115.96, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "archer24": {"rotate": [{"angle": 5.69}]}, "archer10": {"rotate": [{"angle": -73.02, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 16.06, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 32.02, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 1.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -73.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -86.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -89.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -116.25, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -73.02}], "translate": [{"x": -90.1, "y": 180.45, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 97.36, "y": 122.43, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 208.94, "y": 64.41, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -134.73, "y": 46.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -377.98, "y": 126.89, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -456.09, "y": 287.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -442.7, "y": 249.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -351.17, "y": 225.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -90.1, "y": 180.45}]}, "archer2": {"rotate": [{"angle": -13.34}], "translate": [{"y": 6.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "y": 33.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "y": -49.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "y": -69.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "y": 6.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "y": 33.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "y": -49.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "y": -69.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "y": 6.71}]}, "archer68": {"rotate": [{"angle": -20.07}, {"time": 0.1, "angle": -36.46}, {"time": 0.2, "angle": 17.88}, {"time": 0.3, "angle": 29.88}, {"time": 0.4, "angle": 0.42}, {"time": 0.5333, "angle": -20.07}]}, "archer70": {"rotate": [{"angle": 2.68}, {"time": 0.1, "angle": -0.3}, {"time": 0.2, "angle": -23.05}, {"time": 0.3, "angle": -2.08}, {"time": 0.4, "angle": -28.04}, {"time": 0.5333, "angle": 2.68}]}, "archer67": {"rotate": [{"angle": -21.94}, {"time": 0.1, "angle": 1.87}, {"time": 0.2, "angle": -27.84}, {"time": 0.3, "angle": -41.52}, {"time": 0.4, "angle": -11.64}, {"time": 0.5333, "angle": -21.94}]}, "archer88": {"translate": [{"x": 538.5, "y": -1517.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 222.68, "y": -803.17, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 538.5, "y": -1517.3}]}, "archer90": {"translate": [{"x": 3665.72, "y": 866.87, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 291.61, "y": -355.87, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 3665.72, "y": 866.87}]}}, "deform": {"default": {"archer19": {"archer19": [{"offset": 50, "vertices": [29.29971, -7.51984, 30.08694, 3.12943, -4.22198, -29.95404, 29.29332, -7.54654, 34.29597, -21.71634, 39.70453, -8.44638, -17.77597, -36.49536, 34.27692, -21.74741, 25.09648, -40.90817, 37.7468, -29.63919, -37.8703, -29.48267, 25.06031, -40.93109, 1.86008, -51.15164, 19.51712, -47.31818, -50.63004, -7.52695, 1.81461, -51.15411, -7.30482, -58.97583, 13.64203, -57.8392, -59.42303, 0.71283, -7.35717, -58.97012, 7.9594, -34.0824, -32.98892, -11.69385, 7.92926, -34.08997, 0.37393, -15.72943, 5.81589, -14.61942, -15.59122, -2.11784, 0.36009, -15.73013, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.65785, -2.04446, 2.26492, -1.34097, -1.84824, -1.8748, 1.65612, -2.04636, -9.59277, 13.20718, 15.13028, 6.12254, -4.39181, 15.7204, -19.50644, 16.1344, 20.36295, 15.03761, -12.66525, 21.91742, -22.72079, 19.44901, 24.35477, 17.35721, -14.52385, 26.1438, -1.06073, 26.6265, 26.09552, -5.39435, 8.27985, 25.32803, 9.41519, 28.42468, 25.31342, -15.9948, 18.72619, 23.3649, 15.01956, 31.9303, 27.3634, -22.2794, 25.20063, 24.69891, 10.52824, 16.75058, 17.81543, -8.60416, 10.54322, 16.74084, 6.82907, 19.4855, 17.26202, -11.32849, 13.18834, 15.88617, 15.41138, 19.40753, 7.70767, 23.5531, 20.99744, -13.16295, 15.42916, 19.39246, 23.2359, 10.2424, 18.22931, 17.6778, 12.75748, -21.95602, 23.24511, 10.22086, 23.76955, 8.45932, 19.3493, 16.19141, 11.04501, -22.68439, 23.77737, 8.43759, 20.46471, 8.5159, 16.23059, 15.09619, 10.73398, -19.39375, 20.47247, 8.49689, 20.08981, 0.78247, 18.56607, 7.71429, 3.00705, -19.87933, 20.09064, 0.76407, 8.1993, -1.43173, 8.18588, 1.50641, -0.51334, -8.30786, 8.1981, -1.43961, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29.07297, 2.72372, 26.31503, 12.65588, 5.93338, -28.59158, 29.07557, 2.69714, 30.57253, 10.19681, 25.12466, 20.1843, 13.52679, -29.25235, 30.58175, 10.1691, 46.44272, 26.38715, 14.40375, -51.43718, 52.72552, 8.55841, 61.43883, -14.0397, 62.48911, 8.18283, -7.13422, -62.61823, 61.42662, -14.09518, 53.01184, -25.82767, 58.6828, -5.79868, -19.78439, -55.5515, 52.98892, -25.87534]}]}}}}, "cut": {"slots": {"BarbarianAxe": {"attachment": [{"name": "BarbarianAxe"}]}, "archer5": {"attachment": [{"time": 0.2667, "name": "archer5_3"}, {"time": 0.3667, "name": "archer5_2"}, {"time": 0.4, "name": "archer5_1"}, {"time": 0.4333, "name": "archer5"}]}, "archer8": {"attachment": [{"time": 0.2667, "name": "archer8_3"}, {"time": 0.3667, "name": "archer8_2"}, {"time": 0.4, "name": "archer8_1"}, {"time": 0.4333, "name": "archer8"}]}, "archer9": {"attachment": [{"time": 0.2667, "name": "zui2"}, {"time": 0.4333, "name": "archer9"}]}, "archer24": {"attachment": [{"name": null}]}}, "bones": {"archer94": {"rotate": [{"angle": -43.67}]}, "archer22": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -49.38, "curve": 0, "c2": 0.09, "c3": 0.36}, {"time": 0.5667, "angle": -36.98, "curve": 0.25, "c4": 0.79}, {"time": 0.7333}]}, "archer64": {"translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -50.43, "y": -4.12, "curve": "stepped"}, {"time": 0.3333, "x": -50.43, "y": -4.12, "curve": 0, "c2": 0.09, "c3": 0.36}, {"time": 0.4333}]}, "archer65": {"translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -50.43, "y": -4.12, "curve": "stepped"}, {"time": 0.3333, "x": -50.43, "y": -4.12, "curve": 0, "c2": 0.09, "c3": 0.36}, {"time": 0.4333}]}, "archer13": {"rotate": [{"angle": 5.31, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": 6.4, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "angle": 5.45, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2667, "angle": 0.39, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "angle": -3.56, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "angle": 2.31, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": 5.31}]}, "archer12": {"rotate": [{"angle": 3.9, "curve": "stepped"}, {"time": 0.2333, "angle": 3.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 0.34, "curve": "stepped"}, {"time": 0.5667, "angle": 0.34, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "angle": 3.9}]}, "archer17": {"rotate": [{"angle": 14.64, "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 0.1667, "angle": 20.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.2333, "angle": 25.04, "curve": 0.321, "c2": 0.28, "c3": 0.655, "c4": 0.62}, {"time": 0.2667, "angle": -4.69, "curve": "stepped"}, {"time": 0.5667, "angle": -4.69, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.8333, "angle": 14.64}]}, "archer16": {"rotate": [{"angle": 132.13, "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 0.1667, "angle": 128.79, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.2333, "angle": 109.33, "curve": 0.321, "c2": 0.28, "c3": 0.655, "c4": 0.62}, {"time": 0.2667, "angle": 88.82, "curve": "stepped"}, {"time": 0.5667, "angle": 88.82, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.8333, "angle": 132.13}]}, "archer2": {"rotate": [{"angle": -0.33, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": 0.74, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -10.17, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "angle": -8.12, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "angle": -3.29, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": -0.33}], "translate": [{"x": -37.89, "y": -6.91, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "x": -40.36, "y": -6.91, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "x": 3.3, "y": -29.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "x": 5.1, "y": -32.27, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "x": -13.77, "y": -13.4, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "x": -31.08, "y": -6.91, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "x": -37.89, "y": -6.91}]}, "archer5": {"rotate": [{"angle": 41.05, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": 3.31, "curve": "stepped"}, {"time": 0.7333, "angle": 3.31, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": 41.05}], "translate": [{"x": -44.09, "y": -3.01}]}, "archer8": {"rotate": [{"angle": -0.69, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": -2.85, "curve": "stepped"}, {"time": 0.7333, "angle": -2.85, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": -0.69}]}, "archer20": {"rotate": [{"angle": 36.05, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": 36.48, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "angle": 55.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 48.1, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "angle": 27.19, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "angle": 34.89, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": 36.05}]}, "archer19": {"rotate": [{"angle": 79.98, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": 84.09, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "angle": 72.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -8.91, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "angle": 2.28, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "angle": 68.63, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": 79.98}], "translate": [{"x": 56.9, "y": 68.87}]}, "archer7": {"rotate": [{"angle": 5.73}], "translate": [{"x": 22.93}]}, "archer10": {"translate": [{"x": -29.82}]}, "archer88": {"translate": [{"x": 1173.21, "y": -102.26, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "x": 1345.24, "y": 11.13, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "x": 772.82, "y": -574.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -723.28, "y": -922.16, "curve": 0, "c2": 0.09, "c3": 0.36}, {"time": 0.5667, "x": -290.58, "y": -491.58, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "x": 699.24, "y": -414.69, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "x": 1173.21, "y": -102.26}]}, "archer11": {"rotate": [{"angle": 2.94, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": 3.09, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "angle": 3.39, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "angle": -3.7, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "angle": 0.09, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "angle": 2.55, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": 2.94}]}, "archer9": {"rotate": [{"angle": 5.69}]}, "archer6": {"rotate": [{"angle": -56.33, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": -6.72, "curve": "stepped"}, {"time": 0.7333, "angle": -6.72, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": -56.33}]}, "archer90": {"translate": [{"x": 134.76, "y": 1522.28, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "x": -195.85, "y": 1690.47, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "x": 520.31, "y": 1293.85, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 709.38, "y": -612.03, "curve": 0, "c2": 0.09, "c3": 0.36}, {"time": 0.5667, "x": 281.3, "y": -176.85, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "x": 1045.7, "y": 1058.86, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "x": 134.76, "y": 1522.28}]}, "archer80": {"rotate": [{"angle": -1.6}, {"time": 0.1, "angle": -12.01}, {"time": 0.2333, "angle": 12.71}, {"time": 0.2667, "angle": 6.37}, {"time": 0.5667, "angle": -9.62}, {"time": 0.7333, "angle": 8.81}, {"time": 0.8333, "angle": -1.6}]}, "archer78": {"rotate": [{"angle": -17.43}, {"time": 0.1, "angle": -16.93}, {"time": 0.2333, "angle": -21.45}, {"time": 0.2667, "angle": -9.56}, {"time": 0.5667, "angle": -1.67}, {"time": 0.7333, "angle": -17.94}, {"time": 0.8333, "angle": -17.43}]}, "archer77": {"rotate": [{"angle": 3.55}, {"time": 0.1, "angle": 11.15}, {"time": 0.2333, "angle": 2.03}, {"time": 0.2667, "angle": -20.68}, {"time": 0.5667, "angle": 4.77}, {"time": 0.7333, "angle": -4.05}, {"time": 0.8333, "angle": 3.55}]}, "archer75": {"rotate": [{"angle": -0.14}, {"time": 0.1, "angle": -5.54}, {"time": 0.2333, "angle": -32.65}, {"time": 0.2667, "angle": -10.87}, {"time": 0.5667, "angle": -7.47}, {"time": 0.7333, "angle": 5.26}, {"time": 0.8333, "angle": -0.14}]}, "archer73": {"rotate": [{"angle": -15.54}, {"time": 0.1, "angle": -14.26}, {"time": 0.2333, "angle": -8.71}, {"time": 0.2667, "angle": -18.87}, {"time": 0.5667, "angle": 1.49}, {"time": 0.7333, "angle": -16.82}, {"time": 0.8333, "angle": -15.54}]}, "archer72": {"rotate": [{"angle": -1.82}, {"time": 0.1, "angle": 4.71}, {"time": 0.2333, "angle": -0.45}, {"time": 0.2667, "angle": -15.77}, {"time": 0.5667, "angle": -2.01}, {"time": 0.7333, "angle": -8.34}, {"time": 0.8333, "angle": -1.82}]}, "archer70": {"rotate": [{"angle": -2.22}, {"time": 0.1, "angle": -14.15}, {"time": 0.2333, "angle": -5.01}, {"time": 0.2667, "angle": 10.18}, {"time": 0.5667, "angle": -11.41}, {"time": 0.7333, "angle": 9.71}, {"time": 0.8333, "angle": -2.22}]}, "archer68": {"rotate": [{"angle": -17.27}, {"time": 0.1, "angle": -17.53}, {"time": 0.2333, "angle": -21.6}, {"time": 0.2667, "angle": -10.61}, {"time": 0.5667, "angle": 0.31}, {"time": 0.7333, "angle": -17.01}, {"time": 0.8333, "angle": -17.27}]}, "archer67": {"rotate": [{"angle": 1.58}, {"time": 0.1, "angle": 6.74}, {"time": 0.2333, "angle": 8.51}, {"time": 0.2667, "angle": -16.76}, {"time": 0.5667, "angle": 4.1}, {"time": 0.7333, "angle": -3.58}, {"time": 0.8333, "angle": 1.58}]}, "archer21": {"rotate": [{"angle": 2.67, "curve": "stepped"}, {"time": 0.2333, "angle": 2.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 0.05, "curve": "stepped"}, {"time": 0.5667, "angle": 0.05, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "angle": 2.67}]}, "archer18": {"rotate": [{"angle": -0.45}]}, "archer61": {"rotate": [{"angle": 5.3}, {"time": 0.2333, "angle": 8.11}, {"time": 0.2667, "angle": -12.76}, {"time": 0.5667, "angle": 0.81}, {"time": 0.7333, "angle": 4.09}, {"time": 0.8333, "angle": 5.3}]}, "ren": {"translate": [{"x": -39.81, "y": 1574.55}]}, "archer15": {"rotate": [{"angle": -0.69, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": -2.85, "curve": "stepped"}, {"time": 0.7333, "angle": -2.85, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": -0.69}]}, "archer24": {"rotate": [{"angle": 5.69}]}}, "drawOrder": [{"offsets": [{"slot": "archer17", "offset": 14}, {"slot": "archer16", "offset": 14}]}]}, "die": {"slots": {"archer5": {"attachment": [{"time": 0.1, "name": "archer5_4"}]}, "archer8": {"attachment": [{"time": 0.1, "name": "archer8_4"}]}, "archer9": {"attachment": [{"time": 0.1, "name": "zui3"}, {"time": 1.2667, "name": "zui2"}]}}, "bones": {"archer13": {"rotate": [{"angle": 0.18, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1667, "angle": -5.91, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 16.22, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 19.28, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 10.39, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 8.63}]}, "archer12": {"rotate": [{"angle": 0.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": -16.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -10.56, "curve": 0.394, "c4": 0.92}, {"time": 0.5, "angle": -7.55}, {"time": 0.7, "angle": -4.41, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -10.74, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -11.51, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -13.28}]}, "archer17": {"rotate": [{"angle": 3.81, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 28.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 4.23, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 7.66, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7, "angle": 18.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2667, "angle": 66.79}]}, "archer16": {"rotate": [{"angle": -2.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": -8.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -1.97, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": -17.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7, "angle": -31.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2667, "angle": 49.43}]}, "archer2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 9.48, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 11.26, "curve": 0.394, "c4": 0.92}, {"time": 0.5, "angle": -13.18}, {"time": 0.7, "angle": -76.97, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -82.87, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -90.41}], "translate": [{"y": 4.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "x": -40.26, "y": 4.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "x": -61.33, "y": 12.85, "curve": 0.394, "c4": 0.92}, {"time": 0.5, "x": 131.8, "y": -12.1}, {"time": 0.7, "x": 478.57, "y": -361.41, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 473.47, "y": -246.67, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": 473.47, "y": -323.16, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 493.07, "y": -330.51}]}, "archer5": {"rotate": [{"angle": 3.39, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 3.31}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 0.962}]}, "archer8": {"rotate": [{"angle": -2.93, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -2.85}]}, "archer20": {"rotate": [{"angle": -1.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 1.65, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 38.14, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 31.4}]}, "archer19": {"rotate": [{"angle": 2.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -8.83, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 105.08, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 120.61}]}, "archer6": {"rotate": [{"angle": -6.72}]}, "archer7": {"rotate": [{"time": 0.2667, "curve": 0.394, "c4": 0.92}, {"time": 0.5, "angle": -61.45}, {"time": 0.7, "angle": -143.32, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -110.25, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -98.44}], "translate": [{"time": 0.2667, "curve": 0.394, "c4": 0.92}, {"time": 0.5, "x": 66.5, "y": 96.52}, {"time": 0.7, "x": 451.51, "y": 341.3, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 420.91, "y": 267.36, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": 406.21, "y": 125.22, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 433.17, "y": 110.51}]}, "archer10": {"rotate": [{"time": 0.2667, "curve": 0.394, "c4": 0.92}, {"time": 0.5, "angle": -18.21}, {"time": 0.7, "angle": -145.84, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -116.08, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -109.63}], "translate": [{"time": 0.2667, "curve": 0.394, "c4": 0.92}, {"time": 0.5, "x": 33.3, "y": 32.22}, {"time": 0.7, "x": -25.35, "y": 90.87, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -10.05, "y": 55.17, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": -17.4, "y": -18.35, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 19.36, "y": -20.8}]}, "archer11": {"rotate": [{"angle": 0.09, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1, "angle": 9.27, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "angle": 9.33, "curve": 0.394, "c4": 0.92}, {"time": 0.5, "angle": 9.6}, {"time": 0.7, "angle": 14.98, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 5.24, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 0.99, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 0.54}]}, "archer9": {"rotate": [{"angle": 5.69}]}, "archer80": {"rotate": [{"angle": -9.62, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.1, "angle": 5.87}, {"time": 0.5, "angle": 6.74}, {"time": 0.7667, "angle": -20.67}, {"time": 1.2667, "angle": -2.73}]}, "archer78": {"rotate": [{"angle": -1.67, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 5.43}, {"time": 0.5, "angle": -20.89}, {"time": 0.7667, "angle": -16.01}, {"time": 1.2667, "angle": 10.76}]}, "archer77": {"rotate": [{"angle": 4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -12.55}, {"time": 0.5, "angle": -18.23}, {"time": 0.7667, "angle": 18.53}, {"time": 1.2667, "angle": 23.11}]}, "archer75": {"rotate": [{"angle": -7.47, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1, "angle": 3.93}, {"time": 0.5, "angle": 17.76}, {"time": 0.7667, "angle": -12.99}, {"time": 1.2667, "angle": -11.55}]}, "archer73": {"rotate": [{"angle": 1.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": 7.78}, {"time": 0.5, "angle": -13.11}, {"time": 0.7667, "angle": -10.74}, {"time": 1.2667, "angle": 20.61}]}, "archer72": {"rotate": [{"angle": -2.01, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -14.64}, {"time": 0.5, "angle": -12.21}, {"time": 0.7667, "angle": 20.22}, {"time": 1.2667, "angle": 22.95}]}, "archer70": {"rotate": [{"angle": -11.41, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1, "angle": 3.02}, {"time": 0.5, "angle": 8.65}, {"time": 0.7667, "angle": -13.13}, {"time": 1.2667, "angle": 1.06}]}, "archer68": {"rotate": [{"angle": 0.31, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1, "angle": 13.82}, {"time": 0.5, "angle": -27.23}, {"time": 0.7667, "angle": -19.64}, {"time": 1.2667, "angle": 15.17}]}, "archer67": {"rotate": [{"angle": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -15.72}, {"time": 0.5, "angle": 4.11}, {"time": 0.7667, "angle": 24.37}, {"time": 1.2667, "angle": 27.37}]}, "archer21": {"rotate": [{"angle": 0.05, "curve": "stepped"}, {"time": 0.7, "angle": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 1.49}], "translate": [{"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": -6.25, "y": 26.34}]}, "archer18": {"rotate": [{"angle": -0.45, "curve": "stepped"}, {"time": 0.7, "angle": -0.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2667, "angle": 9.66}]}, "archer61": {"rotate": [{"angle": 0.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": 14.49}, {"time": 0.6, "angle": -4.21}, {"time": 1.2667, "angle": 0.81}]}, "archer98": {"rotate": [{"angle": 3.55}]}, "archer15": {"rotate": [{"angle": -2.93, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -2.85}]}, "archer24": {"rotate": [{"angle": 5.69}]}}}, "dig": {"slots": {"BarbarianAxe": {"attachment": [{"name": "barb"}]}, "archer5": {"attachment": [{"time": 0.2667, "name": "archer5_3"}, {"time": 0.3667, "name": "archer5_2"}, {"time": 0.4, "name": "archer5_1"}, {"time": 0.4333, "name": "archer5"}]}, "archer8": {"attachment": [{"time": 0.2667, "name": "archer8_3"}, {"time": 0.3667, "name": "archer8_2"}, {"time": 0.4, "name": "archer8_1"}, {"time": 0.4333, "name": "archer8"}]}, "archer9": {"attachment": [{"time": 0.2667, "name": "zui2"}, {"time": 0.4333, "name": "archer9"}]}, "archer24": {"attachment": [{"name": null}]}}, "bones": {"archer94": {"rotate": [{"angle": -43.67}]}, "archer22": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -49.38, "curve": 0, "c2": 0.09, "c3": 0.36}, {"time": 0.5667, "angle": -36.98, "curve": 0.25, "c4": 0.79}, {"time": 0.7333}]}, "archer64": {"translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -50.43, "y": -4.12, "curve": "stepped"}, {"time": 0.3333, "x": -50.43, "y": -4.12, "curve": 0, "c2": 0.09, "c3": 0.36}, {"time": 0.4333}]}, "archer65": {"translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -50.43, "y": -4.12, "curve": "stepped"}, {"time": 0.3333, "x": -50.43, "y": -4.12, "curve": 0, "c2": 0.09, "c3": 0.36}, {"time": 0.4333}]}, "archer13": {"rotate": [{"angle": 5.31, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": 6.4, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "angle": 5.45, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2667, "angle": 0.39, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "angle": -3.56, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "angle": 2.31, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": 5.31}]}, "archer12": {"rotate": [{"angle": 3.9, "curve": "stepped"}, {"time": 0.2333, "angle": 3.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 0.34, "curve": "stepped"}, {"time": 0.5667, "angle": 0.34, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "angle": 3.9}]}, "archer17": {"rotate": [{"angle": 14.64, "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 0.1667, "angle": 20.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.2333, "angle": 25.04, "curve": 0.321, "c2": 0.28, "c3": 0.655, "c4": 0.62}, {"time": 0.2667, "angle": -4.69, "curve": "stepped"}, {"time": 0.5667, "angle": -4.69, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.8333, "angle": 14.64}]}, "archer16": {"rotate": [{"angle": 132.13, "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 0.1667, "angle": 128.79, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.2333, "angle": 109.33, "curve": 0.321, "c2": 0.28, "c3": 0.655, "c4": 0.62}, {"time": 0.2667, "angle": 88.82, "curve": "stepped"}, {"time": 0.5667, "angle": 88.82, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.8333, "angle": 132.13}]}, "archer2": {"rotate": [{"angle": -0.33, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": 0.74, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -10.17, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "angle": -8.12, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "angle": -3.29, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": -0.33}], "translate": [{"x": -37.89, "y": -6.91, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "x": -40.36, "y": -6.91, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "x": 3.3, "y": -29.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "x": 5.1, "y": -32.27, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "x": -13.77, "y": -13.4, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "x": -31.08, "y": -6.91, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "x": -37.89, "y": -6.91}]}, "archer5": {"rotate": [{"angle": 41.05, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": 3.31, "curve": "stepped"}, {"time": 0.7333, "angle": 3.31, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": 41.05}], "translate": [{"x": -44.09, "y": -3.01}]}, "archer8": {"rotate": [{"angle": -0.69, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": -2.85, "curve": "stepped"}, {"time": 0.7333, "angle": -2.85, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": -0.69}]}, "archer20": {"rotate": [{"angle": 36.05, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": 36.48, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "angle": 55.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 48.1, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "angle": 27.19, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "angle": 34.89, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": 36.05}]}, "archer19": {"rotate": [{"angle": 79.98, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": 84.09, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "angle": 72.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -8.91, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "angle": 2.28, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "angle": 68.63, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": 79.98}], "translate": [{"x": 56.9, "y": 68.87}]}, "archer7": {"rotate": [{"angle": 5.73}], "translate": [{"x": 22.93}]}, "archer10": {"translate": [{"x": -29.82}]}, "archer88": {"translate": [{"x": 1173.21, "y": -102.26, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "x": 1345.24, "y": 11.13, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "x": 772.82, "y": -574.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -723.28, "y": -922.16, "curve": 0, "c2": 0.09, "c3": 0.36}, {"time": 0.5667, "x": -290.58, "y": -491.58, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "x": 699.24, "y": -414.69, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "x": 1173.21, "y": -102.26}]}, "archer11": {"rotate": [{"angle": 2.94, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": 3.09, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "angle": 3.39, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "angle": -3.7, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "angle": 0.09, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "angle": 2.55, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": 2.94}]}, "archer9": {"rotate": [{"angle": 5.69}]}, "archer6": {"rotate": [{"angle": -56.33, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": -6.72, "curve": "stepped"}, {"time": 0.7333, "angle": -6.72, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": -56.33}]}, "archer90": {"translate": [{"x": 134.76, "y": 1522.28, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "x": -195.85, "y": 1690.47, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "x": 520.31, "y": 1293.85, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 709.38, "y": -612.03, "curve": 0, "c2": 0.09, "c3": 0.36}, {"time": 0.5667, "x": 281.3, "y": -176.85, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "x": 1045.7, "y": 1058.86, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "x": 134.76, "y": 1522.28}]}, "archer80": {"rotate": [{"angle": -1.6}, {"time": 0.1, "angle": -12.01}, {"time": 0.2333, "angle": 12.71}, {"time": 0.2667, "angle": 6.37}, {"time": 0.5667, "angle": -9.62}, {"time": 0.7333, "angle": 8.81}, {"time": 0.8333, "angle": -1.6}]}, "archer78": {"rotate": [{"angle": -17.43}, {"time": 0.1, "angle": -16.93}, {"time": 0.2333, "angle": -21.45}, {"time": 0.2667, "angle": -9.56}, {"time": 0.5667, "angle": -1.67}, {"time": 0.7333, "angle": -17.94}, {"time": 0.8333, "angle": -17.43}]}, "archer77": {"rotate": [{"angle": 3.55}, {"time": 0.1, "angle": 11.15}, {"time": 0.2333, "angle": 2.03}, {"time": 0.2667, "angle": -20.68}, {"time": 0.5667, "angle": 4.77}, {"time": 0.7333, "angle": -4.05}, {"time": 0.8333, "angle": 3.55}]}, "archer75": {"rotate": [{"angle": -0.14}, {"time": 0.1, "angle": -5.54}, {"time": 0.2333, "angle": -32.65}, {"time": 0.2667, "angle": -10.87}, {"time": 0.5667, "angle": -7.47}, {"time": 0.7333, "angle": 5.26}, {"time": 0.8333, "angle": -0.14}]}, "archer73": {"rotate": [{"angle": -15.54}, {"time": 0.1, "angle": -14.26}, {"time": 0.2333, "angle": -8.71}, {"time": 0.2667, "angle": -18.87}, {"time": 0.5667, "angle": 1.49}, {"time": 0.7333, "angle": -16.82}, {"time": 0.8333, "angle": -15.54}]}, "archer72": {"rotate": [{"angle": -1.82}, {"time": 0.1, "angle": 4.71}, {"time": 0.2333, "angle": -0.45}, {"time": 0.2667, "angle": -15.77}, {"time": 0.5667, "angle": -2.01}, {"time": 0.7333, "angle": -8.34}, {"time": 0.8333, "angle": -1.82}]}, "archer70": {"rotate": [{"angle": -2.22}, {"time": 0.1, "angle": -14.15}, {"time": 0.2333, "angle": -5.01}, {"time": 0.2667, "angle": 10.18}, {"time": 0.5667, "angle": -11.41}, {"time": 0.7333, "angle": 9.71}, {"time": 0.8333, "angle": -2.22}]}, "archer68": {"rotate": [{"angle": -17.27}, {"time": 0.1, "angle": -17.53}, {"time": 0.2333, "angle": -21.6}, {"time": 0.2667, "angle": -10.61}, {"time": 0.5667, "angle": 0.31}, {"time": 0.7333, "angle": -17.01}, {"time": 0.8333, "angle": -17.27}]}, "archer67": {"rotate": [{"angle": 1.58}, {"time": 0.1, "angle": 6.74}, {"time": 0.2333, "angle": 8.51}, {"time": 0.2667, "angle": -16.76}, {"time": 0.5667, "angle": 4.1}, {"time": 0.7333, "angle": -3.58}, {"time": 0.8333, "angle": 1.58}]}, "archer21": {"rotate": [{"angle": 2.67, "curve": "stepped"}, {"time": 0.2333, "angle": 2.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 0.05, "curve": "stepped"}, {"time": 0.5667, "angle": 0.05, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "angle": 2.67}]}, "archer18": {"rotate": [{"angle": -0.45}]}, "archer61": {"rotate": [{"angle": 5.3}, {"time": 0.2333, "angle": 8.11}, {"time": 0.2667, "angle": -12.76}, {"time": 0.5667, "angle": 0.81}, {"time": 0.7333, "angle": 4.09}, {"time": 0.8333, "angle": 5.3}]}, "ren": {"translate": [{"x": -39.81, "y": 1574.55}]}, "archer15": {"rotate": [{"angle": -0.69, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": -2.85, "curve": "stepped"}, {"time": 0.7333, "angle": -2.85, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": -0.69}]}, "archer24": {"rotate": [{"angle": 5.69}]}}, "drawOrder": [{"offsets": [{"slot": "archer17", "offset": 14}, {"slot": "archer16", "offset": 14}]}]}, "dig_02": {"slots": {"archer5": {"attachment": [{"time": 0.3333, "name": "archer5_3"}, {"time": 0.3667, "name": "archer5_2"}, {"time": 0.4, "name": "archer5_1"}, {"time": 0.4333, "name": "archer5"}]}, "archer8": {"attachment": [{"time": 0.3333, "name": "archer8_3"}, {"time": 0.3667, "name": "archer8_2"}, {"time": 0.4, "name": "archer8_1"}, {"time": 0.4333, "name": "archer8"}]}, "archer9": {"attachment": [{"time": 0.3333, "name": "zui2"}, {"time": 0.4333, "name": "archer9"}]}, "archer24": {"attachment": [{"name": null}]}, "barb3": {"attachment": [{"name": "barb3"}]}}, "bones": {"archer22": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -49.38, "curve": 0, "c2": 0.09, "c3": 0.36}, {"time": 0.5667, "angle": -36.98, "curve": 0.25, "c4": 0.79}, {"time": 0.7333}]}, "archer94": {"rotate": [{"angle": -43.67}, {"time": 0.2333, "angle": -65.94}, {"time": 0.3333, "angle": -72.85}, {"time": 0.5667, "angle": -81.4}, {"time": 0.8333, "angle": -43.67}], "translate": [{}, {"time": 0.5667, "x": -51.45, "y": -97.7}, {"time": 0.8333}]}, "archer64": {"translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -50.43, "y": -4.12, "curve": 0, "c2": 0.09, "c3": 0.36}, {"time": 0.4333}]}, "archer65": {"translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -50.43, "y": -4.12, "curve": 0, "c2": 0.09, "c3": 0.36}, {"time": 0.4333}]}, "archer13": {"rotate": [{"angle": 5.31, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": 6.4, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "angle": 5.45, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3333, "angle": 0.39, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "angle": -3.56, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "angle": 2.31, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": 5.31}]}, "archer12": {"rotate": [{"angle": 3.9, "curve": "stepped"}, {"time": 0.2333, "angle": 3.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 0.34, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "angle": -3.22, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "angle": 3.9}]}, "archer17": {"rotate": [{"angle": 14.64, "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 0.1667, "angle": 20.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.2333, "angle": 25.04, "curve": 0.321, "c2": 0.28, "c3": 0.655, "c4": 0.62}, {"time": 0.3333, "angle": -4.69, "curve": "stepped"}, {"time": 0.5667, "angle": -4.69, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.8333, "angle": 14.64}]}, "archer16": {"rotate": [{"angle": 80.98, "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 0.2333, "angle": 128.79, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.3333, "angle": 88.82, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5667, "angle": 63.17, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.8333, "angle": 80.98}]}, "archer2": {"rotate": [{"angle": -0.33, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": 0.74, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -10.17, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "angle": -15.03, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "angle": -3.29, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": -0.33}], "translate": [{"x": -37.89, "y": -6.91, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "x": -40.36, "y": -6.91, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "x": 3.3, "y": -29.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": 5.1, "y": -32.27, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "x": -13.77, "y": -13.4, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "x": -31.08, "y": -6.91, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "x": -37.89, "y": -6.91}]}, "archer5": {"rotate": [{"angle": 41.05, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": 3.31, "curve": "stepped"}, {"time": 0.7333, "angle": 3.31, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": 41.05}], "translate": [{"x": -44.09, "y": -3.01}]}, "archer8": {"rotate": [{"angle": -0.69, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": -2.85, "curve": "stepped"}, {"time": 0.7333, "angle": -2.85, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": -0.69}]}, "archer20": {"rotate": [{"angle": 36.05, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": 36.48, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "angle": 55.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 48.1, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "angle": 20.25, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "angle": 34.89, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": 36.05}]}, "archer19": {"rotate": [{"angle": 8.14, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.2333, "angle": 72.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -18.56, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "angle": -21.9, "curve": 0.25, "c4": 0.79}, {"time": 0.8333, "angle": 8.14}], "translate": [{"x": 56.9, "y": 68.87}]}, "archer7": {"rotate": [{"angle": 5.73}], "translate": [{"x": 22.93}]}, "archer10": {"translate": [{"x": -29.82}]}, "archer88": {"translate": [{"x": 1173.21, "y": -102.26, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "x": 1345.24, "y": 11.13, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "x": 772.82, "y": -574.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -723.28, "y": -922.16, "curve": 0, "c2": 0.09, "c3": 0.36}, {"time": 0.5667, "x": -290.58, "y": -491.58, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "x": 699.24, "y": -414.69, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "x": 1173.21, "y": -102.26}]}, "archer11": {"rotate": [{"angle": 2.94, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": 3.09, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "angle": 3.39, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -3.7, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "angle": -2.43, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "angle": 2.55, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": 2.94}]}, "archer9": {"rotate": [{"angle": 5.69}]}, "archer6": {"rotate": [{"angle": -56.33, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": -6.72, "curve": "stepped"}, {"time": 0.7333, "angle": -6.72, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": -56.33}]}, "archer90": {"translate": [{"x": 134.76, "y": 1522.28, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "x": -195.85, "y": 1690.47, "curve": 0.25, "c4": 0.71}, {"time": 0.2333, "x": 520.31, "y": 1293.85, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 709.38, "y": -612.03, "curve": 0, "c2": 0.09, "c3": 0.36}, {"time": 0.5667, "x": 281.3, "y": -176.85, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "x": 1045.7, "y": 1058.86, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "x": 134.76, "y": 1522.28}]}, "archer80": {"rotate": [{"angle": -1.6}, {"time": 0.1, "angle": -12.01}, {"time": 0.2333, "angle": 12.71}, {"time": 0.3333, "angle": 6.37}, {"time": 0.5667, "angle": -9.62}, {"time": 0.7333, "angle": 8.81}, {"time": 0.8333, "angle": -1.6}]}, "archer78": {"rotate": [{"angle": -17.43}, {"time": 0.1, "angle": -16.93}, {"time": 0.2333, "angle": -21.45}, {"time": 0.3333, "angle": -9.56}, {"time": 0.5667, "angle": -1.67}, {"time": 0.7333, "angle": -17.94}, {"time": 0.8333, "angle": -17.43}]}, "archer77": {"rotate": [{"angle": 3.55}, {"time": 0.1, "angle": 11.15}, {"time": 0.2333, "angle": 2.03}, {"time": 0.3333, "angle": -20.68}, {"time": 0.5667, "angle": 4.77}, {"time": 0.7333, "angle": -4.05}, {"time": 0.8333, "angle": 3.55}]}, "archer75": {"rotate": [{"angle": -0.14}, {"time": 0.1, "angle": -5.54}, {"time": 0.2333, "angle": -32.65}, {"time": 0.3333, "angle": -10.87}, {"time": 0.5667, "angle": -7.47}, {"time": 0.7333, "angle": 5.26}, {"time": 0.8333, "angle": -0.14}]}, "archer73": {"rotate": [{"angle": -15.54}, {"time": 0.1, "angle": -14.26}, {"time": 0.2333, "angle": -8.71}, {"time": 0.3333, "angle": -18.87}, {"time": 0.5667, "angle": 1.49}, {"time": 0.7333, "angle": -16.82}, {"time": 0.8333, "angle": -15.54}]}, "archer72": {"rotate": [{"angle": -1.82}, {"time": 0.1, "angle": 4.71}, {"time": 0.2333, "angle": -0.45}, {"time": 0.3333, "angle": -15.77}, {"time": 0.5667, "angle": -2.01}, {"time": 0.7333, "angle": -8.34}, {"time": 0.8333, "angle": -1.82}]}, "archer70": {"rotate": [{"angle": -2.22}, {"time": 0.1, "angle": -14.15}, {"time": 0.2333, "angle": -5.01}, {"time": 0.3333, "angle": 10.18}, {"time": 0.5667, "angle": -11.41}, {"time": 0.7333, "angle": 9.71}, {"time": 0.8333, "angle": -2.22}]}, "archer68": {"rotate": [{"angle": -17.27}, {"time": 0.1, "angle": -17.53}, {"time": 0.2333, "angle": -21.6}, {"time": 0.3333, "angle": -10.61}, {"time": 0.5667, "angle": 0.31}, {"time": 0.7333, "angle": -17.01}, {"time": 0.8333, "angle": -17.27}]}, "archer67": {"rotate": [{"angle": 1.58}, {"time": 0.1, "angle": 6.74}, {"time": 0.2333, "angle": 8.51}, {"time": 0.3333, "angle": -16.76}, {"time": 0.5667, "angle": 4.1}, {"time": 0.7333, "angle": -3.58}, {"time": 0.8333, "angle": 1.58}]}, "archer21": {"rotate": [{"angle": 2.67, "curve": "stepped"}, {"time": 0.2333, "angle": 2.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 0.05, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "angle": -6.89, "curve": 0.25, "c4": 0.79}, {"time": 0.7333, "angle": 2.67}]}, "archer18": {"rotate": [{"angle": -45.62, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.2333, "angle": -32.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -36.9, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.5667, "angle": -48.53, "curve": 0.25, "c4": 0.79}, {"time": 0.8333, "angle": -45.62}]}, "archer61": {"rotate": [{"angle": 5.3}, {"time": 0.2333, "angle": 8.11}, {"time": 0.3333, "angle": -12.76}, {"time": 0.5667, "angle": 0.81}, {"time": 0.7333, "angle": 4.09}, {"time": 0.8333, "angle": 5.3}]}, "ren": {"translate": [{"x": -39.81, "y": 1574.55}]}, "archer15": {"rotate": [{"angle": -0.69, "curve": 0.271, "c2": 0.51, "c3": 0.628}, {"time": 0.1, "angle": -2.85, "curve": "stepped"}, {"time": 0.7333, "angle": -2.85, "curve": 0.048, "c2": 0.08, "c3": 0.432, "c4": 0.55}, {"time": 0.8333, "angle": -0.69}]}, "archer24": {"rotate": [{"angle": 5.69}]}}, "drawOrder": [{"offsets": [{"slot": "archer17", "offset": 14}, {"slot": "archer16", "offset": 14}]}]}, "idle": {"slots": {"archer5": {"attachment": [{"time": 0.7333, "name": "archer5_1"}, {"time": 0.8, "name": "archer5_2"}, {"time": 0.8667, "name": "archer5_3"}, {"time": 1, "name": "archer5_2"}, {"time": 1.0667, "name": "archer5_1"}, {"time": 1.1333, "name": "archer5"}]}, "archer8": {"attachment": [{"time": 0.7333, "name": "archer8_1"}, {"time": 0.8, "name": "archer8_2"}, {"time": 0.8667, "name": "archer8_3"}, {"time": 1, "name": "archer8_2"}, {"time": 1.0667, "name": "archer8_1"}, {"time": 1.1333, "name": "archer8"}]}, "archer24": {"attachment": [{"name": null}]}}, "bones": {"archer64": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 9.55, "y": -0.57, "curve": "stepped"}, {"time": 0.7333, "x": 3.2, "y": -0.17, "curve": "stepped"}, {"time": 0.8, "x": -6.47, "y": 0.43, "curve": "stepped"}, {"time": 0.8667, "x": -12.84, "y": 0.83, "curve": "stepped"}, {"time": 0.9333, "x": -12.84, "y": 0.83, "curve": "stepped"}, {"time": 1, "x": -6.49, "y": 0.44, "curve": "stepped"}, {"time": 1.0667, "x": 3.18, "y": -0.17, "curve": "stepped"}, {"time": 1.1333, "x": 9.55, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer65": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 9.55, "y": -0.57, "curve": "stepped"}, {"time": 0.7333, "x": 3.2, "y": -0.17, "curve": "stepped"}, {"time": 0.8, "x": -6.47, "y": 0.43, "curve": "stepped"}, {"time": 0.8667, "x": -12.84, "y": 0.83, "curve": "stepped"}, {"time": 0.9333, "x": -12.84, "y": 0.83, "curve": "stepped"}, {"time": 1, "x": -6.49, "y": 0.43, "curve": "stepped"}, {"time": 1.0667, "x": 3.18, "y": -0.17, "curve": "stepped"}, {"time": 1.1333, "x": 9.55, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer13": {"rotate": [{"angle": 0.18, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 0.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": 0.18}]}, "archer12": {"rotate": [{"angle": 0.34}]}, "archer17": {"rotate": [{"angle": 3.81, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333}, {"time": 1, "angle": 7.61, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 3.81}]}, "archer16": {"rotate": [{"angle": -2.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333}, {"time": 1, "angle": -5.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -2.6}]}, "archer2": {"translate": [{"y": 4.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 9.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "y": 4.95}]}, "archer5": {"rotate": [{"angle": 3.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.39}]}, "archer8": {"rotate": [{"angle": -2.93, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.93}]}, "archer20": {"rotate": [{"angle": -1.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333}, {"time": 1, "angle": -3.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -1.51}]}, "archer19": {"rotate": [{"angle": 2.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333}, {"time": 1, "angle": 4.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 2.28}]}, "archer88": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 524.15, "y": -66.74, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer90": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 257.58, "y": 715.77, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer80": {"rotate": [{"angle": -9.62, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.6333, "angle": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -9.9, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.3333, "angle": -9.62}]}, "archer78": {"rotate": [{"angle": -1.67, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -7.03, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -1.67}]}, "archer77": {"rotate": [{"angle": 4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.03, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.77}]}, "archer75": {"rotate": [{"angle": -7.47, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "angle": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -8.74, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -7.47}]}, "archer73": {"rotate": [{"angle": 1.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -3.13, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": 1.49}]}, "archer72": {"rotate": [{"angle": -2.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.1, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.01}]}, "archer70": {"rotate": [{"angle": -11.41, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -13.11, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -11.41}]}, "archer68": {"rotate": [{"angle": 0.31, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -3.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": 0.31}]}, "archer67": {"rotate": [{"angle": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.1}]}, "archer21": {"rotate": [{"angle": 0.05, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333}, {"time": 1, "angle": 0.09, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 0.05}]}, "archer18": {"rotate": [{"angle": -0.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333}, {"time": 1, "angle": -0.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -0.45}]}, "archer61": {"rotate": [{"angle": 0.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 2.21, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": 0.81}]}, "archer52": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -2.05, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer11": {"rotate": [{"angle": 0.09, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.49, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": 0.09}]}, "archer9": {"rotate": [{"angle": 5.69}]}, "archer6": {"rotate": [{"angle": -6.72}]}, "archer101": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer98": {"rotate": [{"angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 11.65, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": 3.55}]}, "archer15": {"rotate": [{"angle": -2.93, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.93}]}, "archer24": {"rotate": [{"angle": 5.69}]}}}, "idle_02": {"slots": {"archer5": {"attachment": [{"time": 0.7333, "name": "archer5_1"}, {"time": 0.8, "name": "archer5_2"}, {"time": 0.8667, "name": "archer5_3"}, {"time": 1, "name": "archer5_2"}, {"time": 1.0667, "name": "archer5_1"}, {"time": 1.1333, "name": "archer5"}]}, "archer8": {"attachment": [{"time": 0.7333, "name": "archer8_1"}, {"time": 0.8, "name": "archer8_2"}, {"time": 0.8667, "name": "archer8_3"}, {"time": 1, "name": "archer8_2"}, {"time": 1.0667, "name": "archer8_1"}, {"time": 1.1333, "name": "archer8"}]}, "archer24": {"attachment": [{"name": null}]}}, "bones": {"archer64": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 9.55, "y": -0.57, "curve": "stepped"}, {"time": 0.7333, "x": 3.2, "y": -0.17, "curve": "stepped"}, {"time": 0.8, "x": -6.47, "y": 0.43, "curve": "stepped"}, {"time": 0.8667, "x": -12.84, "y": 0.83, "curve": "stepped"}, {"time": 0.9333, "x": -12.84, "y": 0.83, "curve": "stepped"}, {"time": 1, "x": -6.49, "y": 0.44, "curve": "stepped"}, {"time": 1.0667, "x": 3.18, "y": -0.17, "curve": "stepped"}, {"time": 1.1333, "x": 9.55, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer65": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 9.55, "y": -0.57, "curve": "stepped"}, {"time": 0.7333, "x": 3.2, "y": -0.17, "curve": "stepped"}, {"time": 0.8, "x": -6.47, "y": 0.43, "curve": "stepped"}, {"time": 0.8667, "x": -12.84, "y": 0.83, "curve": "stepped"}, {"time": 0.9333, "x": -12.84, "y": 0.83, "curve": "stepped"}, {"time": 1, "x": -6.49, "y": 0.43, "curve": "stepped"}, {"time": 1.0667, "x": 3.18, "y": -0.17, "curve": "stepped"}, {"time": 1.1333, "x": 9.55, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer13": {"rotate": [{"angle": 0.18, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 0.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": 0.18}]}, "archer12": {"rotate": [{"angle": 0.34}]}, "archer17": {"rotate": [{"angle": 24.69}]}, "archer16": {"rotate": [{"angle": 86.02}], "translate": [{"x": -2.57, "y": -10.63}]}, "archer2": {"translate": [{"y": 4.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 9.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "y": 4.95}]}, "archer5": {"rotate": [{"angle": 3.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.39}]}, "archer8": {"rotate": [{"angle": -2.93, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.93}]}, "archer20": {"rotate": [{"angle": 54}]}, "archer19": {"rotate": [{"angle": 6.37}], "translate": [{"x": 18.36, "y": 71.94}]}, "archer88": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 524.15, "y": -66.74, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer90": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 257.58, "y": 715.77, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer80": {"rotate": [{"angle": -9.62, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.6333, "angle": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -9.9, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.3333, "angle": -9.62}]}, "archer78": {"rotate": [{"angle": -1.67, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -7.03, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -1.67}]}, "archer77": {"rotate": [{"angle": 4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.03, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.77}]}, "archer75": {"rotate": [{"angle": -7.47, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "angle": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -8.74, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -7.47}]}, "archer73": {"rotate": [{"angle": 1.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -3.13, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": 1.49}]}, "archer72": {"rotate": [{"angle": -2.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.1, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.01}]}, "archer70": {"rotate": [{"angle": -11.41, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -13.11, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -11.41}]}, "archer68": {"rotate": [{"angle": 0.31, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -3.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": 0.31}]}, "archer67": {"rotate": [{"angle": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.1}]}, "archer21": {"rotate": [{"angle": 0.05}]}, "archer18": {"rotate": [{"angle": -23.67}]}, "archer61": {"rotate": [{"angle": 0.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 2.21, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": 0.81}]}, "archer52": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -2.05, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer11": {"rotate": [{"angle": 0.09, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.49, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": 0.09}]}, "archer9": {"rotate": [{"angle": 5.69}]}, "archer6": {"rotate": [{"angle": -6.72}]}, "archer101": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer98": {"rotate": [{"angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 11.65, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": 3.55}]}, "archer15": {"rotate": [{"angle": -2.93, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.93}]}, "archer24": {"rotate": [{"angle": 5.69}]}}, "drawOrder": [{"offsets": [{"slot": "archer16", "offset": 14}]}]}, "idle_bei": {"slots": {"archer1": {"attachment": [{"name": null}]}, "archer2": {"attachment": [{"name": null}]}, "archer4": {"attachment": [{"name": null}]}, "archer5": {"attachment": [{"name": null}]}, "archer6": {"attachment": [{"name": null}]}, "archer7": {"attachment": [{"name": null}]}, "archer8": {"attachment": [{"name": null}]}, "archer9": {"attachment": [{"name": null}]}, "archer10": {"attachment": [{"name": null}]}, "archer11": {"attachment": [{"name": null}]}, "archer12": {"attachment": [{"name": "archer12_1"}]}, "archer13": {"attachment": [{"name": null}]}, "archer15": {"attachment": [{"name": "archer16"}]}, "archer19": {"attachment": [{"name": null}]}, "archer23": {"attachment": [{"name": "archer23_1"}]}, "archer25": {"attachment": [{"name": "archer20"}]}}, "bones": {"archer29": {"translate": [{"x": -5.84, "y": -209.39}], "scale": [{"y": 0.644}]}, "archer31": {"translate": [{"x": -10.12, "y": -134.6}], "scale": [{"y": 0.71}]}, "archer30": {"rotate": [{"angle": -12.38}], "translate": [{"x": 10.1, "y": -64.62}], "scale": [{"y": 0.12}]}, "archer32": {"translate": [{"x": 20.12, "y": -152.34}], "scale": [{"y": 0.569}]}, "archer13": {"rotate": [{"angle": 0.18, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 0.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": 0.18}], "translate": [{"x": -47.5, "y": -103.15}]}, "archer60": {"translate": [{"x": -12.61, "y": 106.3}]}, "archer12": {"rotate": [{"angle": -1.6}]}, "archer5": {"rotate": [{"angle": 3.31}], "translate": [{"x": 149.64, "y": -2.15}]}, "archer15": {"rotate": [{"angle": -2.85}], "translate": [{"x": -29.72, "y": -6.24, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -29.72, "y": -13.2}, {"time": 1.3333, "x": -29.72, "y": -6.24}]}, "archer72": {"rotate": [{"angle": -2.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.1, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.01}]}, "archer55": {"translate": [{"x": -54.22, "y": -290.71}]}, "archer22": {"scale": [{"x": 1.128, "y": 1.283}]}, "archer8": {"rotate": [{"angle": -2.85}], "translate": [{"x": -147.73, "y": -13.44}]}, "archer17": {"rotate": [{"angle": 18.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 21.2}, {"time": 1.3333, "angle": 18.33}], "translate": [{"x": -56.83, "y": -15.84}]}, "archer19": {"rotate": [{"angle": -41.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -43.39}, {"time": 1.3333, "angle": -41.8}], "translate": [{"x": 116.96, "y": 296.98}]}, "archer90": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 257.58, "y": 715.77, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer88": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 524.15, "y": -66.74, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer16": {"rotate": [{"angle": 83.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 83.17}, {"time": 1.3333, "angle": 83.69}], "translate": [{"x": -133.54, "y": -311.53}]}, "archer67": {"rotate": [{"angle": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.1}]}, "archer7": {"rotate": [{"angle": 9.48}], "translate": [{"x": 337.55, "y": 7.98}]}, "archer39": {"translate": [{"x": 3.48, "y": -142.56}], "scale": [{"y": 0.667}]}, "archer20": {"rotate": [{"angle": 17.91, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 20.83}, {"time": 1.3333, "angle": 17.91}]}, "archer11": {"rotate": [{"angle": -3.58}]}, "archer21": {"rotate": [{"angle": 0.05, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 4.4}, {"time": 1.3333, "angle": 0.05}]}, "archer78": {"rotate": [{"angle": -1.67, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -7.03, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -1.67}]}, "archer51": {"translate": [{"x": -26.37, "y": 35.39}], "scale": [{"y": 0.753}]}, "archer2": {"translate": [{"y": 4.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 9.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "y": 4.95}]}, "archer10": {"rotate": [{"angle": 0.19}], "translate": [{"x": -309.71, "y": 14.57}]}, "archer77": {"rotate": [{"angle": 4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.03, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.77}]}, "archer6": {"rotate": [{"angle": -6.72}]}, "archer70": {"rotate": [{"angle": -11.41, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -13.11, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -11.41}]}, "archer75": {"rotate": [{"angle": -7.47, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "angle": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -8.74, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -7.47}]}, "archer73": {"rotate": [{"angle": 1.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -3.13, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": 1.49}]}, "archer45": {"translate": [{"x": 1.79, "y": -34.83}]}, "archer9": {"rotate": [{"angle": 5.69}]}, "archer68": {"rotate": [{"angle": 0.31, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -3.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": 0.31}]}, "archer18": {"rotate": [{"angle": -8.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -5.38}, {"time": 1.3333, "angle": -8.25}]}, "archer80": {"rotate": [{"angle": -9.62, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.6333, "angle": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -9.9, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.3333, "angle": -9.62}]}, "archer100": {"translate": [{"x": 1.79, "y": -34.83}]}, "archer24": {"rotate": [{"angle": 5.69}]}, "archer3": {"translate": [{"x": -41.44}]}, "wuti": {"rotate": [{"time": 0.6667, "angle": 2.86}]}}, "deform": {"default": {"archer10": {"archer10": [{"vertices": [-21.24329, -257.10358, -21.23761, -257.10565, -21.24329, -257.10358, -21.24756, -257.10803, -21.23761, -257.10565, -21.24329, -257.10358]}]}}}, "drawOrder": [{"offsets": [{"slot": "archer<PERSON>", "offset": 22}]}]}, "idle_help": {"slots": {"archer5": {"attachment": [{"time": 0.7333, "name": "archer5_1"}, {"time": 0.8, "name": "archer5_2"}, {"time": 0.8667, "name": "archer5_3"}, {"time": 1, "name": "archer5_2"}, {"time": 1.0667, "name": "archer5_1"}, {"time": 1.1333, "name": "archer5"}]}, "archer8": {"attachment": [{"time": 0.7333, "name": "archer8_1"}, {"time": 0.8, "name": "archer8_2"}, {"time": 0.8667, "name": "archer8_3"}, {"time": 1, "name": "archer8_2"}, {"time": 1.0667, "name": "archer8_1"}, {"time": 1.1333, "name": "archer8"}]}, "archer15": {"attachment": [{"name": "archer16"}]}, "archer24": {"attachment": [{"name": null}]}}, "bones": {"archer12": {"rotate": [{"angle": 0.34}]}, "archer64": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 9.55, "y": -0.57, "curve": "stepped"}, {"time": 0.7333, "x": 3.2, "y": -0.17, "curve": "stepped"}, {"time": 0.8, "x": -6.47, "y": 0.43, "curve": "stepped"}, {"time": 0.8667, "x": -12.84, "y": 0.83, "curve": "stepped"}, {"time": 0.9333, "x": -12.84, "y": 0.83, "curve": "stepped"}, {"time": 1, "x": -6.49, "y": 0.44, "curve": "stepped"}, {"time": 1.0667, "x": 3.18, "y": -0.17, "curve": "stepped"}, {"time": 1.1333, "x": 9.55, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer65": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 9.55, "y": -0.57, "curve": "stepped"}, {"time": 0.7333, "x": 3.2, "y": -0.17, "curve": "stepped"}, {"time": 0.8, "x": -6.47, "y": 0.43, "curve": "stepped"}, {"time": 0.8667, "x": -12.84, "y": 0.83, "curve": "stepped"}, {"time": 0.9333, "x": -12.84, "y": 0.83, "curve": "stepped"}, {"time": 1, "x": -6.49, "y": 0.43, "curve": "stepped"}, {"time": 1.0667, "x": 3.18, "y": -0.17, "curve": "stepped"}, {"time": 1.1333, "x": 9.55, "y": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer13": {"rotate": [{"angle": 9.26}]}, "archer2": {"rotate": [{"angle": 3.93}], "translate": [{"y": 4.95, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 15.01, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 4.95}]}, "archer5": {"rotate": [{"angle": 3.31}]}, "archer8": {"rotate": [{"angle": -2.85}]}, "archer20": {"rotate": [{"angle": -1.51}]}, "archer19": {"rotate": [{"angle": 121.56}], "translate": [{"x": 33.08, "y": -28.25}]}, "archer17": {"rotate": [{"angle": 3.81}]}, "archer16": {"rotate": [{"angle": 58.41}], "translate": [{"x": -46.49, "y": 39.63}], "scale": [{"x": -1}]}, "archer88": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 524.15, "y": -66.74, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer90": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 257.58, "y": 715.77, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer80": {"rotate": [{"angle": -9.62, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.6333, "angle": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -9.9, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.3333, "angle": -9.62}]}, "archer78": {"rotate": [{"angle": -1.67, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -7.03, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -1.67}]}, "archer77": {"rotate": [{"angle": 4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.03, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.77}]}, "archer75": {"rotate": [{"angle": -7.47, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "angle": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -8.74, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -7.47}]}, "archer73": {"rotate": [{"angle": 1.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -3.13, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": 1.49}]}, "archer72": {"rotate": [{"angle": -2.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.1, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.01}]}, "archer70": {"rotate": [{"angle": -11.41, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -13.11, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -11.41}]}, "archer68": {"rotate": [{"angle": 0.31, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -3.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": 0.31}]}, "archer67": {"rotate": [{"angle": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.1}]}, "archer21": {"rotate": [{"angle": 0.05}]}, "archer18": {"rotate": [{"angle": -0.45}]}, "archer61": {"rotate": [{"angle": 0.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 2.21, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": 0.81}]}, "archer52": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -2.05, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer11": {"rotate": [{"angle": 0.09, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.49, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": 0.09}]}, "archer9": {"rotate": [{"angle": 5.69}]}, "archer6": {"rotate": [{"angle": -6.72}]}, "archer101": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "archer98": {"rotate": [{"angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 11.65, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": 3.55}]}, "ren": {"translate": [{"x": -39.81, "y": 1574.55}]}, "archer15": {"rotate": [{"angle": -2.85}]}, "archer24": {"rotate": [{"angle": 5.69}]}}, "drawOrder": [{"offsets": [{"slot": "archer17", "offset": 13}, {"slot": "archer16", "offset": 13}]}]}, "reng": {"slots": {"archer15": {"attachment": [{"name": "archer16"}]}, "archer24": {"attachment": [{"name": null}]}}, "bones": {"archer12": {"rotate": [{"angle": 0.34, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 2.55}, {"time": 0.2667, "angle": -7.15}, {"time": 0.5, "angle": 0.34}]}, "archer13": {"rotate": [{"angle": 9.26, "curve": "stepped"}, {"time": 0.1667, "angle": 11.74}, {"time": 0.2667, "angle": 8.32}, {"time": 0.5, "angle": 9.26}]}, "archer2": {"rotate": [{"angle": 3.93, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.98}, {"time": 0.2667, "angle": -1.44}, {"time": 0.5, "angle": 3.93}], "translate": [{"y": 4.95, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -7.88, "y": 10.83}, {"time": 0.2667, "x": 33.53, "y": -33.71}, {"time": 0.5, "y": 4.95}]}, "archer5": {"rotate": [{"angle": 3.31}]}, "archer8": {"rotate": [{"angle": -2.85}]}, "archer20": {"rotate": [{"angle": 54}]}, "archer19": {"rotate": [{"angle": -6.68}], "translate": [{"x": 18.36, "y": 71.94}]}, "archer17": {"rotate": [{"angle": 30.5}, {"time": 0.1667, "angle": 22.95}, {"time": 0.2667, "angle": -2.33}, {"time": 0.5, "angle": 30.5}]}, "archer16": {"rotate": [{"angle": 72.97}, {"time": 0.1667, "angle": -130.67}, {"time": 0.2667, "angle": 143.78}, {"time": 0.5, "angle": 72.97}], "translate": [{"x": -2.57, "y": -10.63}, {"time": 0.0333, "x": -65.96, "y": 3.22}, {"time": 0.1667, "x": -41.23, "y": -17.97}, {"time": 0.2667, "x": -118.53, "y": -11.01}, {"time": 0.5, "x": -2.57, "y": -10.63}]}, "archer88": {"translate": [{"curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "y": 1052.18, "curve": 0.324, "c2": 0.31, "c3": 0.664, "c4": 0.66}, {"time": 0.2667, "y": -2432.75, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5}]}, "archer90": {"translate": [{"curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "x": -3561.45, "curve": 0.324, "c2": 0.31, "c3": 0.664, "c4": 0.66}, {"time": 0.2667, "x": 3616.79, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5}]}, "archer80": {"rotate": [{"angle": -9.62}]}, "archer78": {"rotate": [{"angle": -1.67}]}, "archer77": {"rotate": [{"angle": 4.77}]}, "archer75": {"rotate": [{"angle": -7.47}]}, "archer73": {"rotate": [{"angle": 1.49}]}, "archer72": {"rotate": [{"angle": -2.01}]}, "archer70": {"rotate": [{"angle": -11.41}]}, "archer68": {"rotate": [{"angle": 0.31}]}, "archer67": {"rotate": [{"angle": 4.1}]}, "archer21": {"rotate": [{"angle": 0.05}]}, "archer18": {"rotate": [{"angle": -3.93}, {"time": 0.1667, "angle": 7.17, "curve": "stepped"}, {"time": 0.2667, "angle": -24.89, "curve": "stepped"}, {"time": 0.5, "angle": -3.93}]}, "archer61": {"rotate": [{"angle": 0.81}]}, "archer11": {"rotate": [{"angle": 0.09, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": 2.3}, {"time": 0.2667, "angle": -13.25}, {"time": 0.5, "angle": 0.09}]}, "archer9": {"rotate": [{"angle": 5.69}]}, "archer6": {"rotate": [{"angle": -6.72}]}, "archer98": {"rotate": [{"angle": 3.55}]}, "ren": {"translate": [{"x": -39.81, "y": 1574.55}]}, "archer15": {"rotate": [{"angle": -2.85}]}, "archer24": {"rotate": [{"angle": 5.69}]}}, "drawOrder": [{"offsets": [{"slot": "archer16", "offset": 14}]}, {"time": 0.0333, "offsets": [{"slot": "archer17", "offset": 14}, {"slot": "archer16", "offset": 14}]}, {"time": 0.5, "offsets": [{"slot": "archer16", "offset": 14}]}]}, "repel": {"slots": {"archer5": {"attachment": [{"time": 0.0667, "name": "archer5_4"}, {"time": 0.5, "name": "archer5"}]}, "archer8": {"attachment": [{"time": 0.0667, "name": "archer8_4"}, {"time": 0.5, "name": "archer8"}]}, "archer9": {"attachment": [{"name": "zui3"}, {"time": 0.6, "name": "archer9"}]}}, "bones": {"archer13": {"rotate": [{"angle": 0.18, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.0667, "angle": -7.85, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -2.74, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 15.41, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 0.18}]}, "archer12": {"rotate": [{"angle": 0.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "angle": -27.4, "curve": 0.01, "c2": 0.07, "c3": 0.524}, {"time": 0.2, "angle": -11.37, "curve": 0.716, "c3": 0.75}, {"time": 0.4, "angle": 0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 0.34}]}, "archer17": {"rotate": [{"angle": 3.81, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "angle": 4.87, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.3, "angle": 10.68, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 3.81}]}, "archer16": {"rotate": [{"angle": -2.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "angle": -6.82, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.3, "angle": -29.93, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -2.6}]}, "archer2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 9.48, "curve": 0.01, "c2": 0.07, "c3": 0.524}, {"time": 0.2, "angle": 9.84, "curve": 0.716, "c3": 0.75}, {"time": 0.4, "angle": 2.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "translate": [{"y": 4.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "x": -40.26, "y": 4.95, "curve": 0.01, "c2": 0.07, "c3": 0.524}, {"time": 0.2, "x": -185.06, "y": 13.59, "curve": 0.716, "c3": 0.75}, {"time": 0.4, "x": -58.55, "y": -46.78, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "y": 4.95}]}, "archer5": {"rotate": [{"angle": 3.39, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 11.07, "curve": 0.01, "c2": 0.07, "c3": 0.524}, {"time": 0.2, "angle": 16.2, "curve": 0.716, "c3": 0.75}, {"time": 0.4, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 3.39}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 0.962, "curve": 0.01, "c2": 0.07, "c3": 0.524}, {"time": 0.2, "x": 0.898, "curve": 0.716, "c3": 0.75}, {"time": 0.4, "x": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "archer8": {"rotate": [{"angle": -2.93, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 1.6, "curve": 0.01, "c2": 0.07, "c3": 0.524}, {"time": 0.2, "angle": -9.91, "curve": 0.716, "c3": 0.75}, {"time": 0.4, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -2.93}]}, "archer20": {"rotate": [{"angle": -1.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "angle": 2.44, "curve": 0.315, "c2": 0.27, "c3": 0.757}, {"time": 0.3667, "angle": 34.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -1.51}]}, "archer19": {"rotate": [{"angle": 2.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "angle": 2.34, "curve": 0.315, "c2": 0.27, "c3": 0.757}, {"time": 0.3667, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 2.28}]}, "archer88": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 436.66, "y": -498.74, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1667, "x": 1187.05, "y": -1355.81, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -732.71, "y": 1140.45, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "archer7": {"rotate": [{"time": 0.0667, "curve": 0.01, "c2": 0.07, "c3": 0.524}, {"time": 0.2, "angle": -3.58, "curve": 0.716, "c3": 0.75}, {"time": 0.4}], "translate": [{"time": 0.0667, "curve": 0.01, "c2": 0.07, "c3": 0.524}, {"time": 0.2, "x": -46.46, "y": 12.91, "curve": 0.716, "c3": 0.75}, {"time": 0.4}]}, "archer10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -3.6}, {"time": 0.2, "angle": -10.8, "curve": 0.716, "c3": 0.75}, {"time": 0.4}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -26.25, "y": 26.8}, {"time": 0.2, "x": -78.74, "y": 80.41, "curve": 0.716, "c3": 0.75}, {"time": 0.4}]}, "archer6": {"rotate": [{"angle": -6.72, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -33.19, "curve": 0.01, "c2": 0.07, "c3": 0.524}, {"time": 0.2, "angle": -48.62, "curve": 0.716, "c3": 0.75}, {"time": 0.4, "angle": -6.72}]}, "archer9": {"rotate": [{"angle": 5.69, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -18.1, "curve": 0.01, "c2": 0.07, "c3": 0.524}, {"time": 0.2, "angle": 5.69}]}, "archer11": {"rotate": [{"angle": 0.09, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": 10.93, "curve": 0.01, "c2": 0.07, "c3": 0.524}, {"time": 0.2, "angle": 14.74, "curve": 0.716, "c3": 0.75}, {"time": 0.4, "angle": 6.06, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 0.09}]}, "archer90": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 457.87, "y": 479.35, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1667, "x": 1244.7, "y": 1303.08, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -491.72, "y": -1301.89, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "archer80": {"rotate": [{"angle": -9.62, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.0667, "angle": 1.27}, {"time": 0.3, "angle": -18.51}, {"time": 0.6, "angle": -9.62}]}, "archer78": {"rotate": [{"angle": -1.67, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": 8.22}, {"time": 0.3, "angle": -8.8}, {"time": 0.6, "angle": -1.67}]}, "archer77": {"rotate": [{"angle": 4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -16.76}, {"time": 0.3, "angle": 6.22}, {"time": 0.6, "angle": 4.77}]}, "archer75": {"rotate": [{"angle": -7.47, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.0667, "angle": 9.02}, {"time": 0.3, "angle": -11.66}, {"time": 0.6, "angle": -7.47}]}, "archer73": {"rotate": [{"angle": 1.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": 6.38}, {"time": 0.3, "angle": -4.05}, {"time": 0.6, "angle": 1.49}]}, "archer72": {"rotate": [{"angle": -2.01, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -17.41}, {"time": 0.3, "angle": 4.75}, {"time": 0.6, "angle": -2.01}]}, "archer70": {"rotate": [{"angle": -11.41, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.0667, "angle": 5.25}, {"time": 0.3, "angle": -18.29}, {"time": 0.6, "angle": -11.41}]}, "archer68": {"rotate": [{"angle": 0.31, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": 3}, {"time": 0.3, "angle": -15.95}, {"time": 0.6, "angle": 0.31}]}, "archer67": {"rotate": [{"angle": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -1.67}, {"time": 0.3, "angle": 7.03}, {"time": 0.6, "angle": 4.1}]}, "archer21": {"rotate": [{"angle": 0.05, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "angle": 0.94, "curve": 0.315, "c2": 0.27, "c3": 0.757}, {"time": 0.3667, "angle": 8.19, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 0.05}]}, "archer18": {"rotate": [{"angle": -0.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "angle": -1.49, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.3, "angle": -7.14, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -0.45}]}, "archer61": {"rotate": [{"angle": 0.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 15.1}, {"time": 0.3333, "angle": 2.86}, {"time": 0.6, "angle": 0.81}]}, "archer98": {"rotate": [{"angle": 3.55}]}, "archer15": {"rotate": [{"angle": -2.93, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 1.6, "curve": 0.01, "c2": 0.07, "c3": 0.524}, {"time": 0.2, "angle": -9.91, "curve": 0.716, "c3": 0.75}, {"time": 0.4, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -2.93}]}, "archer24": {"rotate": [{"angle": 5.69, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -18.1, "curve": 0.01, "c2": 0.07, "c3": 0.524}, {"time": 0.2, "angle": 5.69}]}}}, "run": {"slots": {"archer24": {"attachment": [{"name": null}]}, "zuojiao": {"attachment": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}]}}, "bones": {"archer7": {"rotate": [{"angle": -51.44, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -66.66, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -104.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -65.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -37.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -15.86, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 43.71, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -51.44}], "translate": [{"x": -136.7, "y": 123.12, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -159.02, "y": 272.64, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -96.53, "y": 254.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 108.78, "y": 149.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 189.12, "y": 185.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 352.03, "y": 152.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 410.06, "y": 36.09, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 308.41, "y": 36.43, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -136.7, "y": 123.12}]}, "archer13": {"rotate": [{"angle": 2.06}], "translate": [{"x": -35.22, "y": -48.97}]}, "archer12": {"rotate": [{"angle": 0.34}]}, "archer2": {"rotate": [{"angle": -13.34}], "translate": [{"y": 6.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "y": 33.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "y": -49.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "y": -69.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "y": 6.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "y": 33.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "y": -49.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "y": -69.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "y": 6.71}]}, "archer5": {"rotate": [{"angle": 3.31}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 93.22, "y": -20.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "archer8": {"rotate": [{"angle": -2.85}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -103.48, "y": 22.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "archer66": {"translate": [{"x": 12.62, "y": -70.53}]}, "archer22": {"rotate": [{"angle": -14.23, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -31.64, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -14.23}]}, "archer6": {"rotate": [{"angle": -6.72}]}, "archer10": {"rotate": [{"angle": -73.02, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 16.06, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 32.02, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 1.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -73.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -86.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -89.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -116.25, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -73.02}], "translate": [{"x": -90.1, "y": 180.45, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 97.36, "y": 122.43, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 208.94, "y": 64.41, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -134.73, "y": 46.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -377.98, "y": 126.89, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -456.09, "y": 287.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -442.7, "y": 249.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -351.17, "y": 225.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -90.1, "y": 180.45}]}, "archer88": {"translate": [{"x": 538.5, "y": -1517.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 222.68, "y": -803.17, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 538.5, "y": -1517.3}]}, "archer20": {"rotate": [{"angle": 69.89, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 55.2, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": 73.29, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333, "angle": 69.89}]}, "archer19": {"rotate": [{"angle": -52.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.16, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -52.55}], "translate": [{"x": 58.07, "y": 140.03, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -29.77, "y": -41.24, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 58.07, "y": 140.03}]}, "archer17": {"rotate": [{"angle": 54.33, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "angle": 69.9, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2667, "angle": 33.55, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": 20.22, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333, "angle": 54.33}]}, "archer16": {"rotate": [{"angle": 55.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -32.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 55.13}], "translate": [{"x": -31.06, "y": -39.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 11.56, "y": 16.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -31.06, "y": -39.1}]}, "archer9": {"rotate": [{"angle": 5.69}]}, "archer90": {"translate": [{"x": 3665.72, "y": 866.87, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 291.61, "y": -355.87, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 3665.72, "y": 866.87}]}, "archer80": {"rotate": [{"angle": 8.34}, {"time": 0.1, "angle": -5.24}, {"time": 0.2, "angle": -23.37}, {"time": 0.3, "angle": -6}, {"time": 0.4, "angle": -4.85}, {"time": 0.5333, "angle": 8.34}]}, "archer78": {"rotate": [{"angle": -30.21}, {"time": 0.1, "angle": -30.77}, {"time": 0.2, "angle": 19.3}, {"time": 0.3, "angle": 28.89}, {"time": 0.4, "angle": 4.29}, {"time": 0.5333, "angle": -30.21}]}, "archer77": {"rotate": [{"angle": -10.2}, {"time": 0.1, "angle": 4.41}, {"time": 0.2, "angle": -25.78}, {"time": 0.3, "angle": -46.27}, {"time": 0.4, "angle": -4.99}, {"time": 0.5333, "angle": -10.2}]}, "archer75": {"rotate": [{"angle": 3.01}, {"time": 0.1, "angle": -34.1}, {"time": 0.2, "angle": -36.57}, {"time": 0.3, "angle": -13.37}, {"time": 0.4, "angle": -27.1}, {"time": 0.5333, "angle": 3.01}]}, "archer73": {"rotate": [{"angle": -23.05}, {"time": 0.1, "angle": -26.22}, {"time": 0.2, "angle": 17.62}, {"time": 0.3, "angle": 9.81}, {"time": 0.4, "angle": 4}, {"time": 0.5333, "angle": -23.05}]}, "archer72": {"rotate": [{"angle": -13.56}, {"time": 0.1, "angle": 7.11}, {"time": 0.2, "angle": -23.17}, {"time": 0.3, "angle": -40.52}, {"time": 0.4, "angle": -2.16}, {"time": 0.5333, "angle": -13.56}]}, "archer70": {"rotate": [{"angle": 2.68}, {"time": 0.1, "angle": -0.3}, {"time": 0.2, "angle": -23.05}, {"time": 0.3, "angle": -2.08}, {"time": 0.4, "angle": -28.04}, {"time": 0.5333, "angle": 2.68}]}, "archer68": {"rotate": [{"angle": -20.07}, {"time": 0.1, "angle": -36.46}, {"time": 0.2, "angle": 17.88}, {"time": 0.3, "angle": 29.88}, {"time": 0.4, "angle": 0.42}, {"time": 0.5333, "angle": -20.07}]}, "archer67": {"rotate": [{"angle": -21.94}, {"time": 0.1, "angle": 1.87}, {"time": 0.2, "angle": -27.84}, {"time": 0.3, "angle": -41.52}, {"time": 0.4, "angle": -11.64}, {"time": 0.5333, "angle": -21.94}]}, "archer21": {"rotate": [{"angle": 5.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.76, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 5.55}]}, "archer18": {"rotate": [{"angle": 3.64, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 3.64}]}, "archer61": {"rotate": [{"angle": 0.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 2.21, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5333, "angle": 0.81}]}, "archer51": {"translate": [{"x": -22.79, "y": 64.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -14.61, "y": 28.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -22.79, "y": 64.22}]}, "archer45": {"translate": [{"x": -22.79, "y": 64.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -14.61, "y": 28.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -22.79, "y": 64.22}]}, "archer11": {"rotate": [{"angle": -4.45}]}, "archer100": {"translate": [{"x": -22.79, "y": 64.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -14.61, "y": 28.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -22.79, "y": 64.22}]}, "archer15": {"rotate": [{"angle": -2.85}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -103.48, "y": 22.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "archer24": {"rotate": [{"angle": 5.69}]}}, "deform": {"default": {"archer19": {"archer19": [{"offset": 50, "vertices": [29.29971, -7.51984, 30.08694, 3.12943, -4.22198, -29.95404, 29.29332, -7.54654, 34.29597, -21.71634, 39.70453, -8.44638, -17.77597, -36.49536, 34.27692, -21.74741, 25.09648, -40.90817, 37.7468, -29.63919, -37.8703, -29.48267, 25.06031, -40.93109, 1.86008, -51.15164, 19.51712, -47.31818, -50.63004, -7.52695, 1.81461, -51.15411, -7.30482, -58.97583, 13.64203, -57.8392, -59.42303, 0.71283, -7.35717, -58.97012, 7.9594, -34.0824, -32.98892, -11.69385, 7.92926, -34.08997, 0.37393, -15.72943, 5.81589, -14.61942, -15.59122, -2.11784, 0.36009, -15.73013, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.65785, -2.04446, 2.26492, -1.34097, -1.84824, -1.8748, 1.65612, -2.04636, -9.59277, 13.20718, 15.13028, 6.12254, -4.39181, 15.7204, -19.50644, 16.1344, 20.36295, 15.03761, -12.66525, 21.91742, -22.72079, 19.44901, 24.35477, 17.35721, -14.52385, 26.1438, -1.06073, 26.6265, 26.09552, -5.39435, 8.27985, 25.32803, 9.41519, 28.42468, 25.31342, -15.9948, 18.72619, 23.3649, 15.01956, 31.9303, 27.3634, -22.2794, 25.20063, 24.69891, 10.52824, 16.75058, 17.81543, -8.60416, 10.54322, 16.74084, 6.82907, 19.4855, 17.26202, -11.32849, 13.18834, 15.88617, 15.41138, 19.40753, 7.70767, 23.5531, 20.99744, -13.16295, 15.42916, 19.39246, 23.2359, 10.2424, 18.22931, 17.6778, 12.75748, -21.95602, 23.24511, 10.22086, 23.76955, 8.45932, 19.3493, 16.19141, 11.04501, -22.68439, 23.77737, 8.43759, 20.46471, 8.5159, 16.23059, 15.09619, 10.73398, -19.39375, 20.47247, 8.49689, 20.08981, 0.78247, 18.56607, 7.71429, 3.00705, -19.87933, 20.09064, 0.76407, 8.1993, -1.43173, 8.18588, 1.50641, -0.51334, -8.30786, 8.1981, -1.43961, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29.07297, 2.72372, 26.31503, 12.65588, 5.93338, -28.59158, 29.07557, 2.69714, 30.57253, 10.19681, 25.12466, 20.1843, 13.52679, -29.25235, 30.58175, 10.1691, 46.44272, 26.38715, 14.40375, -51.43718, 52.72552, 8.55841, 61.43883, -14.0397, 62.48911, 8.18283, -7.13422, -62.61823, 61.42662, -14.09518, 53.01184, -25.82767, 58.6828, -5.79868, -19.78439, -55.5515, 52.98892, -25.87534]}]}}}}, "run_attack": {"slots": {"zuojiao": {"attachment": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}]}}, "bones": {"archer7": {"rotate": [{"angle": -51.44, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -66.66, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -104.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -65.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -37.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -15.86, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 43.71, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -51.44, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -66.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -104.38, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -65.96, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -37.25, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -15.86, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 43.71, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -51.44}], "translate": [{"x": -136.7, "y": 123.12, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -159.02, "y": 272.64, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -96.53, "y": 254.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 108.78, "y": 149.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 189.12, "y": 185.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 352.03, "y": 152.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 410.06, "y": 36.09, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 308.41, "y": 36.43, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -136.7, "y": 123.12, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -159.02, "y": 272.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -96.53, "y": 254.79, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 108.78, "y": 149.9, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 189.12, "y": 185.61, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 352.03, "y": 152.13, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 410.06, "y": 36.09, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 308.41, "y": 36.43, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": -136.7, "y": 123.12}]}, "archer13": {"rotate": [{"angle": 2.06}], "translate": [{"x": -35.22, "y": -48.97}]}, "archer12": {"rotate": [{"angle": 0.34, "curve": 0.342, "c2": 0.36, "c3": 0.683, "c4": 0.72}, {"time": 0.5, "angle": 14.51, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 0.5667, "angle": -26.35, "curve": 0.378, "c2": 0.6, "c3": 0.722}, {"time": 1.0667, "angle": 0.34}]}, "archer2": {"rotate": [{"angle": -13.34}], "translate": [{"y": 6.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "y": 33.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "y": -49.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "y": -69.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "y": 6.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "y": 33.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "y": -49.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "y": -69.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "y": 6.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6, "y": 33.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "y": -49.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7333, "y": -69.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8, "y": 6.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8667, "y": 33.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9333, "y": -49.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "y": -69.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.0667, "y": 6.71}]}, "archer5": {"rotate": [{"angle": 3.31}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 93.22, "y": -20.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 93.22, "y": -20.27, "curve": 0.25, "c3": 0.75}, {"time": 1.0667}]}, "archer8": {"rotate": [{"angle": -2.85}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -103.48, "y": 22.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": -103.48, "y": 22.69, "curve": 0.25, "c3": 0.75}, {"time": 1.0667}]}, "archer66": {"translate": [{"x": 12.62, "y": -70.53}]}, "archer22": {"rotate": [{"angle": -14.23, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -31.64, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -14.23, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -31.64, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -14.23}]}, "archer6": {"rotate": [{"angle": -6.72}]}, "archer10": {"rotate": [{"angle": -73.02, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 16.06, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 32.02, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 1.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -73.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -86.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -89.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -116.25, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -73.02, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 16.06, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 32.02, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 1.54, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -73.02, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -86.44, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -89.5, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -116.25, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -73.02}], "translate": [{"x": -90.1, "y": 180.45, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 97.36, "y": 122.43, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 208.94, "y": 64.41, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -134.73, "y": 46.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -377.98, "y": 126.89, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -456.09, "y": 287.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -442.7, "y": 249.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -351.17, "y": 225.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -90.1, "y": 180.45, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 97.36, "y": 122.43, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 208.94, "y": 64.41, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -134.73, "y": 46.55, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": -377.98, "y": 126.89, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": -456.09, "y": 287.57, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -442.7, "y": 249.63, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -351.17, "y": 225.49, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": -90.1, "y": 180.45}]}, "archer88": {"translate": [{"x": 538.5, "y": -1517.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 222.68, "y": -803.17, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 538.5, "y": -1517.3, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 222.68, "y": -803.17, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 538.5, "y": -1517.3}]}, "archer20": {"rotate": [{"angle": 69.89, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 22.39, "curve": 0.324, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.5, "angle": 35.03, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.5667, "angle": 14.56, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 1.0667, "angle": 69.89}]}, "archer19": {"rotate": [{"angle": -52.55, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 19, "curve": 0.324, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.5, "angle": 93.16, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.5667, "angle": 47.71, "curve": 0.347, "c2": 0.38, "c3": 0.69, "c4": 0.74}, {"time": 0.8333, "angle": -24.29, "curve": 0.377, "c2": 0.61, "c3": 0.719}, {"time": 1.0667, "angle": -52.55}], "translate": [{"x": 58.07, "y": 140.03, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.5667, "x": 71.48, "y": -65.72, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 1.0667, "x": 58.07, "y": 140.03}]}, "archer17": {"rotate": [{"angle": 54.33, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 0.0667, "angle": 69.9, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2667, "angle": 33.55, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": 20.22, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.4667, "angle": 47.41, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5333, "angle": 54.33, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6, "angle": 20.26, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.8, "angle": 32.95, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.8667, "angle": 20.22, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.0667, "angle": 54.33}]}, "archer16": {"rotate": [{"angle": 55.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -32.99, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4667, "angle": 39.73, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5333, "angle": 55.13, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6, "angle": -10.77, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.8, "angle": 4.97, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 55.13}], "translate": [{"x": -31.06, "y": -39.1, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 0.2667, "x": 11.56, "y": 16.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -31.06, "y": -39.1, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 11.56, "y": 16.51, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": -31.06, "y": -39.1}]}, "archer9": {"rotate": [{"angle": 5.69}]}, "archer90": {"translate": [{"x": 3665.72, "y": 866.87, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 291.61, "y": -355.87, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 3665.72, "y": 866.87, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 291.61, "y": -355.87, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 3665.72, "y": 866.87}]}, "archer80": {"rotate": [{"angle": 8.34}, {"time": 0.1, "angle": -5.24}, {"time": 0.2, "angle": -23.37}, {"time": 0.3, "angle": -6}, {"time": 0.4, "angle": -4.85}, {"time": 0.5333, "angle": 8.34}, {"time": 0.6333, "angle": -5.24}, {"time": 0.7333, "angle": -23.37}, {"time": 0.8333, "angle": -6}, {"time": 0.9333, "angle": -4.85}, {"time": 1.0667, "angle": 8.34}]}, "archer78": {"rotate": [{"angle": -30.21}, {"time": 0.1, "angle": -30.77}, {"time": 0.2, "angle": 19.3}, {"time": 0.3, "angle": 28.89}, {"time": 0.4, "angle": 4.29}, {"time": 0.5333, "angle": -30.21}, {"time": 0.6333, "angle": -30.77}, {"time": 0.7333, "angle": 19.3}, {"time": 0.8333, "angle": 28.89}, {"time": 0.9333, "angle": 4.29}, {"time": 1.0667, "angle": -30.21}]}, "archer77": {"rotate": [{"angle": -10.2}, {"time": 0.1, "angle": 4.41}, {"time": 0.2, "angle": -25.78}, {"time": 0.3, "angle": -46.27}, {"time": 0.4, "angle": -4.99}, {"time": 0.5333, "angle": -10.2}, {"time": 0.6333, "angle": 4.41}, {"time": 0.7333, "angle": -25.78}, {"time": 0.8333, "angle": -46.27}, {"time": 0.9333, "angle": -4.99}, {"time": 1.0667, "angle": -10.2}]}, "archer75": {"rotate": [{"angle": 3.01}, {"time": 0.1, "angle": -34.1}, {"time": 0.2, "angle": -36.57}, {"time": 0.3, "angle": -13.37}, {"time": 0.4, "angle": -27.1}, {"time": 0.5333, "angle": 3.01}, {"time": 0.6333, "angle": -34.1}, {"time": 0.7333, "angle": -36.57}, {"time": 0.8333, "angle": -13.37}, {"time": 0.9333, "angle": -27.1}, {"time": 1.0667, "angle": 3.01}]}, "archer73": {"rotate": [{"angle": -23.05}, {"time": 0.1, "angle": -26.22}, {"time": 0.2, "angle": 17.62}, {"time": 0.3, "angle": 9.81}, {"time": 0.4, "angle": 4}, {"time": 0.5333, "angle": -23.05}, {"time": 0.6333, "angle": -26.22}, {"time": 0.7333, "angle": 17.62}, {"time": 0.8333, "angle": 9.81}, {"time": 0.9333, "angle": 4}, {"time": 1.0667, "angle": -23.05}]}, "archer72": {"rotate": [{"angle": -13.56}, {"time": 0.1, "angle": 7.11}, {"time": 0.2, "angle": -23.17}, {"time": 0.3, "angle": -40.52}, {"time": 0.4, "angle": -2.16}, {"time": 0.5333, "angle": -13.56}, {"time": 0.6333, "angle": 7.11}, {"time": 0.7333, "angle": -23.17}, {"time": 0.8333, "angle": -40.52}, {"time": 0.9333, "angle": -2.16}, {"time": 1.0667, "angle": -13.56}]}, "archer70": {"rotate": [{"angle": 2.68}, {"time": 0.1, "angle": -0.3}, {"time": 0.2, "angle": -23.05}, {"time": 0.3, "angle": -2.08}, {"time": 0.4, "angle": -28.04}, {"time": 0.5333, "angle": 2.68}, {"time": 0.6333, "angle": -0.3}, {"time": 0.7333, "angle": -23.05}, {"time": 0.8333, "angle": -2.08}, {"time": 0.9333, "angle": -28.04}, {"time": 1.0667, "angle": 2.68}]}, "archer68": {"rotate": [{"angle": -20.07}, {"time": 0.1, "angle": -36.46}, {"time": 0.2, "angle": 17.88}, {"time": 0.3, "angle": 29.88}, {"time": 0.4, "angle": 0.42}, {"time": 0.5333, "angle": -20.07}, {"time": 0.6333, "angle": -36.46}, {"time": 0.7333, "angle": 17.88}, {"time": 0.8333, "angle": 29.88}, {"time": 0.9333, "angle": 0.42}, {"time": 1.0667, "angle": -20.07}]}, "archer67": {"rotate": [{"angle": -21.94}, {"time": 0.1, "angle": 1.87}, {"time": 0.2, "angle": -27.84}, {"time": 0.3, "angle": -41.52}, {"time": 0.4, "angle": -11.64}, {"time": 0.5333, "angle": -21.94}, {"time": 0.6333, "angle": 1.87}, {"time": 0.7333, "angle": -27.84}, {"time": 0.8333, "angle": -41.52}, {"time": 0.9333, "angle": -11.64}, {"time": 1.0667, "angle": -21.94}]}, "archer21": {"rotate": [{"angle": 5.55, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": -11.9, "curve": 0.324, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.5, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.5667, "angle": -26.87, "curve": 0.347, "c2": 0.38, "c3": 0.69, "c4": 0.74}, {"time": 0.8333, "angle": -11.13, "curve": 0.377, "c2": 0.61, "c3": 0.719}, {"time": 1.0667, "angle": 5.55}]}, "archer18": {"rotate": [{"angle": 3.64, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 0.2667, "angle": 2.67, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4667, "angle": 4.32, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5333, "angle": 3.64, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6, "angle": -46.18, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.8, "angle": -25.73, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 3.64}]}, "archer61": {"rotate": [{"angle": 0.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 2.21, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5333, "angle": 0.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 2.21, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.0667, "angle": 0.81}]}, "archer51": {"translate": [{"x": -22.79, "y": 64.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -14.61, "y": 28.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -22.79, "y": 64.22, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": -14.61, "y": 28.08, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": -22.79, "y": 64.22}]}, "archer45": {"translate": [{"x": -22.79, "y": 64.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -14.61, "y": 28.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -22.79, "y": 64.22, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": -14.61, "y": 28.08, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": -22.79, "y": 64.22}]}, "archer11": {"rotate": [{"angle": -4.45, "curve": 0.351, "c2": 0.41, "c3": 0.686, "c4": 0.75}, {"time": 0.5, "angle": 9.73, "curve": 0.337, "c2": 0.35, "c3": 0.67, "c4": 0.68}, {"time": 0.5667, "angle": -16.19, "curve": 0.363, "c2": 0.64, "c3": 0.699}, {"time": 1.0667, "angle": -4.45}]}, "archer100": {"translate": [{"x": -22.79, "y": 64.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -14.61, "y": 28.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -22.79, "y": 64.22, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": -14.61, "y": 28.08, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": -22.79, "y": 64.22}]}, "bone": {"rotate": [{"time": 0.5, "angle": 0.14}]}, "archer15": {"rotate": [{"angle": -2.85}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -103.48, "y": 22.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": -103.48, "y": 22.69, "curve": 0.25, "c3": 0.75}, {"time": 1.0667}]}, "archer24": {"rotate": [{"angle": 5.69}]}}, "deform": {"default": {"archer19": {"archer19": [{"offset": 50, "vertices": [29.29971, -7.51984, 30.08694, 3.12943, -4.22198, -29.95404, 29.29332, -7.54654, 34.29597, -21.71634, 39.70453, -8.44638, -17.77597, -36.49536, 34.27692, -21.74741, 25.09648, -40.90817, 37.7468, -29.63919, -37.8703, -29.48267, 25.06031, -40.93109, 1.86008, -51.15164, 19.51712, -47.31818, -50.63004, -7.52695, 1.81461, -51.15411, -7.30482, -58.97583, 13.64203, -57.8392, -59.42303, 0.71283, -7.35717, -58.97012, 7.9594, -34.0824, -32.98892, -11.69385, 7.92926, -34.08997, 0.37393, -15.72943, 5.81589, -14.61942, -15.59122, -2.11784, 0.36009, -15.73013, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.65785, -2.04446, 2.26492, -1.34097, -1.84824, -1.8748, 1.65612, -2.04636, -9.59277, 13.20718, 15.13028, 6.12254, -4.39181, 15.7204, -19.50644, 16.1344, 20.36295, 15.03761, -12.66525, 21.91742, -22.72079, 19.44901, 24.35477, 17.35721, -14.52385, 26.1438, -1.06073, 26.6265, 26.09552, -5.39435, 8.27985, 25.32803, 9.41519, 28.42468, 25.31342, -15.9948, 18.72619, 23.3649, 15.01956, 31.9303, 27.3634, -22.2794, 25.20063, 24.69891, 10.52824, 16.75058, 17.81543, -8.60416, 10.54322, 16.74084, 6.82907, 19.4855, 17.26202, -11.32849, 13.18834, 15.88617, 15.41138, 19.40753, 7.70767, 23.5531, 20.99744, -13.16295, 15.42916, 19.39246, 23.2359, 10.2424, 18.22931, 17.6778, 12.75748, -21.95602, 23.24511, 10.22086, 23.76955, 8.45932, 19.3493, 16.19141, 11.04501, -22.68439, 23.77737, 8.43759, 20.46471, 8.5159, 16.23059, 15.09619, 10.73398, -19.39375, 20.47247, 8.49689, 20.08981, 0.78247, 18.56607, 7.71429, 3.00705, -19.87933, 20.09064, 0.76407, 8.1993, -1.43173, 8.18588, 1.50641, -0.51334, -8.30786, 8.1981, -1.43961, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29.07297, 2.72372, 26.31503, 12.65588, 5.93338, -28.59158, 29.07557, 2.69714, 30.57253, 10.19681, 25.12466, 20.1843, 13.52679, -29.25235, 30.58175, 10.1691, 46.44272, 26.38715, 14.40375, -51.43718, 52.72552, 8.55841, 61.43883, -14.0397, 62.48911, 8.18283, -7.13422, -62.61823, 61.42662, -14.09518, 53.01184, -25.82767, 58.6828, -5.79868, -19.78439, -55.5515, 52.98892, -25.87534]}]}}}}, "run_dig": {"slots": {"archer24": {"attachment": [{"name": null}]}, "barb3": {"attachment": [{"name": "barb3"}]}, "zuojiao": {"attachment": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}]}}, "bones": {"archer22": {"rotate": [{"angle": -14.23, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -49.38, "curve": 0, "c2": 0.09, "c3": 0.36}, {"time": 0.4667, "angle": -36.98, "curve": 0.25, "c4": 0.79}, {"time": 0.5333, "angle": -14.23}]}, "archer7": {"rotate": [{"angle": -51.44, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -66.66, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -104.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -65.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -37.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -15.86, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 43.71, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -51.44}], "translate": [{"x": -136.7, "y": 123.12, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -159.02, "y": 272.64, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -96.53, "y": 254.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 108.78, "y": 149.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 189.12, "y": 185.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 352.03, "y": 152.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 410.06, "y": 36.09, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 308.41, "y": 36.43, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -136.7, "y": 123.12}]}, "archer13": {"rotate": [{"angle": 2.06}], "translate": [{"x": -35.22, "y": -48.97}]}, "archer12": {"rotate": [{"angle": 0.34, "curve": 0.337, "c2": 0.34, "c3": 0.674, "c4": 0.69}, {"time": 0.1, "angle": 19.52, "curve": 0.34, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.1667, "angle": 21.35, "curve": 0.34, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.2333, "angle": 0.22, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.4, "angle": -9.32, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.5333, "angle": 0.34}]}, "archer2": {"rotate": [{"angle": -13.34}], "translate": [{"y": 6.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "y": 33.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "y": -49.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "y": -69.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "y": 6.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "y": 33.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "y": -49.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "y": -69.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "y": 6.71}]}, "archer5": {"rotate": [{"angle": 3.31}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 93.22, "y": -20.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "archer8": {"rotate": [{"angle": -2.85}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -103.48, "y": 22.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "archer66": {"translate": [{"x": 12.62, "y": -70.53}]}, "archer6": {"rotate": [{"angle": -6.72}]}, "archer10": {"rotate": [{"angle": -73.02, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 16.06, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 32.02, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 1.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -73.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -86.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -89.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -116.25, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -73.02}], "translate": [{"x": -90.1, "y": 180.45, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 97.36, "y": 122.43, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 208.94, "y": 64.41, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -134.73, "y": 46.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -377.98, "y": 126.89, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -456.09, "y": 287.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -442.7, "y": 249.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -351.17, "y": 225.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -90.1, "y": 180.45}]}, "archer88": {"translate": [{"x": 538.5, "y": -1517.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 222.68, "y": -803.17, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 538.5, "y": -1517.3}]}, "archer20": {"rotate": [{"angle": 69.89, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 55.94, "curve": "stepped"}, {"time": 0.1667, "angle": 55.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 48.1, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.4667, "angle": 20.25, "curve": 0.25, "c4": 0.79}, {"time": 0.5333, "angle": 69.89}]}, "archer19": {"rotate": [{"angle": -52.55, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 72.33, "curve": "stepped"}, {"time": 0.1667, "angle": 72.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -18.56, "curve": 0, "c2": 0.08, "c3": 0.65, "c4": 0.74}, {"time": 0.4667, "angle": 1.99, "curve": 0.457, "c2": 0.6, "c3": 0.817, "c4": 0.98}, {"time": 0.5333, "angle": -52.55}], "translate": [{"x": 58.07, "y": 140.03, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 56.9, "y": 68.87, "curve": "stepped"}, {"time": 0.4667, "x": 56.9, "y": 68.87, "curve": 0.25, "c4": 0.79}, {"time": 0.5333, "x": 58.07, "y": 140.03}]}, "archer17": {"rotate": [{"angle": 54.33, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "angle": 69.9, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2667, "angle": 33.55, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": 20.22, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333, "angle": 54.33}]}, "archer16": {"rotate": [{"angle": 55.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -32.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 55.13}], "translate": [{"x": -31.06, "y": -39.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 11.56, "y": 16.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -31.06, "y": -39.1}]}, "archer9": {"rotate": [{"angle": 5.69}]}, "archer90": {"translate": [{"x": 3665.72, "y": 866.87, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 291.61, "y": -355.87, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 3665.72, "y": 866.87}]}, "archer87": {"rotate": [{"curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": 19.19, "curve": "stepped"}, {"time": 0.1667, "angle": 19.19, "curve": 0.324, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.2333, "angle": -16.18, "curve": "stepped"}, {"time": 0.4, "angle": -16.18, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 0.5333}]}, "archer86": {"rotate": [{"curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": 19.19, "curve": "stepped"}, {"time": 0.1667, "angle": 19.19, "curve": 0.324, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.2333, "angle": -16.18, "curve": "stepped"}, {"time": 0.4, "angle": -16.18, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 0.5333}]}, "archer80": {"rotate": [{"angle": 8.34}, {"time": 0.1, "angle": 27.53, "curve": "stepped"}, {"time": 0.1667, "angle": 27.53}, {"time": 0.2333, "angle": -7.12, "curve": "stepped"}, {"time": 0.4, "angle": -7.12}, {"time": 0.5333, "angle": 8.34}]}, "archer78": {"rotate": [{"angle": -30.21}, {"time": 0.1, "angle": -11.03, "curve": "stepped"}, {"time": 0.1667, "angle": -11.03}, {"time": 0.2333, "angle": -45.67, "curve": "stepped"}, {"time": 0.4, "angle": -45.67}, {"time": 0.5333, "angle": -30.21}]}, "archer77": {"rotate": [{"angle": -10.2}, {"time": 0.1, "angle": 8.99, "curve": "stepped"}, {"time": 0.1667, "angle": 8.99}, {"time": 0.2333, "angle": -25.65, "curve": "stepped"}, {"time": 0.4, "angle": -25.65}, {"time": 0.5333, "angle": -10.2}]}, "archer75": {"rotate": [{"angle": 3.01}, {"time": 0.1, "angle": -34.1}, {"time": 0.2, "angle": -36.57}, {"time": 0.3, "angle": -13.37}, {"time": 0.4, "angle": -27.1}, {"time": 0.5333, "angle": 3.01}]}, "archer73": {"rotate": [{"angle": -23.05}, {"time": 0.1, "angle": -26.22}, {"time": 0.2, "angle": 17.62}, {"time": 0.3, "angle": 9.81}, {"time": 0.4, "angle": 4}, {"time": 0.5333, "angle": -23.05}]}, "archer72": {"rotate": [{"angle": -13.56}, {"time": 0.1, "angle": 7.11}, {"time": 0.2, "angle": -23.17}, {"time": 0.3, "angle": -40.52}, {"time": 0.4, "angle": -2.16}, {"time": 0.5333, "angle": -13.56}]}, "archer70": {"rotate": [{"angle": 2.68}, {"time": 0.1, "angle": -0.3}, {"time": 0.2, "angle": -23.05}, {"time": 0.3, "angle": -2.08}, {"time": 0.4, "angle": -28.04}, {"time": 0.5333, "angle": 2.68}]}, "archer68": {"rotate": [{"angle": -20.07}, {"time": 0.1, "angle": -36.46}, {"time": 0.2, "angle": 17.88}, {"time": 0.3, "angle": 29.88}, {"time": 0.4, "angle": 0.42}, {"time": 0.5333, "angle": -20.07}]}, "archer67": {"rotate": [{"angle": -21.94}, {"time": 0.1, "angle": 1.87}, {"time": 0.2, "angle": -27.84}, {"time": 0.3, "angle": -41.52}, {"time": 0.4, "angle": -11.64}, {"time": 0.5333, "angle": -21.94}]}, "archer21": {"rotate": [{"angle": 5.55, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 2.67, "curve": "stepped"}, {"time": 0.1667, "angle": 2.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 0.05, "curve": 0, "c2": 0.09, "c3": 0.853, "c4": 0.99}, {"time": 0.4667, "angle": -6.89, "curve": 0.25, "c4": 0.79}, {"time": 0.5333, "angle": 5.55}]}, "archer18": {"rotate": [{"angle": 3.64, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 2.67, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 3.64}]}, "archer61": {"rotate": [{"angle": 0.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 2.21, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5333, "angle": 0.81}]}, "archer51": {"translate": [{"x": -22.79, "y": 64.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -14.61, "y": 28.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -22.79, "y": 64.22}]}, "archer45": {"translate": [{"x": -22.79, "y": 64.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -14.61, "y": 28.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -22.79, "y": 64.22}]}, "archer11": {"rotate": [{"angle": -4.45, "curve": 0.344, "c2": 0.38, "c3": 0.678, "c4": 0.71}, {"time": 0.1, "angle": 14.74, "curve": 0.34, "c2": 0.37, "c3": 0.674, "c4": 0.7}, {"time": 0.1667, "angle": 16.57, "curve": 0.34, "c2": 0.37, "c3": 0.674, "c4": 0.7}, {"time": 0.2333, "angle": -5.14, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.4, "angle": -14.69, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.5333, "angle": -4.45}]}, "archer100": {"translate": [{"x": -22.79, "y": 64.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -14.61, "y": 28.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -22.79, "y": 64.22}]}, "archer94": {"rotate": [{"time": 0.0667, "angle": -65.94, "curve": "stepped"}, {"time": 0.1667, "angle": -65.94}, {"time": 0.2667, "angle": -72.85}, {"time": 0.4667, "angle": -123.07}], "translate": [{"time": 0.4667, "x": -41.22, "y": -50.56}]}, "bone": {"rotate": [{"angle": -8.54}, {"time": 0.4667, "angle": 33.88}, {"time": 0.5333, "angle": 95.73}], "translate": [{"x": -31.62, "y": -3.05}]}, "archer15": {"rotate": [{"angle": -2.85}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -103.48, "y": 22.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "archer24": {"rotate": [{"angle": 5.69}]}}, "deform": {"default": {"archer19": {"archer19": [{"offset": 50, "vertices": [29.29971, -7.51984, 30.08694, 3.12943, -4.22198, -29.95404, 29.29332, -7.54654, 34.29597, -21.71634, 39.70453, -8.44638, -17.77597, -36.49536, 34.27692, -21.74741, 25.09648, -40.90817, 37.7468, -29.63919, -37.8703, -29.48267, 25.06031, -40.93109, 1.86008, -51.15164, 19.51712, -47.31818, -50.63004, -7.52695, 1.81461, -51.15411, -7.30482, -58.97583, 13.64203, -57.8392, -59.42303, 0.71283, -7.35717, -58.97012, 7.9594, -34.0824, -32.98892, -11.69385, 7.92926, -34.08997, 0.37393, -15.72943, 5.81589, -14.61942, -15.59122, -2.11784, 0.36009, -15.73013, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.65785, -2.04446, 2.26492, -1.34097, -1.84824, -1.8748, 1.65612, -2.04636, -9.59277, 13.20718, 15.13028, 6.12254, -4.39181, 15.7204, -19.50644, 16.1344, 20.36295, 15.03761, -12.66525, 21.91742, -22.72079, 19.44901, 24.35477, 17.35721, -14.52385, 26.1438, -1.06073, 26.6265, 26.09552, -5.39435, 8.27985, 25.32803, 9.41519, 28.42468, 25.31342, -15.9948, 18.72619, 23.3649, 15.01956, 31.9303, 27.3634, -22.2794, 25.20063, 24.69891, 10.52824, 16.75058, 17.81543, -8.60416, 10.54322, 16.74084, 6.82907, 19.4855, 17.26202, -11.32849, 13.18834, 15.88617, 15.41138, 19.40753, 7.70767, 23.5531, 20.99744, -13.16295, 15.42916, 19.39246, 23.2359, 10.2424, 18.22931, 17.6778, 12.75748, -21.95602, 23.24511, 10.22086, 23.76955, 8.45932, 19.3493, 16.19141, 11.04501, -22.68439, 23.77737, 8.43759, 20.46471, 8.5159, 16.23059, 15.09619, 10.73398, -19.39375, 20.47247, 8.49689, 20.08981, 0.78247, 18.56607, 7.71429, 3.00705, -19.87933, 20.09064, 0.76407, 8.1993, -1.43173, 8.18588, 1.50641, -0.51334, -8.30786, 8.1981, -1.43961, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29.07297, 2.72372, 26.31503, 12.65588, 5.93338, -28.59158, 29.07557, 2.69714, 30.57253, 10.19681, 25.12466, 20.1843, 13.52679, -29.25235, 30.58175, 10.1691, 46.44272, 26.38715, 14.40375, -51.43718, 52.72552, 8.55841, 61.43883, -14.0397, 62.48911, 8.18283, -7.13422, -62.61823, 61.42662, -14.09518, 53.01184, -25.82767, 58.6828, -5.79868, -19.78439, -55.5515, 52.98892, -25.87534]}]}}}, "drawOrder": [{"offsets": [{"slot": "barb3", "offset": -15}]}, {"time": 0.2667, "offsets": [{"slot": "barb3", "offset": 3}]}, {"time": 0.5333, "offsets": [{"slot": "barb3", "offset": -15}]}]}, "run_help": {"slots": {"archer15": {"attachment": [{"name": "archer16"}]}, "archer24": {"attachment": [{"name": null}]}, "zuojiao": {"attachment": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}]}}, "bones": {"archer12": {"rotate": [{"angle": 0.34}]}, "archer22": {"rotate": [{"angle": -14.23}]}, "archer7": {"rotate": [{"angle": -51.44, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -66.66, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -104.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -65.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -37.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -15.86, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 43.71, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -51.44}], "translate": [{"x": -136.7, "y": 123.12, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -159.02, "y": 272.64, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -96.53, "y": 254.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 108.78, "y": 149.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 189.12, "y": 185.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 352.03, "y": 152.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 410.06, "y": 36.09, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 308.41, "y": 36.43, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -136.7, "y": 123.12}]}, "archer13": {"rotate": [{"angle": 10.56}], "translate": [{"x": -35.22, "y": -48.97}]}, "archer2": {"rotate": [{"angle": 6.04}], "translate": [{"y": 6.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "y": 33.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "y": -49.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "y": -69.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "y": 6.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "y": 33.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "y": -49.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "y": -69.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "y": 6.71}]}, "archer5": {"rotate": [{"angle": 3.31}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 93.22, "y": -20.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "archer8": {"rotate": [{"angle": -2.85}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -103.48, "y": 22.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "archer66": {"translate": [{"x": 12.62, "y": -70.53}]}, "archer6": {"rotate": [{"angle": -6.72}]}, "archer10": {"rotate": [{"angle": -73.02, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 16.06, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 32.02, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 1.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -73.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -86.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -89.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -116.25, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -73.02}], "translate": [{"x": -90.1, "y": 180.45, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 97.36, "y": 122.43, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 208.94, "y": 64.41, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -134.73, "y": 46.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -377.98, "y": 126.89, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -456.09, "y": 287.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -442.7, "y": 249.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -351.17, "y": 225.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -90.1, "y": 180.45}]}, "archer88": {"translate": [{"x": 538.5, "y": -1517.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 222.68, "y": -803.17, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 538.5, "y": -1517.3}]}, "archer20": {"rotate": [{"angle": -1.51}]}, "archer19": {"rotate": [{"angle": 121.56}], "translate": [{"x": 6.26, "y": -13.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -4.15, "y": -50.25, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 6.26, "y": -13.49}]}, "archer17": {"rotate": [{"angle": 3.81}]}, "archer16": {"rotate": [{"angle": 58.41}], "translate": [{"x": -75.22, "y": -15.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -62.94, "y": 30.17, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -75.22, "y": -15.4}], "scale": [{"x": -1}]}, "archer9": {"rotate": [{"angle": 5.69}]}, "archer90": {"translate": [{"x": 3665.72, "y": 866.87, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 291.61, "y": -355.87, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 3665.72, "y": 866.87}]}, "archer80": {"rotate": [{"angle": 8.34}, {"time": 0.1, "angle": -5.24}, {"time": 0.2, "angle": -23.37}, {"time": 0.3, "angle": -6}, {"time": 0.4, "angle": -4.85}, {"time": 0.5333, "angle": 8.34}]}, "archer78": {"rotate": [{"angle": -30.21}, {"time": 0.1, "angle": -30.77}, {"time": 0.2, "angle": 19.3}, {"time": 0.3, "angle": 28.89}, {"time": 0.4, "angle": 4.29}, {"time": 0.5333, "angle": -30.21}]}, "archer77": {"rotate": [{"angle": -10.2}, {"time": 0.1, "angle": 4.41}, {"time": 0.2, "angle": -25.78}, {"time": 0.3, "angle": -46.27}, {"time": 0.4, "angle": -4.99}, {"time": 0.5333, "angle": -10.2}]}, "archer75": {"rotate": [{"angle": 3.01}, {"time": 0.1, "angle": -34.1}, {"time": 0.2, "angle": -36.57}, {"time": 0.3, "angle": -13.37}, {"time": 0.4, "angle": -27.1}, {"time": 0.5333, "angle": 3.01}]}, "archer73": {"rotate": [{"angle": -23.05}, {"time": 0.1, "angle": -26.22}, {"time": 0.2, "angle": 17.62}, {"time": 0.3, "angle": 9.81}, {"time": 0.4, "angle": 4}, {"time": 0.5333, "angle": -23.05}]}, "archer72": {"rotate": [{"angle": -13.56}, {"time": 0.1, "angle": 7.11}, {"time": 0.2, "angle": -23.17}, {"time": 0.3, "angle": -40.52}, {"time": 0.4, "angle": -2.16}, {"time": 0.5333, "angle": -13.56}]}, "archer70": {"rotate": [{"angle": 2.68}, {"time": 0.1, "angle": -0.3}, {"time": 0.2, "angle": -23.05}, {"time": 0.3, "angle": -2.08}, {"time": 0.4, "angle": -28.04}, {"time": 0.5333, "angle": 2.68}]}, "archer68": {"rotate": [{"angle": -20.07}, {"time": 0.1, "angle": -36.46}, {"time": 0.2, "angle": 17.88}, {"time": 0.3, "angle": 29.88}, {"time": 0.4, "angle": 0.42}, {"time": 0.5333, "angle": -20.07}]}, "archer67": {"rotate": [{"angle": -21.94}, {"time": 0.1, "angle": 1.87}, {"time": 0.2, "angle": -27.84}, {"time": 0.3, "angle": -41.52}, {"time": 0.4, "angle": -11.64}, {"time": 0.5333, "angle": -21.94}]}, "archer21": {"rotate": [{"angle": 0.05}]}, "archer18": {"rotate": [{"angle": -0.45}]}, "archer61": {"rotate": [{"angle": 0.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 2.21, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5333, "angle": 0.81}]}, "archer51": {"translate": [{"x": -22.79, "y": 64.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -14.61, "y": 28.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -22.79, "y": 64.22}]}, "archer45": {"translate": [{"x": -22.79, "y": 64.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -14.61, "y": 28.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -22.79, "y": 64.22}]}, "archer11": {"rotate": [{"angle": -4.45}]}, "archer100": {"translate": [{"x": -22.79, "y": 64.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -14.61, "y": 28.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -22.79, "y": 64.22}]}, "ren": {"translate": [{"x": -39.81, "y": 1574.55}]}, "archer15": {"rotate": [{"angle": -2.85}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -103.48, "y": 22.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "archer24": {"rotate": [{"angle": 5.69}]}}, "deform": {"default": {"archer19": {"archer19": [{"offset": 50, "vertices": [29.29971, -7.51984, 30.08694, 3.12943, -4.22198, -29.95404, 29.29332, -7.54654, 34.29597, -21.71634, 39.70453, -8.44638, -17.77597, -36.49536, 34.27692, -21.74741, 25.09648, -40.90817, 37.7468, -29.63919, -37.8703, -29.48267, 25.06031, -40.93109, 1.86008, -51.15164, 19.51712, -47.31818, -50.63004, -7.52695, 1.81461, -51.15411, -7.30482, -58.97583, 13.64203, -57.8392, -59.42303, 0.71283, -7.35717, -58.97012, 7.9594, -34.0824, -32.98892, -11.69385, 7.92926, -34.08997, 0.37393, -15.72943, 5.81589, -14.61942, -15.59122, -2.11784, 0.36009, -15.73013, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.65785, -2.04446, 2.26492, -1.34097, -1.84824, -1.8748, 1.65612, -2.04636, -9.59277, 13.20718, 15.13028, 6.12254, -4.39181, 15.7204, -19.50644, 16.1344, 20.36295, 15.03761, -12.66525, 21.91742, -22.72079, 19.44901, 24.35477, 17.35721, -14.52385, 26.1438, -1.06073, 26.6265, 26.09552, -5.39435, 8.27985, 25.32803, 9.41519, 28.42468, 25.31342, -15.9948, 18.72619, 23.3649, 15.01956, 31.9303, 27.3634, -22.2794, 25.20063, 24.69891, 10.52824, 16.75058, 17.81543, -8.60416, 10.54322, 16.74084, 6.82907, 19.4855, 17.26202, -11.32849, 13.18834, 15.88617, 15.41138, 19.40753, 7.70767, 23.5531, 20.99744, -13.16295, 15.42916, 19.39246, 23.2359, 10.2424, 18.22931, 17.6778, 12.75748, -21.95602, 23.24511, 10.22086, 23.76955, 8.45932, 19.3493, 16.19141, 11.04501, -22.68439, 23.77737, 8.43759, 20.46471, 8.5159, 16.23059, 15.09619, 10.73398, -19.39375, 20.47247, 8.49689, 20.08981, 0.78247, 18.56607, 7.71429, 3.00705, -19.87933, 20.09064, 0.76407, 8.1993, -1.43173, 8.18588, 1.50641, -0.51334, -8.30786, 8.1981, -1.43961, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29.07297, 2.72372, 26.31503, 12.65588, 5.93338, -28.59158, 29.07557, 2.69714, 30.57253, 10.19681, 25.12466, 20.1843, 13.52679, -29.25235, 30.58175, 10.1691, 46.44272, 26.38715, 14.40375, -51.43718, 52.72552, 8.55841, 61.43883, -14.0397, 62.48911, 8.18283, -7.13422, -62.61823, 61.42662, -14.09518, 53.01184, -25.82767, 58.6828, -5.79868, -19.78439, -55.5515, 52.98892, -25.87534]}]}}}, "drawOrder": [{"offsets": [{"slot": "archer17", "offset": 13}, {"slot": "archer16", "offset": 13}]}]}, "walk": {"slots": {"archer5": {"attachment": [{"time": 0.0667, "name": "archer5_1"}, {"time": 0.1333, "name": "archer5_2"}, {"time": 0.2, "name": "archer5_3"}, {"time": 0.3333, "name": "archer5_2"}, {"time": 0.4, "name": "archer5_1"}, {"time": 0.4667, "name": "archer5"}]}, "archer8": {"attachment": [{"time": 0.0667, "name": "archer8_1"}, {"time": 0.1333, "name": "archer8_2"}, {"time": 0.2, "name": "archer8_3"}, {"time": 0.3333, "name": "archer8_2"}, {"time": 0.4, "name": "archer8_1"}, {"time": 0.4667, "name": "archer8"}]}, "archer24": {"attachment": [{"name": null}]}, "zuojiao": {"attachment": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}]}}, "bones": {"archer7": {"rotate": [{"angle": -20.32, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -35.96, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 47.96, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 2.61, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -9.61, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -20.32}], "translate": [{"x": -5.59, "y": 25.15, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 109, "y": 26.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 306.03, "y": 55.9, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 361.4, "y": 4.19, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 186.11, "y": 4.19, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 43.24, "y": 22.86, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -5.59, "y": 25.15}]}, "archer64": {"translate": [{"curve": "stepped"}, {"time": 0.0667, "x": -7.34, "y": 0.82, "curve": "stepped"}, {"time": 0.1333, "x": -18.51, "y": 2.07, "curve": "stepped"}, {"time": 0.2, "x": -25.88, "y": 2.9, "curve": "stepped"}, {"time": 0.2667, "x": -25.88, "y": 2.9, "curve": "stepped"}, {"time": 0.3333, "x": -18.53, "y": 2.08, "curve": "stepped"}, {"time": 0.4, "x": -7.37, "y": 0.82, "curve": "stepped"}, {"time": 0.4667}]}, "archer65": {"translate": [{"curve": "stepped"}, {"time": 0.0667, "x": -7.34, "y": 0.82, "curve": "stepped"}, {"time": 0.1333, "x": -18.51, "y": 2.07, "curve": "stepped"}, {"time": 0.2, "x": -25.88, "y": 2.9, "curve": "stepped"}, {"time": 0.2667, "x": -25.88, "y": 2.9, "curve": "stepped"}, {"time": 0.3333, "x": -18.53, "y": 2.08, "curve": "stepped"}, {"time": 0.4, "x": -7.37, "y": 0.82, "curve": "stepped"}, {"time": 0.4667}]}, "archer13": {"rotate": [{"angle": -0.83, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -3.42, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -0.83}]}, "archer12": {"rotate": [{"angle": 0.34}]}, "archer2": {"translate": [{"y": 3.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "y": -11.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "y": 17.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "y": 3.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "y": -11.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "y": 17.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "y": 3.56}]}, "archer5": {"rotate": [{"angle": 3.31}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 46.39, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "archer8": {"rotate": [{"angle": -2.85}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -115.96, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "archer20": {"rotate": [{"angle": 28.98, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.51, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 20.96, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "angle": 28.98}]}, "archer10": {"rotate": [{"angle": 15.05, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -0.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.57, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -29.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -72.98, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -6.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 15.05}], "translate": [{"y": 9.78, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -190.05, "y": -4.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -329.78, "y": 6.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -392.67, "y": 43.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -260.08, "y": 106.24, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -145.08, "y": 70.06, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 9.78}]}, "archer16": {"rotate": [{"angle": 22.28, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.6, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 22.28}], "translate": [{"x": -2.57, "y": -10.63, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -1.23, "y": -5.1, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -2.57, "y": -10.63}]}, "archer46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "archer88": {"translate": [{"x": -304.26, "y": -1185.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 175.6, "y": -1769.97, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -304.26, "y": -1185.1}]}, "archer17": {"rotate": [{"angle": 16.15, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": 41.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": 3.81, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": -3.81, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "angle": 16.15}]}, "archer19": {"rotate": [{"angle": -55.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.28, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -55.69}], "translate": [{"x": 18.36, "y": 71.94, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 1.04, "y": 4.08, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 18.36, "y": 71.94}]}, "archer9": {"rotate": [{"angle": 5.69}]}, "archer6": {"rotate": [{"angle": -6.72}]}, "archer11": {"rotate": [{"angle": 0.09}]}, "archer90": {"translate": [{"x": 382.93, "y": 112.63, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 1862.83, "y": -83.34, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 382.93, "y": 112.63}]}, "archer80": {"rotate": [{"angle": -9.62, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.4667, "angle": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -9.9, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1, "angle": -9.62}]}, "archer78": {"rotate": [{"angle": -1.67, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "angle": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -7.03, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1, "angle": -1.67}]}, "archer77": {"rotate": [{"angle": 4.77}], "translate": [{"x": 18.5, "y": 72.47, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 18.5, "y": 72.47}]}, "archer75": {"rotate": [{"angle": -7.47, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4, "angle": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -8.74, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1, "angle": -7.47}]}, "archer73": {"rotate": [{"angle": 1.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1667, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -3.13, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1, "angle": 1.49}]}, "archer72": {"rotate": [{"angle": -2.01}], "translate": [{"x": 18.5, "y": 72.47, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 18.5, "y": 72.47}]}, "archer70": {"rotate": [{"angle": -11.41, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -13.11, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1, "angle": -11.41}]}, "archer68": {"rotate": [{"angle": 0.31, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1333, "angle": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -3.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1, "angle": 0.31}]}, "archer67": {"rotate": [{"angle": 4.1}], "translate": [{"x": -9.68, "y": -37.93, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -9.68, "y": -37.93}]}, "archer21": {"rotate": [{"angle": 0.05}]}, "archer18": {"rotate": [{"angle": -0.45}]}, "archer61": {"rotate": [{"angle": 0.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 2.21, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 0.81}]}, "archer52": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.05, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "archer101": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "archer98": {"rotate": [{"angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 11.65, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1, "angle": 3.55}]}, "archer15": {"rotate": [{"angle": -2.85}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -115.96, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "archer24": {"rotate": [{"angle": 5.69}]}}, "deform": {"default": {"archer19": {"archer19": [{"offset": 50, "vertices": [29.29971, -7.51984, 30.08694, 3.12943, -4.22198, -29.95404, 29.29332, -7.54654, 34.29597, -21.71634, 39.70453, -8.44638, -17.77597, -36.49536, 34.27692, -21.74741, 25.09648, -40.90817, 37.7468, -29.63919, -37.8703, -29.48267, 25.06031, -40.93109, 1.86008, -51.15164, 19.51712, -47.31818, -50.63004, -7.52695, 1.81461, -51.15411, -7.30482, -58.97583, 13.64203, -57.8392, -59.42303, 0.71283, -7.35717, -58.97012, 7.9594, -34.0824, -32.98892, -11.69385, 7.92926, -34.08997, 0.37393, -15.72943, 5.81589, -14.61942, -15.59122, -2.11784, 0.36009, -15.73013, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.65785, -2.04446, 2.26492, -1.34097, -1.84824, -1.8748, 1.65612, -2.04636, -9.59277, 13.20718, 15.13028, 6.12254, -4.39181, 15.7204, -19.50644, 16.1344, 20.36295, 15.03761, -12.66525, 21.91742, -22.72079, 19.44901, 24.35477, 17.35721, -14.52385, 26.1438, -1.06073, 26.6265, 26.09552, -5.39435, 8.27985, 25.32803, 9.41519, 28.42468, 25.31342, -15.9948, 18.72619, 23.3649, 15.01956, 31.9303, 27.3634, -22.2794, 25.20063, 24.69891, 10.52824, 16.75058, 17.81543, -8.60416, 10.54322, 16.74084, 6.82907, 19.4855, 17.26202, -11.32849, 13.18834, 15.88617, 15.41138, 19.40753, 7.70767, 23.5531, 20.99744, -13.16295, 15.42916, 19.39246, 23.2359, 10.2424, 18.22931, 17.6778, 12.75748, -21.95602, 23.24511, 10.22086, 23.76955, 8.45932, 19.3493, 16.19141, 11.04501, -22.68439, 23.77737, 8.43759, 20.46471, 8.5159, 16.23059, 15.09619, 10.73398, -19.39375, 20.47247, 8.49689, 20.08981, 0.78247, 18.56607, 7.71429, 3.00705, -19.87933, 20.09064, 0.76407, 8.1993, -1.43173, 8.18588, 1.50641, -0.51334, -8.30786, 8.1981, -1.43961, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29.07297, 2.72372, 26.31503, 12.65588, 5.93338, -28.59158, 29.07557, 2.69714, 30.57253, 10.19681, 25.12466, 20.1843, 13.52679, -29.25235, 30.58175, 10.1691, 46.44272, 26.38715, 14.40375, -51.43718, 52.72552, 8.55841, 61.43883, -14.0397, 62.48911, 8.18283, -7.13422, -62.61823, 61.42662, -14.09518, 53.01184, -25.82767, 58.6828, -5.79868, -19.78439, -55.5515, 52.98892, -25.87534]}]}}}}, "walk_bei": {"slots": {"archer1": {"attachment": [{"name": null}]}, "archer2": {"attachment": [{"name": null}]}, "archer4": {"attachment": [{"name": null}]}, "archer5": {"attachment": [{"name": null}]}, "archer6": {"attachment": [{"name": null}]}, "archer7": {"attachment": [{"name": null}]}, "archer8": {"attachment": [{"name": null}]}, "archer9": {"attachment": [{"name": null}]}, "archer10": {"attachment": [{"name": null}]}, "archer11": {"attachment": [{"name": null}]}, "archer12": {"attachment": [{"name": "archer12_1"}]}, "archer13": {"attachment": [{"name": null}]}, "archer15": {"attachment": [{"name": "archer16"}]}, "archer19": {"attachment": [{"name": null}]}, "archer23": {"attachment": [{"name": "archer23_1"}]}, "archer25": {"attachment": [{"name": "archer20"}]}}, "bones": {"archer29": {"translate": [{"x": -5.84, "y": -209.39}], "scale": [{"y": 0.644}]}, "archer31": {"translate": [{"x": -10.12, "y": -134.6}], "scale": [{"y": 0.71}]}, "archer30": {"rotate": [{"angle": -12.38}], "translate": [{"x": 10.1, "y": -64.62}], "scale": [{"y": 0.12}]}, "archer32": {"translate": [{"x": 20.12, "y": -152.34}], "scale": [{"y": 0.569}]}, "archer13": {"rotate": [{"angle": -0.83, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -3.42, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -0.83}], "translate": [{"x": -47.5, "y": -103.15}]}, "archer60": {"translate": [{"x": -12.61, "y": 106.3}]}, "archer12": {"rotate": [{"angle": -1.6}]}, "archer5": {"rotate": [{"angle": 3.31}], "translate": [{"x": 149.64, "y": -2.15}]}, "archer15": {"rotate": [{"angle": -2.85}], "translate": [{"x": -346.75, "y": -6.59, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -263.24, "y": 1.53}, {"time": 0.3333, "x": -219.21, "y": 18.76}, {"time": 0.5, "x": -79.46, "y": -13.78}, {"time": 0.6667, "x": -152.45, "y": -11.27}, {"time": 0.8333, "x": -220.4, "y": 6.35}, {"time": 1, "x": -346.75, "y": -6.59}]}, "archer72": {"rotate": [{"angle": -2.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -10.14, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.01}]}, "archer55": {"translate": [{"x": -54.22, "y": -290.71}]}, "archer22": {"scale": [{"x": 1.128, "y": 1.283}]}, "archer8": {"rotate": [{"angle": -2.85}], "translate": [{"x": -147.73, "y": -13.44}]}, "archer17": {"rotate": [{"angle": 16.15, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "angle": 41.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5, "angle": 3.81, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": -3.81, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "angle": 16.15}]}, "archer19": {"rotate": [{"angle": -41.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": -28.15}, {"time": 1, "angle": -41.8}], "translate": [{"x": 116.96, "y": 296.98}]}, "archer16": {"rotate": [{"angle": 51.83, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 31.94, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 51.83}], "translate": [{"x": -135.12, "y": -335.73}]}, "archer67": {"rotate": [{"angle": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -4.03, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.1}]}, "archer7": {"rotate": [{"angle": 9.48}], "translate": [{"x": 337.55, "y": 7.98}]}, "archer39": {"translate": [{"x": 3.48, "y": -142.56}], "scale": [{"y": 0.667}]}, "archer20": {"rotate": [{"angle": 17.91, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 35.09}, {"time": 1, "angle": 17.91}]}, "archer11": {"rotate": [{"angle": -3.58}]}, "archer21": {"rotate": [{"angle": 0.05, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 17.22}, {"time": 1, "angle": 0.05}]}, "archer78": {"rotate": [{"angle": -1.67, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "angle": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -7.03, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1, "angle": -1.67}]}, "archer51": {"translate": [{"x": -26.37, "y": 35.39}], "scale": [{"y": 0.753}]}, "archer2": {"translate": [{"y": 3.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "y": -11.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "y": 17.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "y": 3.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "y": -11.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "y": 17.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "y": 3.56}]}, "archer10": {"rotate": [{"angle": 15.05, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -0.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.57, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -29.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -72.98, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -6.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 15.05}], "translate": [{"y": 9.78, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -190.05, "y": -4.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -329.78, "y": 6.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -392.67, "y": 43.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -260.08, "y": 106.24, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -145.08, "y": 70.06, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 9.78}]}, "archer77": {"rotate": [{"angle": 4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -3.36, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.77}]}, "archer6": {"rotate": [{"angle": -6.72}]}, "archer70": {"rotate": [{"angle": -11.41, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -13.11, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1, "angle": -11.41}]}, "archer75": {"rotate": [{"angle": -7.47, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4, "angle": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -8.74, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1, "angle": -7.47}]}, "archer73": {"rotate": [{"angle": 1.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1667, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -3.13, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1, "angle": 1.49}]}, "archer45": {"translate": [{"x": 1.79, "y": -34.83}]}, "archer9": {"rotate": [{"angle": 5.69}]}, "archer68": {"rotate": [{"angle": 0.31, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1333, "angle": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -3.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1, "angle": 0.31}]}, "archer18": {"rotate": [{"angle": -0.45}]}, "archer80": {"rotate": [{"angle": -9.62, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.4667, "angle": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -9.9, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1, "angle": -9.62}]}, "archer100": {"translate": [{"x": 1.79, "y": -34.83}]}, "archer24": {"rotate": [{"angle": 5.69}]}, "archer3": {"rotate": [{"angle": -22.15}, {"time": 0.1667, "angle": -60.73}, {"time": 0.3333, "angle": 9.46}, {"time": 0.5, "angle": 13.75}, {"time": 0.6667, "angle": -0.97}, {"time": 0.8333, "angle": -43.45}, {"time": 1, "angle": -22.15}], "translate": [{"x": -442.91, "y": 31.6}, {"time": 0.1667, "x": -338.18, "y": 100.21}, {"time": 0.3333, "x": -100.13, "y": 37.86}, {"time": 0.5, "x": 18.56, "y": -11.91}, {"time": 0.6667, "x": -127.42, "y": -11.91}, {"time": 0.8333, "x": -303.6, "y": 45.97}, {"time": 1, "x": -442.91, "y": 31.6}]}}, "deform": {"default": {"archer10": {"archer10": [{"vertices": [-21.24329, -257.10358, -21.23761, -257.10565, -21.24329, -257.10358, -21.24756, -257.10803, -21.23761, -257.10565, -21.24329, -257.10358]}]}}}, "drawOrder": [{"offsets": [{"slot": "archer<PERSON>", "offset": 22}]}]}, "work": {"slots": {"archer5": {"attachment": [{"name": "archer5_3"}]}, "archer8": {"attachment": [{"name": "archer8_3"}]}, "archer24": {"attachment": [{"name": null}]}}, "bones": {"archer13": {"rotate": [{"angle": 3.57, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "angle": 3.33, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 4.06, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5333, "angle": 3.57}], "translate": [{"x": -26.97, "y": -27.4}]}, "archer12": {"rotate": [{"angle": 3.58}]}, "archer17": {"rotate": [{"angle": 16.36, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -2.63, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 16.36}]}, "archer16": {"rotate": [{"angle": 76.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 90.96, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 76.44}]}, "archer2": {"rotate": [{"angle": -1.96, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "angle": -0.33, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -2.33, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5333, "angle": -1.96}], "translate": [{"x": -17.77, "y": -31.38, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "x": -22.61, "y": -24.72, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "x": -8.2, "y": -44.54, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5333, "x": -17.77, "y": -31.38}]}, "archer5": {"rotate": [{"angle": 3.39}]}, "archer8": {"rotate": [{"angle": -2.93}]}, "archer20": {"rotate": [{"angle": 76.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 106.91, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 76.61}]}, "archer19": {"rotate": [{"angle": -19.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -42.63, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -19.06}]}, "archer66": {"rotate": [{"angle": 5.75}], "translate": [{"x": -1.77, "y": -55.3}]}, "archer88": {"translate": [{"x": -158.28, "y": -283.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -316.56, "y": -567.77, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "x": -158.28, "y": -283.88}]}, "archer90": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1396.59, "y": -109.38, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "archer80": {"rotate": [{"angle": -9.62, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.2667, "angle": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -9.62}]}, "archer78": {"rotate": [{"angle": -1.67, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -7.03, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5333, "angle": -1.67}]}, "archer77": {"rotate": [{"angle": 4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 4.77}]}, "archer75": {"rotate": [{"angle": -7.47, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2, "angle": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -8.74, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5333, "angle": -7.47}]}, "archer73": {"rotate": [{"angle": 1.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -3.13, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5333, "angle": 1.49}]}, "archer72": {"rotate": [{"angle": -2.01, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -4.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -2.01}]}, "archer70": {"rotate": [{"angle": -11.41, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -13.11, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5333, "angle": -11.41}]}, "archer68": {"rotate": [{"angle": 0.31, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -3.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": 0.31}]}, "archer67": {"rotate": [{"angle": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 4.1}]}, "archer21": {"rotate": [{"angle": 0.05}]}, "archer18": {"rotate": [{"angle": -0.45}]}, "archer61": {"rotate": [{"angle": 0.81}]}, "archer52": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -2.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "archer46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "archer11": {"rotate": [{"angle": -17.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -15.3, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -17.22}]}, "archer9": {"rotate": [{"angle": 5.69}]}, "archer6": {"rotate": [{"angle": -6.72}]}, "archer101": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "archer98": {"rotate": [{"angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 11.65, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5333, "angle": 3.55}]}, "archer15": {"rotate": [{"angle": -2.93}]}, "archer24": {"rotate": [{"angle": 5.69}]}}, "deform": {"default": {"archer19": {"archer19": [{"offset": 96, "vertices": [-0.06015, 24.1981, -5.73996, 23.50743, 24.19751, -0.10047, -16.20033, 17.97475, -6.89499, 34.05992, -14.69929, 31.48874, 34.10422, 6.66883, -27.87293, 20.75323, -9.97385, 53.27348, -22.20314, 49.44229, 53.33798, 9.62006, -42.99277, 33.00206, -9.97385, 53.27348, -22.20314, 49.44229, 53.33798, 9.62006, -42.99277, 33.00206, -9.97385, 53.27348, -22.20314, 49.44229, 53.33798, 9.62006, -42.99277, 33.00206, -7.39728, 74.50285, -24.68294, 70.68332, 74.54886, 6.90234, -55.2479, 50.52725, -23.7817, 75.54781, 79.05298, 4.85439, -56.7612, 55.23763, -19.96531, 66.48029, 69.33273, 3.33813, -49.10147, 49.06393, -13.63827, 44.63902, 46.61002, 2.46682, -33.17433, 32.83437, -11.44029, 40.20641, 41.77835, 1.40305, -29.13733, 29.97406, -2.90604, 26.93626, 26.84128, -3.67787, -15.33107, 22.3373, 0.35873, 15.72589, 15.17456, -4.14187, -7.14098, 14.01552]}]}}}}}}