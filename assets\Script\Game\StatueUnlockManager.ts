// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import Unit from "../GameStuffAndComps/Unit";
import GameManager from "../GameManager";
import GameUtils from "./GameUtils";
import LocalUtils, { TweenObject } from "../LocalUtils";
import GameDirector from "./GameDirector";
import GameStuffManager from "./GameStuffManager";
import UnlockProgressUI from "./UnlockProgressUI";


export default class StatueUnlockManager {

    public static get instance(): StatueUnlockManager {
        if(!this._instance) {
            this._instance = new StatueUnlockManager();
        }
        return this._instance;
    }
    private static _instance: StatueUnlockManager = null;

    statueUnlockProgressUIList: UnlockProgressUI[] = [];

    statueUnlockNeedConfig: number[][] = [
        [10, 15, 25, 35, 45],
        [10, 20, 25],
        [20, 25, 30, 35, 40, 45, 50, 55, 60],
        [40, 50, 60, 70],
    ];

    statueUnlockCurConfigIndex: number[] = [0, 0];
    statueUnlockProgressList: number[] = [0, 0];
    statueUnlockMaxProgressList: number[] = [25, 50];


    floorUnlockAreaUINodeList: cc.Node[] = [];

    isFloorUnlockAreaUnlock = [false, false, false, false, false, false, false, false, false, false, false, false];
    floorUnlockAreaProgressList: number[] =    [0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0];
    floorUnlockAreaMaxProgressList: number[] = [10,  5,  5, 10, 20, 30, 10, 20, 30,  15, 30, 15, 30, 15, 30, 15, 30, 25, 80, 50, 60, 60, 80,  10, 10, 10, 10];

    floorUnlockCurConfigLevel: number[] = [0, 0, 0, 0];

    isAllStatuesUnlockCalled = false;
    isAllFloorUnlockAreaUnlockCalled = false;

    protected updateCallbackId = -1;

    // constructor() {
    // }

    GetUnlockListIndexFromStatueRef(statueRef: number): number {
        let index = -1;
        if(statueRef == 1) {
        } else if(statueRef == 201 || statueRef == 202) {
            index = statueRef - 201;
        }
        return index;
    }

    StatueUnlock(statueRef: number) {
        let index = this.GetUnlockListIndexFromStatueRef(statueRef);
        console.log(`#### 解锁雕像 ${statueRef} !!!`);
        if(statueRef == 1) {
            // LocalUtils.PlaySound('level_up');
            // GameUtils.rootHeroControl.SetPos(cc.v2(120, -800));
            // GameUtils.rootGameWorld.whiteLight.opacity = 180;
            // GameUtils.rootHeroControl.isCanHeroMove = false;
            // GameUtils.rootGameWorldUI.PlayTP(cc.v2(120, -800));
            // // cc.tween(new TweenObject<number>(-100, (value: number)=>{
            // //     GameUtils.mainHero.script.attribute.moveSpeedUp = value;
            // // })).delay(0.8).call(()=>{
            // //     GameUtils.rootHeroControl.isCanHeroMove = true;
            // // }).to(0.2, {value: 0}).start();
            // GameManager.instance.LateTimeCallOnce(()=>{
            //     GameUtils.rootHeroControl.isCanHeroMove = true;
            //     GameUtils.mainHero.script.attribute.AddASlowingEffect(-70, 0.2);
            // }, 0.8);
            // GameUtils.rootEnemyCreator.StartCreate();
            // GameDirector.instance.OnHeroTPIn();
            // return;

            // GameUtils.rootEnemyCreator.StartCreate();
            return;
        } else if(statueRef == 2) {
        }
        this.statueUnlockProgressList[index] = this.statueUnlockMaxProgressList[index];
        let statue = GameUtils.rootGameWorld.statues[index + 1];
        let ui = this.statueUnlockProgressUIList[index + 1];
        // statue.ReadyToDestroy();

        // if(!this._isAllowBoss) {
        //     this._isAllowBoss = true;
        //     GameUtils.rootEnemyCreator.AllowCreateBoss();
        // }
        let generatePos = statue.rootPosition.clone().add(cc.v2(0, -80));
        // if(ui) {
        //     GameManager.instance.LateTimeCallOnce(()=>{
        //         ui.node.active = false;
        //     }, 1.5);
        // }
        this.statueUnlockCurConfigIndex[index] += 1;
        let curConfigIndex = this.statueUnlockCurConfigIndex[index];
        statue.OnUnlock();
        GameManager.instance.LateFrameCall(()=>{
            GameManager.instance.LateTimeCallOnce(()=>{
                if(index == 1) {
                    if(!GameDirector.instance.isGameOver) {
                        GameUtils.OpenHeroChoosePanel(generatePos);
                    }
                } else if(index == 0) {
                    GameUtils.rootNPCCreator.CreateANPCLogger(generatePos);
                } else {
                }
                
                GameManager.instance.LateTimeCallOnce(()=>{
                    if(statueRef == 201) {
                        GameUtils.rootGuideToStatue.isStatueFirstUnlocked1 = true;
                        GameUtils.rootGuideToStatue.statue1UnlockTimes += 1;
                    } else if(statueRef == 202) {
                        GameUtils.rootGuideToStatue.isStatueFirstUnlocked2 = true;
                    }
                }, 0.2);
            }, 0.5);
            if((index == 1 && curConfigIndex <= 3) || (index == 0)) {
                GameManager.instance.LateTimeCallOnce(()=>{
                    let maxProgress = 100;
                    if(curConfigIndex > 8) {
                        maxProgress = this.statueUnlockNeedConfig[index][8] + (curConfigIndex - 8) * 5;
                    } else {
                        maxProgress = this.statueUnlockNeedConfig[index][curConfigIndex];
                    }
                    let ui = this.statueUnlockProgressUIList[index + 1];
                    if(ui) {
                        ui.SetProgressMax(maxProgress);
                    }
                    this.statueUnlockMaxProgressList[index] = maxProgress;
                    this.statueUnlockProgressList[index] = 0;
                    statue.unlockMaxProgress = maxProgress;
                    statue.unlockProgress = 0;
                    statue.isUnlockFinished = false;
                    this.RefreshStatueUnlockUI();
                }, (index == 0) ? 0.6 : 1.8);
            } else if(index < 0) {
            } else {
                // statue.isUnlockFinished = true;
                GameManager.instance.LateTimeCallOnce(()=>{
                    this.HideStatueUnlockUI(index);
                    GameUtils.rootGameStuffs.newUnlockNodes_6.find(e => e.name == 'building_board_3').active = false;
                }, 0.6);
            }
        });
        // let statues = GameUtils.rootGameWorld.statues;
        // if(!this.isAllStatuesUnlockCalled && statues[1].isUnlockFinished && statues[2].isUnlockFinished && statues[3].isUnlockFinished && statues[4].isUnlockFinished) {
        //     console.log(`#### 全部雕像解锁！`);
        //     this.isAllStatuesUnlockCalled = true;
        //     GameDirector.instance.OnAllStatuesUnlock();
        //     if(this.isAllFloorUnlockAreaUnlockCalled) {
        //         GameDirector.instance.OnAllStatuesAndFloorUnlockAreaUnlock();
        //     }
        // }
    }

    AddStatueUnlockProgress(statueRef: number, value: number) {
        let index = this.GetUnlockListIndexFromStatueRef(statueRef);
        this.statueUnlockProgressList[index] += value;
        this.RefreshStatueUnlockUI();
        if(this.statueUnlockProgressList[index] >= this.statueUnlockMaxProgressList[index]) {
            this.StatueUnlock(statueRef);
        }
    }

    AddTowerUnlockProgress(tower_id: number, value: number) {
        let index = tower_id;
        this.statueUnlockProgressList[index] += value;
        this.RefreshStatueUnlockUI();
        if(this.statueUnlockProgressList[index] >= this.statueUnlockMaxProgressList[index]) {
            this.StatueUnlock(index + 2);
        }
    }

    InitStatueUnlockUI() {
        this.statueUnlockMaxProgressList.forEach((e, index)=>{
            let script = this.statueUnlockProgressUIList[index + 1];
            if(script) {
                script.SetProgressMax(e);
            }
            let statue = GameUtils.rootGameWorld.statues[index + 1];
            if(statue) {
                statue.unlockMaxProgress = e;
            }
        });
    }

    RefreshStatueUnlockUI() {
        this.statueUnlockProgressList.forEach((e, index)=>{
            let script = this.statueUnlockProgressUIList[index + 1];
            if(script) {
                script.SetProgress(e);
            }
        });
    }

    HideStatueUnlockUI(index: number) {
        let script = this.statueUnlockProgressUIList[index + 1];
        if(script) {
            script.HideSelf();
        }
    }

    FloorUnlockAreaUnlock(floorUnlockAreaRef: number) {
        let index = floorUnlockAreaRef - 1;
        let floorUnlockAreaList = GameUtils.rootGameWorld.floorUnlockAreaList;
        
        console.log(`#### 解锁地面 UI ${floorUnlockAreaRef} !!!`);
        LocalUtils.PlaySoundDelay(0.1, 'bling');

        if(floorUnlockAreaRef == 1) { // 解锁小助理
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.rootNPCCreator.CreateANPCWaiter();
            }, 0.5);
        } else if(floorUnlockAreaRef >= 2 && floorUnlockAreaRef <= 9) { // 解锁桌子
            GameManager.instance.LateTimeCallOnce(()=>{
                GameStuffManager.instance.UnlockTable(floorUnlockAreaRef - 1 - 1 + 2);
                // GameDirector.instance.TrySetGameStateTrue('first_digger_create');
            }, 0.5);
        } else if(floorUnlockAreaRef >= 10 && floorUnlockAreaRef <= 17) { // 解锁床铺
            GameManager.instance.LateTimeCallOnce(()=>{
                let unlockIndexs: number[] = [];
                if(floorUnlockAreaRef == 10) {
                    unlockIndexs = [0];
                } else if(floorUnlockAreaRef == 11) {
                    unlockIndexs = [1, 2];
                } else if(floorUnlockAreaRef == 12) {
                    unlockIndexs = [3];
                } else if(floorUnlockAreaRef == 13) {
                    unlockIndexs = [4, 5];
                } else if(floorUnlockAreaRef == 14) {
                    unlockIndexs = [6];
                } else if(floorUnlockAreaRef == 15) {
                    unlockIndexs = [7, 8];
                } else if(floorUnlockAreaRef == 16) {
                    unlockIndexs = [9];
                } else if(floorUnlockAreaRef == 17) {
                    unlockIndexs = [10, 11];
                }
                unlockIndexs.forEach((e)=>{
                    GameStuffManager.instance.UnlockBed(e);
                });
            }, 0.5);
        } else if(floorUnlockAreaRef == 18) { // 解锁旅馆
            GameManager.instance.LateTimeCallOnce(()=>{
                GameDirector.instance.TrySetGameStateTrue('unlock_expand_1');
            }, 0.5);
        } else if(floorUnlockAreaRef == 19) { // 解锁房间
            // 解锁传送带
            GameManager.instance.LateTimeCallOnce(()=>{
            }, 0.5);
        } else if(floorUnlockAreaRef >= 20 && floorUnlockAreaRef <= 23) { // 升级房间
            GameManager.instance.LateTimeCallOnce(()=>{
                GameDirector.instance.TrySetGameStateTrue(`upgrade_${floorUnlockAreaRef - 19}`);
            }, 0.5);
        } else if(floorUnlockAreaRef >= 24 && floorUnlockAreaRef <= 27) { // 塔
            GameManager.instance.LateTimeCallOnce(()=>{
                GameDirector.instance.TrySetGameStateTrue(`unlock_tower_${floorUnlockAreaRef - 23}`);
            }, 0.5);
        }

        // 可重置地面 ui
        floorUnlockAreaList[index].OnUnlock();
        let isReloadProgress = false;
        let reloadProgress = 50;
        let reloadProgressDelay = 0.2;

        if(floorUnlockAreaRef == 1) {
            let levelIndex = 0;
            if(floorUnlockAreaRef == 1) { // 挖掘工（前 3 个）
                levelIndex = 0;
            }
            let level = this.floorUnlockCurConfigLevel[levelIndex];
            if(level < this.statueUnlockNeedConfig[levelIndex].length - 1) {
                this.floorUnlockCurConfigLevel[levelIndex] += 1;
                isReloadProgress = true;
                reloadProgress = this.statueUnlockNeedConfig[levelIndex][level + 1];
            } else {
                if(floorUnlockAreaRef == 1) {
                    // GameDirector.instance.TrySetGameStateTrue('first_3_digger_create');
                }
                this.HideFloorUnlockAreaUI(index);
            }
        } else {
            this.HideFloorUnlockAreaUI(index);
        }

        if(isReloadProgress) { // 可重置的地面 ui
            GameManager.instance.LateTimeCallOnce(()=>{
                let maxProgress = reloadProgress;
                floorUnlockAreaList[index].unlockProgress = 0;
                floorUnlockAreaList[index].unlockMaxProgress = maxProgress;
                this.floorUnlockAreaProgressList[index] = 0;
                this.floorUnlockAreaMaxProgressList[index] = maxProgress;
                this.RefreshFloorUnlockAreaUI(index);
            }, reloadProgressDelay);
            GameManager.instance.LateTimeCallOnce(()=>{
                floorUnlockAreaList[index].isUnlockFinished = false;
            }, reloadProgressDelay + 0.6);
        }

        this.isFloorUnlockAreaUnlock[index] = true;
        let isAllTableUnlock = true;
        let isArea1UnlockFinish = true;
        let isAllBedsUnlock = true;
        for(let i = 2; i <= 9; i++) {
            if(!this.isFloorUnlockAreaUnlock[i - 1]) {
                isAllTableUnlock = false;
                break;
            }
        }
        for(let i = 2; i <= 3; i++) {
            if(!this.isFloorUnlockAreaUnlock[i - 1]) {
                isArea1UnlockFinish = false;
                break;
            }
        }
        for(let i = 10; i <= 17; i++) {
            if(!this.isFloorUnlockAreaUnlock[i - 1]) {
                isAllBedsUnlock = false;
                break;
            }
        }
        if(isAllTableUnlock) {
            GameDirector.instance.TrySetGameStateTrue('finish_unlocked_area_seats');
        }
        if(isArea1UnlockFinish) {
            GameDirector.instance.TrySetGameStateTrue('finish_unlocked_area_1');
        }
        if(isAllBedsUnlock) {
            GameDirector.instance.TrySetGameStateTrue('finish_unlocked_area_beds');
        }
        // let towerUnlockNum = 0;
        // for(let i = 1; i <= 4; i++) {
        //     if(this.isFloorUnlockAreaUnlock[i]) {
        //         towerUnlockNum += 1;
        //     }
        // }
        // if(towerUnlockNum >= 2) {
        //     GameDirector.instance.TrySetGameStateTrue('unlocked_2_towers');
        // }
    }

    AddFloorUnlockAreaProgress(floorUnlockAreaRef: number, value: number) {
        let index = floorUnlockAreaRef - 1;
        this.floorUnlockAreaProgressList[index] += value;
        this.RefreshFloorUnlockAreaUI(index);
        if(this.floorUnlockAreaProgressList[index] >= this.floorUnlockAreaMaxProgressList[index]) {
            this.FloorUnlockAreaUnlock(floorUnlockAreaRef);
        }
    }

    InitFloorUnlockAreaUI() {
        this.floorUnlockAreaMaxProgressList.forEach((e, index)=>{
            let ui = this.floorUnlockAreaUINodeList[index];
            let floorUnlockArea = GameUtils.rootGameWorld.floorUnlockAreaList[index];
            if(floorUnlockArea) {
                floorUnlockArea.unlockMaxProgress = e;
                floorUnlockArea.InitUI();
            }
        });
    }

    RefreshFloorUnlockAreaUI(index: number) {
        let maxProgress = this.floorUnlockAreaMaxProgressList[index];
        // this.floorUnlockAreaMaxProgressList.forEach((e, index)=>{
        let progress = this.floorUnlockAreaProgressList[index];
        let floorUnlockArea = GameUtils.rootGameWorld.floorUnlockAreaList[index];
        if(floorUnlockArea) {
            floorUnlockArea.RefreshUI(progress, maxProgress);
        }
    }

    HideFloorUnlockAreaUI(index: number) {
        let floorUnlockArea = GameUtils.rootGameWorld.floorUnlockAreaList[index];
        if(floorUnlockArea) {
            floorUnlockArea.HideUI();
        }
    }

    // OnAbsorb(progressAdd = 1) {
    //     this.absorbProgress += progressAdd;
    //     if(this.absorbProgress >= this.absorbMaxProgress) {
    //         this.absorbProgress = this.absorbMaxProgress;
    //         this.isAbsorbProgressFull = true;
    //         if(!this.isAbsorbProgressFullCalled) {
    //             this.isAbsorbProgressFullCalled = true;
    //             GameDirector.instance.OnAbsorbProgressFull();
    //         }
    //     }
    // }

    // onDestroy() {
    //     if(this.updateCallbackId < 0) {
    //         // console.error('this.updateCallbackId < 0 !!!!');
    //     } else {
    //         GameManager.instance.RemoveUpdateCallbackByID(this.updateCallbackId);
    //     }
    // }
}
