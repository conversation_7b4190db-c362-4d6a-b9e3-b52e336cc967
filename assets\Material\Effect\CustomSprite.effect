// Copyright (c) 2017-2018 Xiamen Yaji Software Co., Ltd.  

CCEffect %{
  # mix:
  techniques:
  - passes:
    - vert: vs
      frag: fs
      blendState:
        targets:
        - blend: true
      rasterizerState:
        cullMode: none
      properties:
        texture: { value: white }
        dataTexture: { value: white }
        alphaThreshold: { value: 0.5 }
}%


CCProgram vs %{
  precision highp float;

  #include <cc-global>
  #include <cc-local>
  #include <texture>

  // 函数定义必须在 main 函数之前
  vec4 decodeDataTextrue(sampler2D dataTexture);
  vec4 loadTextrue(sampler2D dataTexture, ivec2 uv);
  ivec2 getUVFromOffset(int offset, int lineLength);
  int getOffset(int index);
  int modInt(int a, int b);

  float decodeToFloat(vec4 color);
  float decodeCustomFloat(vec4 color);
  // float decodeFloatFromRGBA(vec4 color);
  float decodeFloatFromRGBA_Simple(vec4 color);
  // int[] decodeUint8(vec4 color);

  in vec3 a_position;
  in vec4 a_color;
  in vec4 a_color0;
  in vec4 a_color1;
  out vec4 v_color;
  out vec4 v_color0;
  out vec4 v_color1;

  #if USE_TINT
  // in vec4 a_color0;
  #endif

  uniform sampler2D dataTexture;

  in vec4 a_texCoord1;
  out vec4 v_texCoord1;

  #if USE_TEXTURE
  in vec2 a_uv0;
  out vec2 v_uv0;
  #endif

  out vec3 v_modelPos; // 传递给片元着色器

  out vec4 v_lightColor; // 顶点光照计算的颜色

  void main () {
    vec4 pos = vec4(a_position, 1);

    #if CC_USE_MODEL
    pos = cc_matViewProj * cc_matWorld * pos;
    #else
    pos = cc_matViewProj * pos;
    #endif

    #if USE_TEXTURE
    v_uv0 = a_uv0;
    #endif

    #if CC_USE_MODEL
    v_modelPos = a_position; // 直接传递
    #endif
    // gl_Position = cc_matViewProj * vec4(a_position, 1.0);

    v_color = a_color;
    v_color0 = a_color0;
    v_color1 = a_color1;
    v_texCoord1 = a_texCoord1;

    // 计算顶点光照颜色
    // vec4 lightColor = vec4(1.0, 1.0, 1.0, 1.0);
    // CCTexture(dataTexture, vec2(0, 0), lightColor);
    // v_lightColor = lightColor;
    v_lightColor = decodeDataTextrue(dataTexture);

    gl_Position = pos;
  }

  // 解码数据纹理
  vec4 decodeDataTextrue(sampler2D dataTexture) {
    
    // vec4 ambientColor = loadTextrue(dataTexture, ivec2(0, 0)); // (0, 0) 处存储的是环境光
    vec4 ambientColor = loadTextrue(dataTexture, getUVFromOffset(getOffset(0), 64)); // (0, 0) 处存储的是环境光

    vec4 lightColor = ambientColor;

    // 布尔解析
    bool pointLigthsIsOn[32];
    ivec2 pointLigthsIsOnUV = getUVFromOffset(getOffset(1), 64);
    vec4 pointLigthsIsOnOrigData = loadTextrue(dataTexture, pointLigthsIsOnUV);
    // vec4 pointLigthsIsOnOrigData = loadTextrue(dataTexture, ivec2(2, 2));
    for(int i = 0; i < 4; i++) {
      float leftData;
      if(i == 0) {
        leftData = pointLigthsIsOnOrigData.a * 255.0;
      } else if(i == 1) {
        leftData = pointLigthsIsOnOrigData.b * 255.0;
      } else if(i == 2) {
        leftData = pointLigthsIsOnOrigData.g * 255.0;
      } else if(i == 3) {
        leftData = pointLigthsIsOnOrigData.r * 255.0;
      }
        // int boolIntDatas[8] = decodeUint8(pointLigthsIsOnOrigData.r * 255.0);
        // for(int j = 0; j < 8; j++) {
        //   pointLigthsIsOn[i *8 + j] = boolIntDatas[j] == 1;
        // }
      bool boolDatas[8];
      float modNum = 128.0;
      for(int j = 0; j < 8; j++) {
        boolDatas[j] = (leftData / modNum) >= 1.0;
        leftData = mod(leftData, modNum);
        modNum /= 2.0;
        pointLigthsIsOn[i *8 + j] = boolDatas[j];
      }
    }

    // 坐标 vec3 解析
    // 颜色 pointLightColor, color
    // 光照强度 pointLightIntensity, float
    // Y轴缩放 pointLightYScale, float
    // 内圈半径 pointLightInnerRadius, float
    // 外圈半径 pointLightOuterRadius, float
    // 衰减指数 pointLightFalloffExponent, float
    vec3 pointLightPositions[32];
    vec4 pointLightColors[32];
    float pointLightIntensities[32];
    float pointLightYScales[32];
    float pointLightInnerRadiuss[32];
    float pointLightOuterRadiuss[32];
    float pointLightFalloffExponents[32];
    vec4 tempLightColor = vec4(0.0, 0.0, 0.0, 0.0);
    for(int i = 0; i < 32; i++) {
      if(pointLigthsIsOn[i]) {
        vec3 pointLightPosition;
        vec4 pointLightColor;
        float pointLightIntensity;
        float pointLightYScale;
        float pointLightInnerRadius;
        float pointLightOuterRadius;
        float pointLightFalloffExponent;
        // 坐标
        for(int j = 0; j < 3; j++) {
          ivec2 pointLightPositionUV = getUVFromOffset(getOffset(2) + i * 3 + j, 64);
          vec4 pointLightPositionOrigData = loadTextrue(dataTexture, pointLightPositionUV);
          float decodedFloat = decodeCustomFloat(pointLightPositionOrigData);
          if(j == 0) {
            // pointLightPosition.x = decodeToFloat(pointLightPositionOrigData);
            pointLightPosition.x = decodedFloat;
          } else if(j == 1) {
            // pointLightPosition.y = decodeToFloat(pointLightPositionOrigData);
            pointLightPosition.y = decodedFloat;
          } else if(j == 2) {
            // pointLightPosition.z = decodeToFloat(pointLightPositionOrigData);
            pointLightPosition.z = decodedFloat;
          }
        }
        pointLightPositions[i] = pointLightPosition;
        // 颜色
        ivec2 pointLightColorUV = getUVFromOffset(getOffset(3) + i, 64);
        pointLightColor = loadTextrue(dataTexture, pointLightColorUV);
        pointLightColors[i] = pointLightColor;
        // 光照强度
        ivec2 pointLightIntensityUV = getUVFromOffset(getOffset(4) + i, 64);
        vec4 pointLightIntensityOrigData = loadTextrue(dataTexture, pointLightIntensityUV);
        pointLightIntensity = decodeToFloat(pointLightIntensityOrigData);
        pointLightIntensities[i] = pointLightIntensity;
        // Y轴缩放
        ivec2 pointLightYScaleUV = getUVFromOffset(getOffset(5) + i, 64);
        vec4 pointLightYScaleOrigData = loadTextrue(dataTexture, pointLightYScaleUV);
        pointLightYScale = decodeToFloat(pointLightYScaleOrigData);
        pointLightYScales[i] = pointLightYScale;
        // 内圈半径
        ivec2 pointLightInnerRadiusUV = getUVFromOffset(getOffset(6) + i, 64);
        vec4 pointLightInnerRadiusOrigData = loadTextrue(dataTexture, pointLightInnerRadiusUV);
        pointLightInnerRadius = decodeToFloat(pointLightInnerRadiusOrigData);
        pointLightInnerRadiuss[i] = pointLightInnerRadius;
        // 外圈半径
        ivec2 pointLightOuterRadiusUV = getUVFromOffset(getOffset(7) + i, 64);
        vec4 pointLightOuterRadiusOrigData = loadTextrue(dataTexture, pointLightOuterRadiusUV);
        pointLightOuterRadius = decodeToFloat(pointLightOuterRadiusOrigData);
        pointLightOuterRadiuss[i] = pointLightOuterRadius;
        // 衰减指数
        ivec2 pointLightFalloffExponentUV = getUVFromOffset(getOffset(8) + i, 64);
        vec4 pointLightFalloffExponentOrigData = loadTextrue(dataTexture, pointLightFalloffExponentUV);
        pointLightFalloffExponent = decodeToFloat(pointLightFalloffExponentOrigData);
        pointLightFalloffExponents[i] = pointLightFalloffExponent;
    
        // 计算点光源
        //
        vec3 posSub = pointLightPositions[i] - a_position;
        float distE = length(vec3(posSub.x, posSub.y * pointLightYScales[i], posSub.z));
        if(distE < pointLightOuterRadiuss[i]) {
          if(distE < pointLightInnerRadiuss[i]) {
            lightColor += pointLightColors[i] * pointLightIntensities[i];
          } else {
            float t = (distE - pointLightInnerRadiuss[i]) / (pointLightOuterRadiuss[i] - pointLightInnerRadiuss[i]);
            float falloff = pow(1.0 - t, pointLightFalloffExponents[i]);
            lightColor += pointLightColors[i] * pointLightIntensities[i] * falloff;
          }
          // lightColor += pointLightColors[i] * pointLightIntensities[i];
        }
      }
    }

    // float dist = length(pointLightPositions[0] - a_position);
    // if(dist < 500.0) {
    //   ivec2 pointLigthsColorUV = getUVFromOffset(getOffset(3), 64);
    //   vec4 pointLigthColor = pointLightColors[0];
    //   lightColor += pointLigthColor * pointLightIntensities[0];
    //   // lightColor = vec4(1.0, 1.0, 0.0, 1.0);
    // } else {
    //   // lightColor = vec4(0.0, 0.0, 1.0, 0.5);
    // }

    // vec3 pointLightPosition[32];
    // 测试代码
    // for(int i = 0; i < 32; i++) {
    //   if(pointLigthsIsOn[i] == true) {
    //     lightColor = vec4(0.0, 1.0, 0.0, 1.0);
    //   }
    // }
    // lightColor = pointLigthsIsOnOrigData;
    // lightColor = vec4(1.0, 1.0, 1.0, 1.0);
    return lightColor;
  }

  ivec2 getUVFromOffset(int offset, int lineLength) {
    int x = modInt(offset, lineLength);
    int y = offset / lineLength;
    return ivec2(x, y);
  }

  int modInt(int a, int b) {
    return a - (a / b) * b;
  }

  // 获取偏移值的函数，避免数组初始化问题
  int getOffset(int index) {
    if (index == 0) return 0;
    else if (index == 1) return 32;
    else if (index == 2) return 33;
    else if (index == 3) return 129;
    else if (index == 4) return 161;
    else if (index == 5) return 193;
    else if (index == 6) return 225;
    else if (index == 7) return 257;
    else if (index == 8) return 289;
    else return 0; // 默认值
  }

  // 从纹理中指定位置读取颜色值
  vec4 loadTextrue(sampler2D dataTexture, ivec2 uv) {
    // 根据 WebGL 版本选择最佳采样方法
    #if __VERSION__ >= 300
      // WebGL 2.0: 使用 texelFetch 进行精确采样，避免任何插值
      vec4 color = texelFetch(dataTexture, ivec2(uv.x, uv.y), 0);
    #else
      float x = float(uv.x) / 64.0;
      float y = float(uv.y) / 5.0;
      // WebGL 1.0: 使用 texture2D，但确保纹理设置为 NEAREST 过滤
      vec4 color = texture2D(dataTexture, vec2(x, y));
    #endif
    return color;
  }

  float decodeToFloat(vec4 color) {
    // float rData = color.r * 255.0 * 256.0 * 256.0 * 256.0;
    // float gData = color.g * 255.0 * 256.0 * 256.0;
    // float bData = color.b * 255.0 * 256.0;
    // float aData = color.b * 255.0;
    // return intBitsToFloat(rData + gData + bData + aData);
    return decodeFloatFromRGBA_Simple(color);
  }

  float decodeCustomFloat(vec4 color) {
    // 24 位整数 + 7 位小数 + 1 位符号
    vec4 bytes = color * 255.0; // +0.5用于四舍五入
    // 提取各个字节（小端序）
    float byte0 = bytes.r;
    float byte1 = bytes.g;
    float byte2 = bytes.b;
    float byte3 = bytes.a;

    // 提取符号位
    float sign = (byte0 >= 128.0) ? -1.0 : 1.0;
    // float sign = byte0 >= 1.0 ? -1.0 : 1.0;
    // 提取整数部分
    float intPart = byte3 * 256.0 * 256.0 + byte2 * 256.0 + byte1;
    // 提取小数部分
    float fracPart = mod(byte0, 128.0) / 128.0;
    return sign * (intPart + fracPart);
    // return intPart;
  }

  // 将4个0-255整数解码为32位浮点数（IEEE 754标准）
  // 兼容版本：不使用intBitsToFloat，使用纯数学运算
  float decodeFloatFromRGBA(vec4 color) {
    // 将归一化的颜色值转换回0-255范围的整数
    vec4 bytes = color * 255.0 + 0.5; // +0.5用于四舍五入

    // IEEE 754单精度浮点数格式：
    // 位31: 符号位 (S)
    // 位30-23: 指数位 (E) - 8位
    // 位22-0: 尾数位 (M) - 23位

    // 提取各个字节（小端序）
    float byte0 = bytes.r; // 最低字节：尾数的低8位
    float byte1 = bytes.g; // 尾数的中8位
    float byte2 = bytes.b; // 尾数的高7位 + 指数的低1位
    float byte3 = bytes.a; // 指数的高7位 + 符号位

    // 提取符号位（第31位）
    float sign = (byte3 >= 128.0) ? -1.0 : 1.0;

    // 提取指数（第30-23位）
    float exp_high = mod(byte3, 128.0); // 去掉符号位
    float exp_low = floor(byte2 / 128.0); // byte2的最高位
    float exponent = exp_high * 2.0 + exp_low;

    // 提取尾数（第22-0位）
    float mantissa_high = mod(byte2, 128.0); // byte2的低7位
    float mantissa_mid = byte1;
    float mantissa_low = byte0;

    // 重构尾数值（23位）
    float mantissa = (mantissa_high * 65536.0 + mantissa_mid * 256.0 + mantissa_low) / 8388608.0; // 2^23

    // 处理特殊情况
    if (exponent == 0.0) {
      // 非规格化数或零
      if (mantissa == 0.0) {
        return sign * 0.0; // 正零或负零
      } else {
        // 非规格化数：(-1)^S × 2^(-126) × (0.mantissa)
        return sign * pow(2.0, -126.0) * mantissa;
      }
    } else if (exponent == 255.0) {
      // 无穷大或NaN
      if (mantissa == 0.0) {
        return sign * (1.0 / 0.0); // 无穷大
      } else {
        return 0.0 / 0.0; // NaN（在shader中可能不完全支持）
      }
    } else {
      // 规格化数：(-1)^S × 2^(E-127) × (1.mantissa)
      float realExponent = exponent - 127.0;
      return sign * pow(2.0, realExponent) * (1.0 + mantissa);
    }
  }

  // 简化版本：适用于大多数常见浮点数值（不处理特殊值如NaN、无穷大）
  // 如果您的数据不包含这些特殊值，这个版本更简单且兼容性更好
  float decodeFloatFromRGBA_Simple(vec4 color) {
    // 将归一化的颜色值转换回0-255范围
    vec4 bytes = floor(color * 255.0 + 0.5);

    // 提取符号位
    float sign = (bytes.a >= 128.0) ? -1.0 : 1.0;

    // 提取指数（简化处理）
    float exp_high = mod(bytes.a, 128.0);
    float exp_low = floor(bytes.b / 128.0);
    float exponent = exp_high * 2.0 + exp_low - 127.0; // 减去偏移量

    // 提取尾数
    float mantissa_high = mod(bytes.b, 128.0);
    float mantissa = (mantissa_high * 65536.0 + bytes.g * 256.0 + bytes.r) / 8388608.0;

    // 简化计算：只处理规格化数
    if (exp_high == 0.0 && exp_low == 0.0 && mantissa == 0.0) {
      return 0.0; // 零值
    }

    return sign * pow(2.0, exponent) * (1.0 + mantissa);
  }
}%


CCProgram fs %{
  precision highp float;
  
  #include <alpha-test>
  #include <texture>

  struct hsv { float h; float s; float v; };

  vec3 fromHSV (hsv hsvInput);
  hsv toHsv(vec3 color);
  float ArrayMaxLen3(float array[3]);
  float ArrayMinLen3(float array[3]);

  in vec4 v_color;
  in vec4 v_color0;
  in vec4 v_color1;
  // in vec4 v_position;  // 片元坐标
  // in vec4 v_positionWorld;  // 片元的世界坐标
  in vec3 v_modelPos; // 从顶点着色器传来的模型坐标

  in vec4 v_texCoord1;

  // uniform mix {
  //   vec4 mixColor;
  //   float mixRatio;
  // };
  // uniform HSV {
  //   int H;
  //   int S;
  //   int V;
  // };
  // uniform hyalinize {
  //   float hyalinize;
  //   float hyalinizeHeight;
  // };

  in vec4 v_lightColor;

  #if USE_TEXTURE
  in vec2 v_uv0;
  uniform sampler2D texture;
  #endif

  uniform sampler2D dataTexture;

  void main () {
    float mixRatio = v_texCoord1.w;
    vec4 mixColor = v_color0;
    hsv hsvValue = hsv(0.0, 0.0, 0.0);

    float ratio = 0.0;
    // if(mixRatio != 0.0) {
      ratio = mixRatio;
    // }

    vec4 o = vec4(1, 1, 1, 1);

    int H = int(v_texCoord1.x);
    int S = int(v_texCoord1.y);
    int V = int(v_texCoord1.z);

    #if USE_TEXTURE
      CCTexture(texture, v_uv0, o);
    #endif

    o *= v_color; // 颜色信息
    // o.a *= v_color.a;
    ALPHA_TEST(o);
    
    hsvValue = toHsv(o.rgb);
    if(H != 0) {
      hsvValue.h += float(H) / 360.0; // 色相
    }
    if(S != 0) {
      hsvValue.s *= float(100 + S) / 100.0; // 饱和度
    }
    if(V != 0) {
      hsvValue.v *= float(100 + V) / 100.0; // 亮度
    }
    o = vec4(fromHSV(hsvValue), o.a);

    float ax = 1.0;

    #if CC_USE_MODEL
    float y = v_modelPos.y;
    // if(hyalinize >= 1.0) {
    //   ax = 0.0;
    // } else if(hyalinizeHeight > 0.0) {
    //   if(y < hyalinizeHeight) {
    //     ax = 1.0 - hyalinize;
    //   } else {
    //   }
    // }
    #endif

    float a = o.a * ax;

    // 处理光接收器属性
    // v_color1.r 现在直接包含位标志数据 (0-255 范围)
    float data = v_color1.r * 255.0; // 将归一化的颜色值转换回 0-255 范围
    float leftData = data;
    bool isLightReceiver = (leftData / 128.0) >= 1.0;
    leftData = mod(leftData, 128.0);
    bool isChangeOpacity = (leftData / 64.0) >= 1.0;
    leftData = mod(leftData, 64.0);
    bool isToFrag = (leftData / 32.0) >= 1.0;
    // ...后续的解码不需要

    // 得到环境光和环境光强度
    // vec4 ambient = vec4(1.0, 1.0, 1.0, 1.0);
    // CCTexture(dataTexture, vec2(0, 0), ambient);
    // 使用顶点传递的光照颜色
    vec4 ambient = v_lightColor;

    vec3 mixedColorRgb = (o.rgb * (1.0 - ratio) + mixColor.rgb * ratio);
    // vec3 mixedColorRgb = (o.rgb * (1.0 - (ratio > 0.0 ? ratio : 0.0));
    if(isLightReceiver) {
      vec3 outRgb = mixedColorRgb * (ambient.rgb * (isChangeOpacity? 1.0 : ambient.a));
      o = vec4(outRgb, a * (isChangeOpacity? ambient.a : 1.0));
    } else {
      o = vec4(mixedColorRgb, a);
    }
    // o = vec4(mixedColorRgb, a);


    ALPHA_TEST(o);

    gl_FragColor = o;
  }
  
  vec3 fromHSV (hsv hsvInput) {
    float h = hsvInput.h;
    float s = hsvInput.s;
    float v = hsvInput.v;
    float r = 0.0;
    float g = 0.0;
    float b = 0.0;
    float f;
    float p;
    float q;
    float t;
    int i = 0;
    if(s == 0.0) {
        r = g = b = v;
    } else {
      if (v == 0.0) {
        r = g = b = 0.0;
      } else {
        if(h == 1.0) h = 0.0;
        if(h > 1.0) h -= 1.0;
        if(h < 0.0) h += 1.0;
        h *= 6.0;
        s = s;
        v = v;
        i = int(h);

        f = h - float(i);
        p = v * (1.0 - s);  
        q = v * (1.0 - (s * f));  
        t = v * (1.0 - (s * (1.0 - f)));
        if(i == 0) {  
          r = v;  
          g = t;  
          b = p;
        } else if(i == 1) {  
          r = q;  
          g = v;  
          b = p;  
        } else if(i == 2) {  
          r = p;  
          g = v;  
          b = t;
        } else if(i == 3) {
          r = p;  
          g = q;  
          b = v;  
        } else if(i == 4) {
          r = t;  
          g = p;  
          b = v;  
        } else if(i == 5) {
          r = v;  
          g = p;  
          b = q;  
        }
      }
    }
    return vec3(r, g, b);
  }

  hsv toHsv(vec3 color) {
    hsv hsvResult = hsv(0.0, 0.0, 0.0);
    float r = color.r;
    float g = color.g;
    float b = color.b;
    float rgbArray[3];
    rgbArray[0] = r;
    rgbArray[1] = g;
    rgbArray[2] = b;
    float maxV = ArrayMaxLen3(rgbArray);
    float minV = ArrayMinLen3(rgbArray);
    float delta = 0.0;
    hsvResult.v = maxV;
    hsvResult.s = maxV == 0.0 ? 0.0 : ((maxV - minV) / maxV);
    if (hsvResult.s == 0.0) hsvResult.h = 0.0;
    else {
      delta = maxV - minV;
      if (r == maxV) hsvResult.h = (g - b) / delta;
      else if (g == maxV) hsvResult.h = 2.0 + (b - r) / delta;
      else hsvResult.h = 4.0 + (r - g) / delta;
      hsvResult.h /= 6.0;
      if (hsvResult.h < 0.0) hsvResult.h += 1.0;
    }
    return hsvResult;
  }

  float ArrayMaxLen3(float array[3]) {
    return array[0] > array[1] ? (array[0] > array[2] ? array[0] : array[2]) : (array[1] > array[2] ? array[1] : array[2]);
  }

  float ArrayMinLen3(float array[3]) {
    return array[0] < array[1] ? (array[0] < array[2] ? array[0] : array[2]) : (array[1] < array[2] ? array[1] : array[2]);
  }
}%

