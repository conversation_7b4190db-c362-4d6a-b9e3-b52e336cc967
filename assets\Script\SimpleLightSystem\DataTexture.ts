
export enum DataTextureOriginalDataType {
    none,
    double,
    float,
    int,
    bool,
    color,
    vector2,
    vector3,
    vector4,
    char,
}

export enum DataTextureDataFormatType {
    none,
    double, // 一个完整的 64 位颜色 | 一个 double 数值 | 2 个 float 数值（高低位数字）
    float32, // 一个完整的 32 位颜色 | 一个 float 数值
    rgba8, // 一个 8 位颜色分量（0-255），在和其它 8 位颜色值顺序存储时，会每 4 个合并在一起
    bit, // 一个位，只存（0，1）,每 32 个合并
    texture, // 纹理单元
}


import { StringDictionary } from "../LocalUtils";
import { SimpleLightInfo, SimpleLightType } from "./SimpleLightSystemManager";


/** 数据化纹理  
 * 将数据写入纹理 Texture2D，用于材质 Effect 读取和解析
 */

export default class DataTexture {


    static dataTextureDataFormats: StringDictionary<DataTextureDataFormat> = {
        'Test': {
            mark: '简单的数据测试', formatCell: null, isGroup: true, groupFormats: [
                { mark: '传入一个颜色值', formatCell: 
                    { key: 'color', formatType: DataTextureOriginalDataType.color, }, sizeNum: 1,
                },
            ],
        },
        'ParametricLightsMap': {
            mark: '参数化光照数据', formatCell: null, isGroup: true, groupFormats: [
                { mark: '环境光颜色数据', formatCell: 
                    { key: 'ambientColor', formatType: DataTextureOriginalDataType.color, }, sizeNum: 32,
                },
                { mark: '点光源', formatCell: null, isGroup: true, groupFormats: [
                    // 支持固定数量动态点光源
                    { mark: '是否启用点光源', formatCell: 
                        { key: 'pointLightIsOn', formatType: DataTextureOriginalDataType.bool, }, sizeNum: 32,
                    },
                    { mark: '位置', formatCell: 
                        { key: 'pointLightPosition', formatType: DataTextureOriginalDataType.vector3, }, sizeNum: 32,
                    },
                    { mark: '颜色', formatCell: 
                        { key: 'pointLightColor', formatType: DataTextureOriginalDataType.color, }, sizeNum: 32,
                    },
                    { mark: '光照强度', formatCell: 
                        { key: 'pointLightIntensity', formatType: DataTextureOriginalDataType.float, }, sizeNum: 32,
                    },
                    { mark: 'Y轴缩放', formatCell: 
                        { key: 'pointLightYScale', formatType: DataTextureOriginalDataType.float, }, sizeNum: 32,
                    },
                    { mark: '内圈半径', formatCell: 
                        { key: 'pointLightInnerRadius', formatType: DataTextureOriginalDataType.float, }, sizeNum: 32,
                    },
                    { mark: '外圈半径', formatCell: 
                        { key: 'pointLightOuterRadius', formatType: DataTextureOriginalDataType.float, }, sizeNum: 32,
                    },
                    { mark: '衰减指数', formatCell: 
                        { key: 'pointLightFalloffExponent', formatType: DataTextureOriginalDataType.float, }, sizeNum: 32,
                    },
                ]},
            ],
        },

    };

    static uniformTextureFormats: StringDictionary<UniformTextureFormat> = {};



    /** 根据不同的数据格式，创建数据 */

    /** 测试 */
    static WriteTestColor(color: cc.Color, texture?: cc.Texture2D): cc.Texture2D {
        let data = new DataTextureRawData();
        data.key = 'color';
        data.values = [color];
        data.originalDataType = DataTextureOriginalDataType.color;
        // data.formatType = DataTextureDataFormatType.float32;
        
        return this.WriteDataTexture([data], 'Test', texture);
    }

    static WriteParametricLightsMap(ambient: cc.Color, lights: SimpleLightInfo[], texture?: cc.Texture2D): cc.Texture2D {
        let pointLightDataListIsOn: boolean[] = [];
        let pointLightDataListPosition: cc.Vec3[] = [];
        let pointLightDataListColor: cc.Color[] = [];
        let pointLightDataListIntensity: number[] = [];
        let pointLightDataListYScale: number[] = [];
        let pointLightDataListInnerRadius: number[] = [];
        let pointLightDataListOuterRadius: number[] = [];
        let pointLightDataListFalloffExponent: number[] = [];

        let pointLightCurIndex = 0;
        lights.forEach((lightInfo)=>{
            if(lightInfo.type == SimpleLightType.point) {
                let script = lightInfo.script;
                pointLightDataListIsOn[pointLightCurIndex] = true;
                pointLightDataListPosition[pointLightCurIndex] = script.worldPosition;
                pointLightDataListColor[pointLightCurIndex] = script.lightColor;
                pointLightDataListIntensity[pointLightCurIndex] = script.lightIntensity;
                pointLightDataListYScale[pointLightCurIndex] = script.lightYScale;
                pointLightDataListInnerRadius[pointLightCurIndex] = script.innerRadius;
                pointLightDataListOuterRadius[pointLightCurIndex] = script.outerRadius;
                pointLightDataListFalloffExponent[pointLightCurIndex] = script.falloffExponent;

                pointLightCurIndex += 1;
            }
        });
        // 测试代码
        // for(let i = 0; i < 32; i++) {
        //     pointLightDataListIsOn[i] = true;
        //     pointLightDataListColor[i] = cc.color(180, 180, 20, 255);
        // }

        let rawDatas: DataTextureRawData[] = [];

        let dataAmbient = new DataTextureRawData();
        dataAmbient.key = 'ambientColor';
        dataAmbient.values = [ambient];
        dataAmbient.originalDataType = DataTextureOriginalDataType.color;
        rawDatas.push(dataAmbient);
        
        let data0 = new DataTextureRawData();
        data0.key = 'pointLightIsOn';
        data0.values = pointLightDataListIsOn;
        data0.originalDataType = DataTextureOriginalDataType.bool;
        rawDatas.push(data0);
        let data1 = new DataTextureRawData();
        data1.key = 'pointLightPosition';
        data1.values = pointLightDataListPosition;
        data1.originalDataType = DataTextureOriginalDataType.vector3;
        rawDatas.push(data1);
        let data2 = new DataTextureRawData();
        data2.key = 'pointLightColor';
        data2.values = pointLightDataListColor;
        data2.originalDataType = DataTextureOriginalDataType.color;
        rawDatas.push(data2);
        let data3 = new DataTextureRawData();
        data3.key = 'pointLightIntensity';
        data3.values = pointLightDataListIntensity;
        data3.originalDataType = DataTextureOriginalDataType.float;
        rawDatas.push(data3);
        let data4 = new DataTextureRawData();
        data4.key = 'pointLightYScale';
        data4.values = pointLightDataListYScale;
        data4.originalDataType = DataTextureOriginalDataType.float;
        rawDatas.push(data4);
        let data5 = new DataTextureRawData();
        data5.key = 'pointLightInnerRadius';
        data5.values = pointLightDataListInnerRadius;
        data5.originalDataType = DataTextureOriginalDataType.float;
        rawDatas.push(data5);
        let data6 = new DataTextureRawData();
        data6.key = 'pointLightOuterRadius';
        data6.values = pointLightDataListOuterRadius;
        data6.originalDataType = DataTextureOriginalDataType.float;
        rawDatas.push(data6);
        let data7 = new DataTextureRawData();
        data7.key = 'pointLightFalloffExponent';
        data7.values = pointLightDataListFalloffExponent;
        data7.originalDataType = DataTextureOriginalDataType.float;
        rawDatas.push(data7);

        return this.WriteDataTexture(rawDatas, 'ParametricLightsMap', texture);
    }

    static WriteDataTexture(rawDatas: DataTextureRawData[], dataFormatName: string, texture?: cc.Texture2D): cc.Texture2D {
        // 将数据写入纹理中
        // 根据数据大小，决定纹理大小（纹理尺寸数值必须为 2 的幂？）
        // 写入的最终像素格式为 RGBA8888，存储 32 位大小的数据单元
        // 写入的数据头：用于解码像素，存入所有数据的类型、大小等，可在 effect 中解析
        let dataFormat = this.dataTextureDataFormats[dataFormatName];
        if(!dataFormat) { cc.error(`WriteDataTexture[0] 数据纹理格式不存在：${dataFormatName}`); return null; }
        let textureFormat = this.GetFormat(dataFormatName);
        if(!textureFormat) { cc.error(`WriteDataTexture[1] 数据纹理格式不存在：${dataFormatName}`); return null; }

        let datas: Float32Array[] = this.CreateDataTextureDatas(rawDatas, dataFormat, textureFormat);
        // 创建纹理
        let tex: cc.Texture2D = null;
        if(texture) {
            tex = texture;
        } else {
            tex = new cc.Texture2D();
        }

        let size = datas.length;
        let texture2DData = new Uint8Array(Math.ceil(size /64) * 64 * 4);
        for (let i = 0; i < size; i++) {
            let data4Uint = new Uint8Array(datas[i].buffer);
            texture2DData[i * 4 + 0] = data4Uint[0];
            texture2DData[i * 4 + 1] = data4Uint[1];
            texture2DData[i * 4 + 2] = data4Uint[2];
            texture2DData[i * 4 + 3] = data4Uint[3];
            // texture2DData[i * 4 + 3] = 255;
        }
        if(size <= 64) {
            tex.initWithData(texture2DData, cc.Texture2D.PixelFormat.RGBA8888, size, 1);
        } else if(size <= 64 * 64) {
            // tex.initWithData(texture2DData, cc.Texture2D.PixelFormat.RGBA8888, 64, (size /64));
            tex.initWithData(texture2DData, cc.Texture2D.PixelFormat.RGBA8888, 64, Math.ceil(size /64));
        } else {
            cc.warn('数据大小超过了 64 × 64！');
        }

        // 设置为 NEAREST 过滤，确保精确采样，避免插值
        tex.setFilters(cc.Texture2D.Filter.NEAREST, cc.Texture2D.Filter.NEAREST);

        return tex;
    }


    static CreateDataTextureDatas(rawDatas: DataTextureRawData[], dataFormat: DataTextureDataFormat, textureFormat: UniformTextureFormat): Float32Array[] {
        // 根据最终格式创建数据结构
        // 将数据填入该数据结构
        // 按格式的顺序，而不是数据的顺序
        let datas: Float32Array[] = [];

        let formatCells: UniformTextureFormatCell[] = [];
        let rawDatasOffsets: number[] = [];
        let rawDatasMaxLengths: number[] = [];

        let curOffset = 0;

        textureFormat.formats.forEach((formatCell)=>{
            let rawDataIndex = rawDatas.findIndex((e)=>{
                return e.key == formatCell.key;
            });
            // 所有格式，必须能够对应 key 找到数据，没有计入格式的数据 key，或者格式没有对应 key 的数据，就会忽略
            if(rawDataIndex >= 0) {
                // 如果格式大小为 0，则不写入
                if(formatCell.num <= 0) return;
                if(formatCell.formatType == DataTextureDataFormatType.float32
                      || formatCell.formatType == DataTextureDataFormatType.double
                      || formatCell.formatType == DataTextureDataFormatType.rgba8
                      || formatCell.formatType == DataTextureDataFormatType.bit ) { // 必须是支持的格式
                    let length = this.GetLengthWithFormatAndNum(formatCell.formatType, formatCell.num);
                    formatCells[rawDataIndex] = formatCell;
                    rawDatasOffsets[rawDataIndex] = curOffset;
                    rawDatasMaxLengths[rawDataIndex] = length;
                    curOffset += length;
                }
            }
        });

        datas = new Array(textureFormat.float32length);

        rawDatas.forEach((rawData, rawDataIndex)=>{
            let offset = rawDatasOffsets[rawDataIndex];
            if(offset == undefined) { cc.error(`CreateDataTextureDatas 原始数据 key [${rawDatas[rawDataIndex].key}] 在预设格式中不存在 !`); return; }

            // 从格式信息中找到 key 对应的格式
            let formatCell: UniformTextureFormatCell = formatCells[rawDataIndex];
            let rawDataformatCell: DataTextureDataFormatCell = formatCell.rawDataFormatCell;
            let maxLength = rawDatasMaxLengths[rawDataIndex];

            // 创建一个数据，其可能是单个 Float32Array 数值，或者有长度的 Float32Array 数值
            let data = this.CreateDataTextureData(rawData.values, rawDataformatCell, maxLength);
            // 将 Float32Array 数值写入 datas 数据列表中
            this.PushInDatas({val: datas}, data, offset, maxLength);
        });
        return datas;
    }

    /** 根据原始数据，创建要传入纹理的格式化数据 */
    static CreateDataTextureData(values: any[], rawDataformatCell: DataTextureDataFormatCell, maxLength: number): Float32Array {
        let errorId = -1;
        let data: Float32Array = new Float32Array(maxLength);
        let bitNum = 0;
        if(rawDataformatCell.formatType == DataTextureOriginalDataType.color) {
            bitNum = 32;
            for(let i = 0; i < values.length; i++) {
                let value = values[i];
                if(value instanceof cc.Color) {
                    let singleData: Float32Array = this.EncodeColorToFloat(value);
                    data.set(singleData, i);
                } else { errorId = 1; }
            }
        } else if(rawDataformatCell.formatType == DataTextureOriginalDataType.float) {
            bitNum = 32;
            for(let i = 0; i < values.length; i++) {
                let value = values[i];
                if(typeof value == 'number') {
                    let singleData: Float32Array = new Float32Array(1);
                    singleData[0] = value;
                    data.set(singleData, i);
                } else { errorId = 2; }
            }
        } else if(rawDataformatCell.formatType == DataTextureOriginalDataType.vector3) {
            bitNum = 32 * 3;
            for(let i = 0; i < values.length; i++) {
                let value = values[i];
                if(value instanceof cc.Vec3) {
                    let singleData: Float32Array = new Float32Array(3);
                    // singleData[0] = value.x;
                    // singleData[1] = value.y;
                    // singleData[2] = value.z;
                    singleData[0] = this.EncodeFloatToCustomFloat(value.x)[0];
                    singleData[1] = this.EncodeFloatToCustomFloat(value.y)[0];
                    singleData[2] = this.EncodeFloatToCustomFloat(value.z)[0];
                    data.set(singleData, i * 3);
                } else { errorId = 3; }
            }
        } else if(rawDataformatCell.formatType == DataTextureOriginalDataType.bool) {
            bitNum = 1;
            let boolGroups: boolean[][] = [];
            for(let i = 0; i < values.length; i++) {
                let value = values[i];
                if(typeof value == 'boolean') {
                    if(!boolGroups[Math.floor(i / 32)]) {
                        boolGroups[Math.floor(i / 32)] = [];
                    }
                    boolGroups[Math.floor(i / 32)][i % 32] = value;
                } else { errorId = 4; }
            }
            boolGroups.forEach((group, index)=>{
                let singleData: Float32Array = new Float32Array(1);
                let num = 0;
                for(let i = 0; i < 32; i++) {
                    if(group[i]) num |= 1 << (31 - i);
                }
                singleData[0] = num;
                data.set(singleData, index);
            });
        }
        if(errorId >= 0) { cc.error(`CreateDataTextureData 数据格式错误！${errorId}`); }
        return data;
    }

    static ConcatFloat32Array(data1: Float32Array, data2: Float32Array): Float32Array {
        let data = new Float32Array(data1.length + data2.length);
        data.set(data1);
        data.set(data2, data1.length);
        return data;
    }

    static PushInDatas(datas_out: {val: Float32Array[]}, data: Float32Array, start: number, maxLength: number) {
        if(data.length == 1) {
            this.FlushInArray({val: datas_out.val}, [data], start, maxLength);
        } else if(data.length > 1) {
            let dataList: Float32Array[] = [];
            for(let i = 0; i < data.length; i++) {
                let singleData = new Float32Array(1);
                singleData[0] = data[i];
                // datas.push(singleData);
                dataList.push(singleData);
            }
            this.FlushInArray({val: datas_out.val}, dataList, start, maxLength);
        }
    }

    /** 数值方法：将一段数据刷入主数组中，保护原数组的长度 */
    static FlushInArray<T>(srcArray_out: {val: T[]}, array: T[], start: number = 0, maxLength: number = -1) {
        let length = array.length;
        if(maxLength >= 0) {
            length = maxLength;
        }
        if(start + length > srcArray_out.val.length) {
            length = srcArray_out.val.length - start;
        }
        let puttinginArray = new Array(length);
        puttinginArray.splice(0, length, ...array.slice(0, length));
        srcArray_out.val.splice(start, length, ...puttinginArray);
    }

    // 将普通的浮点数编码为方便 rgba 解析的数值
    // 8位整数 + 16位小数 + 1位符号
    // static EncodeFloatToCustomFloat(value: number): Float32Array {
    //     // 不直接传入浮点数原始数据，进行转化之后存储
    //     let singleData: Float32Array = new Float32Array(1);
    //     let valueAbs = Math.abs(value);
    //     let partInt = Math.floor(valueAbs);
    //     let partFrac = valueAbs - partInt;
    //     let partFrac1Full = partFrac * 256 * 256;
    //     let partFrac1 = Math.floor(partFrac1Full);
    //     let partFrac2Full = (partFrac1Full - partFrac1) * 256;
    //     let partFrac2 = Math.floor(partFrac2Full);
    //     // let partFrac3Full = (partFrac2Full - partFrac2);
    //     // let partFrac3 = Math.floor(partFrac3Full);
    //     let sign = value < 0 ? 1 : 0;
        
    //     let rgbData = new Uint8Array(singleData.buffer);
    //     rgbData[0] = partInt;
    //     rgbData[1] = partFrac1;
    //     rgbData[2] = partFrac2;
    //     rgbData[3] = sign;
    //     return singleData;
    // }

    // 将普通的浮点数编码为方便 rgba 解析的数值
    // 24 位整数 + 7 位小数 + 1 位符号
    static EncodeFloatToCustomFloat(value: number): Float32Array {
        // 不直接传入浮点数原始数据，进行转化之后存储
        let singleData: Float32Array = new Float32Array(1);
        let valueAbs = Math.abs(value);
        if(valueAbs > 16777215) { 
            cc.warn(`EncodeFloatToCustomFloat: 数值过大，已截断！${value}`);
            valueAbs = 16777215;
        }
        let leftValue = valueAbs;
        let partInt1 = Math.floor(leftValue / (256 * 256));
        let reduce1 = partInt1 * 256 * 256;
        leftValue -= reduce1;
        let partInt2 = Math.floor(leftValue / 256);
        let reduce2 = partInt2 * 256;
        leftValue -= reduce2;
        let partInt3 = Math.floor(leftValue);
        let reduce3 = partInt3;
        leftValue -= reduce3;
        let partFrac = Math.floor(leftValue * 128);
        let sign = value < 0 ? 1 : 0;
        if(partFrac > 127) {
            partFrac = 127;
        }
        let rgbData = new Uint8Array(singleData.buffer);
        rgbData[0] = partFrac | (sign << 7);
        // rgbData[0] = sign;
        rgbData[1] = partInt3;
        rgbData[2] = partInt2;
        rgbData[3] = partInt1;
        return singleData;
    }

    /** 将颜色编码为一个 float 型数值 */
    static EncodeColorToFloat(color: cc.Color): Float32Array {
        let data = new Float32Array(1);
        let rgbData = new Uint8Array(data.buffer);
        rgbData[0] = color.r;
        rgbData[1] = color.g;
        rgbData[2] = color.b;
        rgbData[3] = color.a;
        return data;
    }

    static FindDataFormat(key: string, dataTextureDataFormat: DataTextureDataFormat): DataTextureDataFormat {
        let format: DataTextureDataFormat = null;
        if(dataTextureDataFormat.isGroup && dataTextureDataFormat.groupFormats) {
            for(let i = 0; i < dataTextureDataFormat.groupFormats.length; i++) {
                if(!format) {
                    format = this.FindDataFormat(key, dataTextureDataFormat.groupFormats[i]);
                }
            }
        } else {
            if(dataTextureDataFormat.formatCell.key == key) {
                return dataTextureDataFormat;
            }
        }
        return format;
    }

    /** 获取写入 Texture 内的数据格式 */
    static GetFormat(formatName: string): UniformTextureFormat {
        if(!this.uniformTextureFormats[formatName]) {
            let dataTextureDataFormat = this.dataTextureDataFormats[formatName];
            if(dataTextureDataFormat) {
                this.uniformTextureFormats[formatName] = this.GenerateFormat(dataTextureDataFormat);
            }
        }
        return this.uniformTextureFormats[formatName];
    }

    static GenerateFormat(dataTextureDataFormat: DataTextureDataFormat): UniformTextureFormat {
        let uniformTextureFormat: UniformTextureFormat = null;
        let formats = this.GenerateFormatCells(dataTextureDataFormat);
        if(formats.length > 0) {
            let float32length = 0;
            formats.forEach((e)=>{
                float32length += this.GetLengthWithFormatAndNum(e.formatType, e.num);
            });
            uniformTextureFormat = { formats: formats, float32length: float32length };
        }
        return uniformTextureFormat;
    }

    static GenerateFormatCells(dataTextureDataFormat: DataTextureDataFormat): UniformTextureFormatCell[] {
        let cells: UniformTextureFormatCell[] = [];
        if(dataTextureDataFormat.isGroup && dataTextureDataFormat.groupFormats) {
            if(dataTextureDataFormat.groupFormats.length > 0) {
                dataTextureDataFormat.groupFormats.forEach((e)=>{
                    cells = cells.concat(this.GenerateFormatCells(e));
                });
            }
        } else {
            if(dataTextureDataFormat.sizeNum >= 1) {
                cells = [this.GenerateFormatCell(dataTextureDataFormat.formatCell, dataTextureDataFormat.sizeNum)];
            }
        }
        return cells;
    }

    static GetLengthWithFormatAndNum(formatType: DataTextureDataFormatType, num: number): number {
        if(formatType == DataTextureDataFormatType.float32
              || formatType == DataTextureDataFormatType.double
              || formatType == DataTextureDataFormatType.rgba8
              || formatType == DataTextureDataFormatType.bit ) { // 必须是支持的格式
            let length = 0;
            if(formatType == DataTextureDataFormatType.float32) { // 标准的格式，一个占 32 位
                length = num * 1;
            } else if(formatType == DataTextureDataFormatType.bit) { // 位格式，32 个占一个 32 位
                length = Math.ceil(num / 32);
            } else if(formatType == DataTextureDataFormatType.double) { // 双精度浮点，占 2 个 32 位
                length = num * 2;
            } else if(formatType == DataTextureDataFormatType.rgba8) { // 双精度浮点，占 8 位，每 4 个占一个 32 位
                length = Math.ceil(num / 4);
            }
            return length;
        } else {
            cc.error(`GetLengthWithFormatAndNum 不支持的格式：${formatType}`);
            return 0;
        }
    }

    /** 不同的原始数据格式会生成不同的纹理数据格式 */
    static GenerateFormatCell(dataTextureDataFormatCell: DataTextureDataFormatCell, num = 1): UniformTextureFormatCell {
        let cell = dataTextureDataFormatCell;
        let type = cell.formatType;
        let key = cell.key;
        if(type == DataTextureOriginalDataType.color) {
            return { key: key, formatType: DataTextureDataFormatType.float32, num: num, rawDataFormatCell: cell };
        } else if(type == DataTextureOriginalDataType.double) {
            return { key: key, formatType: DataTextureDataFormatType.double, num: num, rawDataFormatCell: cell };
        } else if(type == DataTextureOriginalDataType.float) {
            return { key: key, formatType: DataTextureDataFormatType.float32, num: num, rawDataFormatCell: cell };
        } else if(type == DataTextureOriginalDataType.int) {
            return { key: key, formatType: DataTextureDataFormatType.float32, num: num, rawDataFormatCell: cell };
        } else if(type == DataTextureOriginalDataType.bool) {
            return { key: key, formatType: DataTextureDataFormatType.bit, num: num, rawDataFormatCell: cell };
        } else if(type == DataTextureOriginalDataType.vector2) {
            return { key: key, formatType: DataTextureDataFormatType.float32, num: num * 2, rawDataFormatCell: cell };
        } else if(type == DataTextureOriginalDataType.vector3) {
            return { key: key, formatType: DataTextureDataFormatType.float32, num: num * 3, rawDataFormatCell: cell };
        } else if(type == DataTextureOriginalDataType.vector4) {
            return { key: key, formatType: DataTextureDataFormatType.float32, num: num * 4, rawDataFormatCell: cell };
        } else if(type == DataTextureOriginalDataType.char) {
            return { key: key, formatType: DataTextureDataFormatType.float32, num: num * 3, rawDataFormatCell: cell };
        }
    }

    static EncodeFloatToRGBA(value: number): [number, number, number, number] {
        const floatView = new Float32Array(1);
        const intView = new Uint8Array(floatView.buffer);
        floatView[0] = value;

        return [intView[0], intView[1], intView[2], intView[3]]; 
    }
}

export class DataTextureRawData {
    key: string = '';
    // value: any = null;
    values: any[];
    originalDataType: DataTextureOriginalDataType = DataTextureOriginalDataType.none;
    // formatType: DataTextureDataFormatType = DataTextureDataFormatType.none;
}

/** 这是数据格式，每种 Uniform 传参都有一个固定格式，用于写入纹理 */
export type DataTextureDataFormat = {

    formatCell: DataTextureDataFormatCell;
    mark?: string,
    /** 大小，不指定或为 0 则忽略，不会写入空间中 */
    sizeNum?: number;
    // 注意：格式可以嵌套，当指定为组时，将逐个读取组内的格式，忽略上面的格式
    // 最终的数据写入是顺序向下的
    isGroup?: boolean;
    groupFormats?: DataTextureDataFormat[],
}

/** 这是数据格式的单元，根据单个数据的类型和空间大小，写入纹理空间中 */
export type DataTextureDataFormatCell = {
    key: string;
    formatType: DataTextureOriginalDataType;
}

/** 最终存入的数据格式信息 */
export type UniformTextureFormat = {
    formats: UniformTextureFormatCell[];
    float32length: number;
}

export type UniformTextureFormatCell = {
    key: string;
    rawDataFormatCell: DataTextureDataFormatCell;
    formatType: DataTextureDataFormatType;
    num: number;
}