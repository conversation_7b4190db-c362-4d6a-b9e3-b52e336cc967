// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import GameManager from "../GameManager";
import GameUtils, { ObjectPool } from "./GameUtils";
import LocalUtils, { StringDictionary, TweenObject } from "../LocalUtils";
import SmithyUtils from "../Smithy/SmithyUtils";
import AntiExtrusionManager from "./AntiExtrusionManager";
import Building from "../GameStuffAndComps/Building";
import Enemy from "../GameStuffAndComps/Enemy";
import GameDirector from "./GameDirector";
import GameStuffManager from "./GameStuffManager";
import Hero from "../GameStuffAndComps/Hero";
import NPC from "../GameStuffAndComps/NPC";
import SkillEff from "../GameStuffAndComps/SkillEff";
import StatueUnlockManager from "./StatueUnlockManager";
import Stuff from "../GameStuffAndComps/Stuff";
import Unit from "../GameStuffAndComps/Unit";
import UnlockProgressUI from "./UnlockProgressUI";
import FitFollowNode from "../Utils/FitFollowNode";

const {ccclass, property} = cc._decorator;

@ccclass
export default class GameWorldUI extends cc.Component {

    @property(cc.Node)
    MiddleNode: cc.Node = null;

    @property(cc.Node)
    effParent: cc.Node = null;
    @property(cc.Node)
    effParent2: cc.Node = null;
    @property(cc.Node)
    backEffParent: cc.Node = null;

    @property(cc.Prefab)
    hit_Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    hit_arrow_Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    hit_fire_1_Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    hit_fire_2_Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    hit_ice_1_Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    hit_ice_2_Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    eff_attack_Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    eff_attack_2_Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    eff_smoke_Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    eff_die_Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    eff_level_up_Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    eff_tp_Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    eff_coin_unclock_Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    eff_stone_fly_Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    eff_collect_Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    eff_absorb_Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    eff_lasers_line_Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    eff_skill_circle: cc.Prefab = null;

    @property(cc.Prefab)
    eff_dust_smoke: cc.Prefab = null;
    @property([cc.Prefab])
    vfxBlockExplodes: cc.Prefab[] = [];
    @property(cc.Prefab)
    vfxGroudBroke: cc.Prefab = null;
    
    @property(cc.Prefab)
    eff_fire_arrow_Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    eff_frost_storm_Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    eff_thunder_Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    eff_tomado_Prefab: cc.Prefab = null;
    // @property(cc.Prefab)
    // eff_meteorite_Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    eff_fire_rain_Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    skillEff_StormWind: cc.Prefab = null;
    @property(cc.Prefab)
    skillEff_SkillSoulBubble: cc.Prefab = null;
    @property(cc.Prefab)
    skillEff_RoseOutbreaking: cc.Prefab = null;
    @property(cc.Prefab)
    skillEff_BunSmashing: cc.Prefab = null;
    @property(cc.Prefab)
    skillEff_SelfClone_Prefab: cc.Prefab = null;
    @property(cc.Prefab)
    skillEff_DiffusionFlame: cc.Prefab = null;

    @property(cc.Prefab)
    skillEff_GroupLightning: cc.Prefab = null;
    @property(cc.Prefab)
    skillEff_FlashRush: cc.Prefab = null;
    @property(cc.Prefab)
    skillEff_EnergyStrikes: cc.Prefab = null;
    @property(cc.Prefab)
    skillEff_EnergyBall: cc.Prefab = null;
    @property(cc.Prefab)
    skillEff_IceFountain: cc.Prefab = null;

    @property(cc.Prefab)
    skillEff_SpiritBombing: cc.Prefab = null;
    @property(cc.Prefab)
    skillEff_MusicWave: cc.Prefab = null;
    @property(cc.Prefab)
    skillEff_ChristmasTree: cc.Prefab = null;
    @property(cc.Prefab)
    skillEff_MagicCircle: cc.Prefab = null;
    @property(cc.Prefab)
    skillEff_LeapSlash: cc.Prefab = null;
    

    @property([cc.Node])
    floorUINodes: cc.Node[] = [];

    @property(cc.Node)
    heroHPBar: cc.Node = null;
    @property(cc.Sprite)
    img_heroHPBar: cc.Sprite = null;

    @property(cc.Node)
    SkillCDBar: cc.Node = null;
    @property(cc.Sprite)
    img_skillCDBar: cc.Sprite = null;

    @property(cc.Node)
    npcHPBar: cc.Node = null;
    @property(cc.Node)
    stockadeHPBar: cc.Node = null;
    @property(cc.Node)
    hpBar_enemy: cc.Node = null;

    @property(cc.Node)
    npcBuyProgressBar: cc.Node = null;

    @property(cc.Node)
    bossHPBar: cc.Node = null;
    @property(cc.Sprite)
    img_bossHPBar: cc.Sprite = null;
    
    @property([cc.Node])
    resourcesNeedBubbles: cc.Node[] = [];
    @property([cc.Node])
    resourcesPushGuideTexts: cc.Node[] = [];

    @property(cc.Node)
    guideTextUI: cc.Node = null;

    @property(cc.Node)
    arrowBubble: cc.Node = null;
    // @property(cc.Node)
    // demandBubble_archer: cc.Node = null;
    // @property(cc.Node)
    // demandBubble_bed: cc.Node = null;
    @property(cc.Node)
    demandBubble_food: cc.Node = null;
    @property(cc.Node)
    demandBubble_meat: cc.Node = null;

    @property(cc.Node)
    eff_lasers_1: cc.Node = null;
    @property(cc.Node)
    eff_lasers_2: cc.Node = null;

    @property([cc.Node])
    damageTextNodes: cc.Node[] = [];

    @property(cc.Node)
    floatText_max: cc.Node = null;


    @property(FitFollowNode)
    whiteLight: FitFollowNode = null;

    @property(cc.Graphics)
    gameWorldGraphics_1: cc.Graphics = null;
    @property(cc.Graphics)
    gameWorldGraphics_2: cc.Graphics = null;


    levelUpEffNodes: cc.Node[] = [];
    levelUpEffTargets: Stuff[] = [];
    levelUpEffPosOffsets: cc.Vec2[] = [];

    mainHeroHPBar: cc.Node = null;
    img_mainHeroHPBar: cc.Sprite = null;
    mainHeroSkillCDBar: cc.Node = null;
    img_mainHeroSkillCDBar: cc.Sprite = null;

    heroHPBars: cc.Node[] = [];
    img_heroHPBars: cc.Sprite[] = [];
    heroSkillCDBars: cc.Node[] = [];
    img_heroSkillCDBars: cc.Sprite[] = [];

    npcScripts: NPC[] = [];
    npcHPBars: cc.Node[] = [];
    npcScriptsForBuyProgressBar: NPC[] = [];
    npcBuyProgressBars: cc.Node[] = [];

    stockadeScripts: Building[] = [];
    stockadeHPBars: cc.Node[] = [];

    enemyScripts: Enemy[] = [];
    enemyHPBars: cc.Node[] = [];

    arrowBubbleList: cc.Node[] = [];
    arrowBubbleNpcList: NPC[] = [];
    towerDemandBubbleList: cc.Node[] = [];
    towerDemandBubbleTowerIndexList: number[] = [];
    npcDemandBubbleList: cc.Node[] = [];
    npcDemandBubbleNpcScriptList: NPC[] = [];

    floatTextMaxFloowingStuffs: Stuff[] = [];

    hpBarUIHeight = 220;
    stockadeHpBarUIHeight = 100;
    enemyHpBarUIHeight = 250;

    private _isAllowBoss = false;

    private _isPlayingCollect = false;
    private _playCollectTime = 0;

    private _refreshGuideTextUICallbackId = -1;

    effectPools: ObjectPool<EffectInfo>[] = [];
    effectNodePools: cc.NodePool[] = [];
    effectPoolNameIndex: StringDictionary<number>[] = [];

    // LIFE-CYCLE CALLBACKS:

    onLoad () {
        GameUtils.rootGameWorldUI = this;
        StatueUnlockManager.instance.floorUnlockAreaUINodeList = this.floorUINodes;
        AntiExtrusionManager.instance.gameWorldGraphics_1 = this.gameWorldGraphics_1;
        AntiExtrusionManager.instance.gameWorldGraphics_2 = this.gameWorldGraphics_2;
        this.InitEffectPools();
    }
    start () {
        GameManager.instance.LateFrameCall(()=>{
            let mainHero = GameUtils.mainHero.script;
            if(mainHero) {
                this.whiteLight.target = (mainHero as Hero).singlePrefabCreator.runningNode.getChildByName('view');
            }
        });
        GameManager.instance.AddGameUpdate('GameWorldUI', (dt: number)=>{
            if(this._isPlayingCollect) {
                this._playCollectTime += dt;
            }
            if(this._playCollectTime > 0.25) {
                this._playCollectTime = 0;
                this._isPlayingCollect = false;
            }
        });
        // let pos = cc.v2(660, -740);
        // GameManager.instance.LateTimeCall((dt: number)=>{
        //     // let srcPos = cc.v2(pos.x + Math.random()* 400 - 200, pos.y + Math.random() * 300 - 150);
        //     let randomDir = LocalUtils.AngleToVec2(Math.random() * 360 - 180, cc.v2(0, 1));
        //     let srcPos = pos.add(randomDir.normalize().mul(600).scale(cc.v2(1, 0.8)));
        //     this.PlayAbsorb(pos, srcPos);
        // }, 1);
    }
    update (dt: number) {
        // this.RefreshStatueUnlockUI();
        this.RefreshNPCHPBars();
        this.RefreshStockadeHPBars(dt);
        this.RefreshEnemyHPBars(dt);
        // this.RefreshNpcBuyProgressBars();

        // 《忙里偷闲》
        // this.RefreshArrowBubbleList();
        // this.RefreshTowerDemandBubbleList();
        // this.RefreshNpcDemandBubbleList();


        this.RefreshNpcDemandBubbleList();


        // 英雄的血条 ui
        // if(!this.mainHeroHPBar) {
        //     this.GenerateNewHeroHPUI(0);
        // }
        // if(!this.mainHeroSkillCDBar) {
        //     this.GenerateNewSkillCDBarUI(0);
        // }
        // if(GameUtils.mainHero) {
        //     let mainHero = GameUtils.mainHero.script;
        //     if(mainHero && mainHero.isValid && !GameDirector.instance.isHeroDeadGameOver) {
        //         this.RefreshHeroHPUI(0, mainHero.attribute.nowHp / mainHero.attribute.maxHp);

        //         if((mainHero as Hero).skillTime < 0.01) {
        //             this.img_mainHeroSkillCDBar.node.active = false;
        //         } else {
        //             this.img_mainHeroSkillCDBar.node.active = true;
        //         }
        //         this.RefreshSkillCDBarUI(0, (mainHero as Hero).skillTime / (mainHero as Hero).skillCD);
        //     } else {
        //         this.mainHeroHPBar.active = false;
        //         this.mainHeroSkillCDBar.active = false;
        //     }
        // } else {
        //     this.mainHeroHPBar.active = false;
        //     this.mainHeroSkillCDBar.active = false;
        // }

        if(GameUtils.mainBoss && GameUtils.mainBoss.script.isAlive) {
            this.ShowBossHPUI();
            let mainBoss = GameUtils.mainBoss.script;
            if(mainBoss && mainBoss.isValid && mainBoss.attribute) {
                this.RefreshBossHPUI(mainBoss.attribute.nowHp / mainBoss.attribute.maxHp);
            }
        } else {
            this.HideBossHPUI();
        }
        
        GameUtils.heroList.forEach((e, index)=>{
            let hero = e.script as Hero;
            if(index > 0 && this.heroHPBars[index] && hero.attribute) {
                this.RefreshHeroHPUI(index, hero.attribute.nowHp / hero.attribute.maxHp);
            }
            if(index > 0 && this.heroSkillCDBars[index]) {
                // let progress = StatueUnlockManager.instance.floorUnlockAreaProgressList[hero.standTowerId - 1];
                // let maxProgress = StatueUnlockManager.instance.floorUnlockAreaMaxProgressList[hero.standTowerId - 1];
                // let viewProgress = progress / maxProgress;
                // if(viewProgress < 0.01) {
                //     this.img_heroSkillCDBars[index].node.active = false;
                // } else {
                //     this.img_heroSkillCDBars[index].node.active = true;
                // }
                // console.log(`${hero.standTowerId}, ${progress}, ${maxProgress}`);
                
                this.RefreshSkillCDBarUI(index, hero.skillTime / hero.skillCD);
                // this.RefreshSkillCDBarUI(index, viewProgress);
            }
        })

        this.levelUpEffNodes.forEach((e, index)=>{
            if(GameUtils.mainHero && GameUtils.mainHero.script && GameUtils.mainHero.script.isValid) {
                // let pos = GameUtils.mainHero.script.rootPosition;
                let pos = this.levelUpEffTargets[index].rootPosition.add(this.levelUpEffPosOffsets[index]);
                e.setPosition(pos);
            }
        });
    }

    InitEffectPools() {
        for(let i = 0; i < 30; i++) {
            this.effectPools[i] = new ObjectPool<EffectInfo>();
            this.effectNodePools[i] = new cc.NodePool();
            this.effectPools[i].SetOnPutCallback((info: EffectInfo)=>{
                info.node.active = false;
                this.effectNodePools[i].put(info.node);
            });
            this.effectPools[i].SetOnTakeCallback((info: EffectInfo)=>{
                info.node.active = true;
                let node = this.effectNodePools[i].get();
                let parentNode = info.generateParent;
                node.parent = parentNode;
                info.node = node;
            });
        }
        let indexPin = 0;
        this.effectPoolNameIndex['hit'] = indexPin++;
        this.effectPoolNameIndex['hit_arrow'] = indexPin++;
        this.effectPoolNameIndex['hit_fire_1'] = indexPin++;
        this.effectPoolNameIndex['hit_fire_2'] = indexPin++;
        this.effectPoolNameIndex['hit_ice_1'] = indexPin++;
        this.effectPoolNameIndex['hit_ice_2'] = indexPin++;

        this.effectPoolNameIndex['fire_arrow'] = indexPin++;

    }

    GenerateNewHeroHPUI(heroIndex: number) {
        if(heroIndex == 0) {
            this.mainHeroHPBar = LocalUtils.CloneNode(this.heroHPBar);
            this.mainHeroHPBar.active = true;
            this.img_mainHeroHPBar = this.mainHeroHPBar.getChildByName('bar').getComponent(cc.Sprite);
        } else {
            let node = LocalUtils.CloneNode(this.heroHPBar);
            this.heroHPBars[heroIndex] = node;
            node.active = true;
            this.img_heroHPBars[heroIndex] = node.getChildByName('bar').getComponent(cc.Sprite);
        }
    }

    RefreshHeroHPUI(heroIndex: number, progress: number) {
        if(heroIndex == 0) {
            let uiHeightAdd = GameUtils.mainHero.script.height;
            this.mainHeroHPBar.setPosition(GameUtils.mainHero.script.rootPosition.add(cc.v2(0, this.hpBarUIHeight + uiHeightAdd)));
            this.img_mainHeroHPBar.fillRange = progress;
        } else {
            let uiHeightAdd = 150;
            // let uiHeightAdd = 0;
            // if((GameUtils.heroList[heroIndex].script as Hero).heroRef == 5) {
            //     uiHeightAdd = 320;
            // }
            let hero = GameUtils.heroList[heroIndex].script as Hero;
            this.heroHPBars[heroIndex].setPosition(hero.rootPosition.add(cc.v2(0, this.hpBarUIHeight + (hero.standTowerId < 0 ? 0 : uiHeightAdd))));
            this.img_heroHPBars[heroIndex].fillRange = progress;
        }
    }

    GenerateNewSkillCDBarUI(heroIndex: number) {
        if(heroIndex == 0) {
            this.mainHeroSkillCDBar = LocalUtils.CloneNode(this.SkillCDBar);
            this.mainHeroSkillCDBar.active = true;
            this.img_mainHeroSkillCDBar = this.mainHeroSkillCDBar.getChildByName('bar').getComponent(cc.Sprite);
        } else {
            let node = LocalUtils.CloneNode(this.SkillCDBar);
            node.active = true;
            this.heroSkillCDBars[heroIndex] = node;
            this.img_heroSkillCDBars[heroIndex] = node.getChildByName('bar').getComponent(cc.Sprite);
        }
    }

    RefreshSkillCDBarUI(heroIndex: number, progress: number) {
        if(heroIndex == 0) {
            let uiHeightAdd = GameUtils.mainHero.script.height;
            this.mainHeroSkillCDBar.setPosition(GameUtils.mainHero.script.rootPosition.add(cc.v2(0, this.hpBarUIHeight + uiHeightAdd)));
            this.img_mainHeroSkillCDBar.fillRange = progress;
        } else {
            let uiHeightAdd = 150;
            // let uiHeightAdd = 0;
            // if((GameUtils.heroList[heroIndex].script as Hero).heroRef == 5) {
            //     uiHeightAdd = 320;
            // }
            let hero = GameUtils.heroList[heroIndex].script as Hero;
            this.heroSkillCDBars[heroIndex].setPosition(hero.rootPosition.add(cc.v2(0, this.hpBarUIHeight + (hero.standTowerId < 0 ? 0 : uiHeightAdd))));
            this.img_heroSkillCDBars[heroIndex].fillRange = progress;
        }
    }

    GenerateNewNPCHPBar(NPCScript: NPC) {
        let node = LocalUtils.CloneNode(this.npcHPBar);
        node.active = false;
        node.setPosition(NPCScript.rootPosition.add(cc.v2(0, 200)));
        let index = this.npcScripts.push(NPCScript) - 1;

        let bar = node.getChildByName('bar');
        let img_bar = bar.getComponent(cc.Sprite);
        img_bar.fillRange = 1;

        this.npcHPBars[index] = node;
    }

    RefreshNPCHPBars() {
        this.npcHPBars.forEach((e, index)=>{
            let bar = e.getChildByName('bar');
            let img_bar = bar.getComponent(cc.Sprite);
            let NPCScript = this.npcScripts[index];
            if(NPCScript.isAlive) {
                e.setPosition(NPCScript.rootPosition.add(cc.v2(0, 200)));
                let nowHp = NPCScript.attribute.nowHp;
                let maxHp = NPCScript.attribute.maxHp;
                img_bar.fillRange = nowHp / maxHp;
                if(nowHp == maxHp) {
                    e.active = false;
                } else {
                    e.active = true;
                }
            } else {
                e.active = false;
            }
        });
    }

    GenerateNewStockadeHPBar(stockadeScript: Building) {
        let node = LocalUtils.CloneNode(this.stockadeHPBar);
        node.active = false;
        node.setPosition(stockadeScript.rootPosition.add(cc.v2(0, this.stockadeHpBarUIHeight)));
        let index = this.stockadeScripts.push(stockadeScript) - 1;

        let bar = node.getChildByName('bar');
        let img_bar = bar.getComponent(cc.Sprite);
        img_bar.fillRange = 1;

        this.stockadeHPBars[index] = node;
    }

    RefreshStockadeHPBars(dt: number) {
        this.stockadeHPBars.forEach((e, index)=>{
            let bar = e.getChildByName('bar');
            let img_bar = bar.getComponent(cc.Sprite);
            let stockadeScript = this.stockadeScripts[index];
            if(stockadeScript.isJustGetHurt) {
                stockadeScript.getHurtTime -= dt;
                if(stockadeScript.getHurtTime <= 0) {
                    stockadeScript.isJustGetHurt = false;
                }
            }
            e.setPosition(stockadeScript.rootPosition.add(cc.v2(0, this.stockadeHpBarUIHeight)));
            if(stockadeScript.isStockadeUnlock && stockadeScript.isJustGetHurt) {
                let nowHp = stockadeScript.nowHP;
                let maxHp = stockadeScript.maxHP;
                img_bar.fillRange = nowHp / maxHp;
                if(nowHp == maxHp) {
                    e.active = false;
                } else {
                    e.active = true;
                }
            } else {
                e.active = false;
            }
        });
    }

    GenerateNewEnemyHPBar(enemyScript: Enemy) {
        let node = LocalUtils.CloneNode(this.hpBar_enemy);
        node.active = false;
        node.setPosition(enemyScript.rootPosition.add(cc.v2(0, this.enemyHpBarUIHeight)));
        let index = this.enemyScripts.push(enemyScript) - 1;

        let bar = node.getChildByName('bar');
        let img_bar = bar.getComponent(cc.Sprite);
        img_bar.fillRange = 1;

        this.enemyHPBars[index] = node;
    }

    RefreshEnemyHPBars(dt: number) {
        this.enemyHPBars.forEach((e, index)=>{
            let bar = e.getChildByName('bar');
            let img_bar = bar.getComponent(cc.Sprite);
            let enemyScript = this.enemyScripts[index];
            e.setPosition(enemyScript.rootPosition.add(cc.v2(0, this.enemyHpBarUIHeight)));
            if(enemyScript.isAlive) {
                let nowHp = enemyScript.attribute.nowHp;
                let maxHp = enemyScript.attribute.maxHp;
                img_bar.fillRange = nowHp / maxHp;
                if(nowHp == maxHp) {
                    e.active = false;
                } else {
                    e.active = true;
                }
            } else {
                e.active = false;
            }
        });
    }

    ShowBossHPUI() {
        this.bossHPBar.active = true;
    }

    HideBossHPUI() {
        this.bossHPBar.active = false;
    }

    RefreshBossHPUI(progress: number) {
        this.img_bossHPBar.fillRange = progress;
        let heights = [320, 380, 500];
        let height = 380;
        let bossEnemyRef = (GameUtils.mainBoss.script as Enemy).enemyRef;
        if(bossEnemyRef == 4) {
            height = heights[1];
        } else if(bossEnemyRef == 6) {
            height = heights[0];
        } else if(bossEnemyRef == 7) {
            height = heights[2];
        }
        this.bossHPBar.setPosition(GameUtils.mainBoss.script.rootPosition.add(cc.v2(0, height)));
    }

    // GenerateNewNpcBuyProgressBar(npcScript: NPC) {
    //     let node = LocalUtils.CloneNode(this.npcBuyProgressBar);
    //     node.active = false;
    //     node.setPosition(npcScript.rootPosition.add(cc.v2(0, 280)));
    //     let index = this.npcScriptsForBuyProgressBar.push(npcScript) - 1;

    //     let progressNode = node.getChildByName('progress');
    //     let lab_num = progressNode.getChildByName('lab_num').getComponent(cc.Label);
    //     let bar = progressNode.getChildByName('bar');
    //     // let icon = progressNode.getChildByName('icon');
    //     // icon.getChildByName('icon_1').active = true;
    //     // icon.getChildByName('icon_2').active = false;
    //     let img_bar = bar.getComponent(cc.Sprite);
    //     lab_num.string = '' + npcScript.buyerBuyMaxProgress;
    //     img_bar.fillRange = 0;

    //     this.npcBuyProgressBars[index] = node;
    // }

    // RefreshNpcBuyProgressBars() {
    //     this.npcScriptsForBuyProgressBar.forEach((e, index)=>{
    //         let barNode = this.npcBuyProgressBars[index];
    //         if(e && e.isValid) {
    //             if(!e.isBuyerBuyComplite) {
    //                 barNode.parent.insertChild(barNode, 0);
    //                 if(!e.isBuyerBuyed) {
    //                     barNode.parent.insertChild(barNode, 0);
    //                 }
    //             }
    //             if(e.isBuyerBuy && !e.isBuyerBuyed) {
    //                 barNode.active = true;
    //                 barNode.setPosition(e.rootPosition.add(cc.v2(0, 280)));
    //                 if(!e.isBuyerUIShowed) {
    //                     // console.log('动画 show！');
    //                     e.isBuyerUIShowed = true;
    //                     barNode.scale = 0;
    //                     cc.tween(barNode).to(0.3, {scale: 1}).start();
    //                 }
    //                 if(!e.isBuyerBuyUITweenOver) {
    //                     // console.log('动画 play!');
    //                     e.isBuyerBuyUITweenOver = true;
    //                     if(e.buyerBuyOverProgress != 0) {
    //                         LocalUtils.PlaySound('give');
    //                     }
    //                     let progressNode = barNode.getChildByName('progress');
    //                     let mask = progressNode.getChildByName('mask');
    //                     let icon = mask.getChildByName('icon');
    //                     let bar = progressNode.getChildByName('bar');
    //                     let img_bar = bar.getComponent(cc.Sprite);
    //                     cc.tween(icon).to(0.12, {scale: 1.3}).to(0.08, {scale: 1}).start();
    //                     if(img_bar) {
    //                         cc.tween(new TweenObject(img_bar.fillRange, (value: number)=>{
    //                             img_bar.fillRange = value;
    //                         })).to(0.2, {value: e.buyerBuyOverProgress / e.buyerBuyMaxProgress}).start();
    //                     }
    //                 }
    //             } else if(e.isBuyerBuyed && !e.isBuyerBuyComplite) {
    //                 barNode.setPosition(e.rootPosition.add(cc.v2(0, 280)));
    //                 if(!e.isBuyerUIHided) {
    //                     // console.log('动画 hide');
    //                     e.isBuyerUIHided = true;
    //                     let progressNode = barNode.getChildByName('progress');
    //                     let finishNode = barNode.getChildByName('finish');
    //                     progressNode.active = false;
    //                     finishNode.active = true;
    //                     finishNode.scale = 0;
    //                     cc.tween(finishNode).to(0.3, {scale: 1}, {easing: 'backOut'}).delay(0.8).call(()=>{
    //                         cc.tween(barNode).to(0.3, {scale: 0}).call(()=>{
    //                             e.isBuyerBuyComplite = true;
    //                             barNode.destroy();
    //                         }).start();
    //                     }).start();
    //                 }
    //                 // barNode.active = false;
    //             }
    //         }
    //     });
    // }

    GenerateArrowBubble(npcScript: NPC) {
        let node = LocalUtils.CloneNode(this.arrowBubble);
        node.active = true;
        let index = this.arrowBubbleList.push(node) - 1;
        this.arrowBubbleNpcList[index] = npcScript;
    }

    RefreshArrowBubbleList() {
        this.arrowBubbleList.forEach((e, index)=>{
            let npcScript = this.arrowBubbleNpcList[index];
            e.active = !npcScript.isTired;
            e.setPosition(npcScript.rootPosition.add(cc.v2(0, 220 + npcScript.body.y)));
            let arrowNum = npcScript.archerArrowNum;
            let lab_num = e.getChildByName('lab_num').getComponent(cc.Label);
            lab_num.string = 'X' + arrowNum;
        });
    }

    // GenerateTowerDemandBubble(pos: cc.Vec2, towerIndex: number) {
    //     // console.log('生成塔需求气泡');
    //     let node = LocalUtils.CloneNode(this.demandBubble_archer);
    //     node.setPosition(pos);
    //     let index = this.towerDemandBubbleList.push(node) - 1;
    //     this.towerDemandBubbleTowerIndexList[index] = towerIndex;
    // }

    RefreshTowerDemandBubbleList() {
        this.towerDemandBubbleList.forEach((e, i)=>{
            let towerIndex = this.towerDemandBubbleTowerIndexList[i];
            let tower = GameUtils.rootGameWorld.towers[towerIndex];
            // e.setPosition(tower.rootPosition.add(cc.v2(0, 0)));
            e.active = !tower.isUnitStandOn;
        });
    }

    GenerateNpcDemandBubble(npcScript: NPC) {
        // console.log('生成npc需求气泡');
        let node = LocalUtils.CloneNode(this.demandBubble_food);
        node.active = true;
        let anim = node.getComponent(cc.Animation);
        if(anim) {
            anim.play('demandBubble_appear');
        }
        node.setPosition(npcScript.rootPosition.add(cc.v2(0, 200 + npcScript.body.y)));
        let index = this.npcDemandBubbleList.push(node) - 1;
        this.npcDemandBubbleNpcScriptList[index] = npcScript;
    }

    RefreshNpcDemandBubbleList() {
        let readyToDestroyIndexs = [];
        this.npcDemandBubbleList.forEach((e, i)=>{
            let npcScript = this.npcDemandBubbleNpcScriptList[i];
            if(npcScript && npcScript.isValid) {
                e.setPosition(npcScript.rootPosition.add(cc.v2(0, 200 + npcScript.body.y)));
            } else {
                let index = this.npcDemandBubbleNpcScriptList.findIndex((e2)=>{
                    return npcScript == e2;
                });
                if(index >= 0) {
                    readyToDestroyIndexs.push(index);
                }
            }
            // e.active = npcScript.isTired && !npcScript.isOnBed;
        });
        readyToDestroyIndexs.forEach((e)=>{
            this.DeleteNpcDemandBubble(e, true);
        });
    }

    CloseNpcDemandBubble(unit_id: number) {
        let index = this.npcDemandBubbleNpcScriptList.findIndex((e)=>{
            return e.unit_id == unit_id;
        });
        if(index >= 0) {
            let node = this.npcDemandBubbleList[index];
            // this.npcDemandBubbleList[index].destroy();
            let anim = node.getComponent(cc.Animation);
            if(anim) {
                anim.play('demandBubble_disappear');
                LocalUtils.PlaySound('buy_3');
            }
            this.DeleteNpcDemandBubble(index);
            GameManager.instance.scheduleOnce(()=>{
                node.destroy();
            }, 5);
        }
    }

    DeleteNpcDemandBubble(index: number, isDestroy: boolean = false) {
        if(isDestroy) {
            this.npcDemandBubbleList[index].destroy();
        }
        this.npcDemandBubbleList.splice(index, 1);
        this.npcDemandBubbleNpcScriptList.splice(index, 1);
    }

    ShowGuideTextUI(textIndex: number = 0) {
        let node = this.guideTextUI;
        let center = node.getChildByName('center');
        let textNode: cc.Node = null;
        let img_texts: cc.Node[] = [];
        img_texts[0] = center.getChildByName('node').getChildByName('img_text_1');
        img_texts[1] = center.getChildByName('node').getChildByName('img_text_2');
        img_texts[2] = center.getChildByName('node').getChildByName('img_text_3');
        img_texts[3] = center.getChildByName('node').getChildByName('img_text_4');
        img_texts[4] = center.getChildByName('node').getChildByName('img_text_5');
        if(textIndex >= 0 && textIndex <= 4) {
            textNode = img_texts[textIndex];
        } else {
            node.active = false;
        }
        if(textNode && !textNode.active) {
            let anim = center.getComponent(cc.Animation);
            let di = center.getChildByName('di');
            let fitFollowNode = di.getComponent(FitFollowNode);
            node.active = true;
            img_texts.forEach((e)=>{ e.active = false; });
            textNode.active = true;
            fitFollowNode.target = textNode;
            let isPlayed = false;
            anim.play('guideTextUI_op');
            anim.off('finished');
            anim.on('finished', ()=>{
                console.log(`finished! index: ${textIndex}`);
                if(!isPlayed) {
                    isPlayed = true;
                    anim.play('guideTextUI');
                }
            }, this);
        }
        if(this._refreshGuideTextUICallbackId < 0) {
            this._refreshGuideTextUICallbackId = GameManager.instance.AddGameUpdate('#RefreshGuideTextUI', (dt: number)=>{
                if(node && node.active && GameUtils.mainHero) {
                    let uiHeightAdd = GameUtils.mainHero.script.height;
                    node.position = cc.v3(GameUtils.mainHero.script.rootPosition.add(cc.v2(0, 170 + uiHeightAdd)));
                }
            });
        }
    }

    PlayEffAttack(followUnit: Unit, attackRef = 1, isLeft = false) {
        let effPrefab = attackRef == 2 ? this.eff_attack_2_Prefab : this.eff_attack_Prefab;
        let effNode = LocalUtils.GenerateNode(this.effParent, effPrefab, cc.v3(followUnit.centerPosition));
        if(isLeft) {
            effNode.scaleX = -effNode.scaleX;
        }
        let callbackId = GameManager.instance.AddGameUpdate('eff_attack', (dt: number)=>{
            effNode.setPosition(followUnit.centerPosition);
        });
        GameManager.instance.LateTimeCallOnce(()=>{
            GameManager.instance.RemoveUpdateCallbackByID(callbackId);
            effNode.destroy();
        }, 1);
    }

    PlayEffSmoke(pos: cc.Vec2) {
        let effNode = LocalUtils.GenerateNode(this.effParent, this.eff_smoke_Prefab, cc.v3(pos));
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 2);
    }

    
    PlayEffHit(pos: cc.Vec2, bulletRef = 0, direction = cc.v2(1, 0)) {
        let effPrefab = this.hit_Prefab;
        let isRotate = false;
        let poolIndex = -1;
        let effectInfo: EffectInfo = null;
        if(bulletRef == 1 || bulletRef == 7 || bulletRef == 13) {
            isRotate = true;
            effPrefab = this.hit_arrow_Prefab;
            poolIndex = this.effectPoolNameIndex['hit_arrow'];
        } else if(bulletRef == 2) {
            effPrefab = this.hit_fire_1_Prefab;
            poolIndex = this.effectPoolNameIndex['hit_fire_1'];
        // } else if(bulletRef == 3) {
        //     effPrefab = this.hit_ice_2_Prefab;
        //     poolIndex = this.effectPoolNameIndex['hit_ice_2'];
        } else if(bulletRef == 4 || bulletRef == 15) {
            effPrefab = this.hit_ice_1_Prefab;
            poolIndex = this.effectPoolNameIndex['hit_ice_1'];
        } else if(bulletRef == 5 || bulletRef == 18) {
            effPrefab = this.hit_fire_2_Prefab;
            poolIndex = this.effectPoolNameIndex['hit_fire_2'];
        }
        let effNode = null;
        if(poolIndex >= 0) {
            if(this.effectPools[poolIndex].Size() > 0) {
                effectInfo = this.effectPools[poolIndex].Take();
                effectInfo.Reset();
                effNode = effectInfo.node;
                effNode.setPosition(pos);
            } else {
                effNode = LocalUtils.GenerateNode(this.effParent, effPrefab, cc.v3(pos));
                effectInfo = new EffectInfo(effNode);
                effectInfo.generateParent = this.effParent;
            }
        } else {
            effNode = LocalUtils.GenerateNode(this.effParent, effPrefab, cc.v3(pos));
        }
        if(isRotate) {
            effNode.children[0].angle = LocalUtils.Vec2ToAngle(direction, cc.v2(1, 0));
        }
        GameManager.instance.LateTimeCallOnce(()=>{
            if(poolIndex >= 0 && effectInfo) {
                this.effectPools[poolIndex].Put(effectInfo);
            } else {
                effNode.destroy();
            }
        }, 1);
    }

    PlayEffDie(pos: cc.Vec2, timeDelay = 0) {
        GameManager.instance.scheduleOnce(()=>{
            let effNode = LocalUtils.GenerateNode(this.effParent, this.eff_die_Prefab, cc.v3(pos));
            GameManager.instance.LateTimeCallOnce(()=>{
                effNode.destroy();
            }, 1);
        }, timeDelay);
    }

    PlayLevelUp(isMainHero = true, target?: Stuff, posOffset: cc.Vec2 = cc.v2()) {
        if(isMainHero || !target) {
            target = GameUtils.mainHero.script;
        }
        let pos = target.rootPosition;
        let effNode = LocalUtils.GenerateNode(this.effParent, this.eff_level_up_Prefab, cc.v3(pos).add(cc.v3(posOffset)));

        this.levelUpEffNodes.push(effNode);
        this.levelUpEffTargets.push(target);
        this.levelUpEffPosOffsets.push(posOffset);

        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
            let selfIndex = this.levelUpEffNodes.findIndex((e)=>{ return e == effNode; });
            if(selfIndex >= 0) {
                this.levelUpEffNodes.splice(selfIndex, 1);
                this.levelUpEffTargets.splice(selfIndex, 1);
                this.levelUpEffPosOffsets.splice(selfIndex, 1);
            }
        }, 1);
    }

    PlayTP(pos: cc.Vec2) {
        let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.eff_tp_Prefab, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        if(skillEff) { skillEff.rootPosition = pos; }
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 1.5);
    }

    PlayCoinUnlock(srcPos: cc.Vec2, trgPos: cc.Vec2) {
        let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.eff_coin_unclock_Prefab, cc.v3(srcPos));
        // let effNode = LocalUtils.GenerateNode(this.node, this.eff_coin_unclock_Prefab, cc.v3(srcPos));
        cc.tween(effNode).to(0.5, {position: cc.v3(trgPos)}).start();
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 1);
    }

    PlayStoneFly(srcPos: cc.Vec2, trgPos: cc.Vec2) {
        let effNode = LocalUtils.GenerateNode(this.effParent, this.eff_stone_fly_Prefab, cc.v3(srcPos));
        cc.tween(effNode).to(0.35, {position: cc.v3(trgPos)}).start();
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 1);
    }

    PlayCollect(target?: Unit) {
        if(this._isPlayingCollect) {
            return;
        } else {
            this._isPlayingCollect = true;
        }
        if(!target) {
            target = GameUtils.mainHero.script;
        }
        let effNode = LocalUtils.GenerateNode(this.effParent, this.eff_collect_Prefab, cc.v3(target.rootPosition.add(cc.v2(0, 50))));
        // let angle = Math.random() * 360;
        let time = 0;
        let isTimer = true;
        // effNode.angle = angle;
        GameManager.instance.AddGameUpdate('eff collect', (dt: number)=>{
            // if(isTimer) {
            //     this._playCollectTime += dt;
            //     if(this._playCollectTime > 0.25) {
            //         this._playCollectTime = 0;
            //         isTimer = false;
            //     }
            // }
            if(time <= 1) {
                time += dt;
            }
            if(time > 1) {
                this._playCollectTime = 0;
                isTimer = false;
                effNode.destroy();
                GameManager.instance.RemoveUpdateCallback();
            } else {
                effNode.setPosition(target.rootPosition.add(cc.v2(0, 50)));
            }
        });
        // GameManager.instance.LateTimeCallOnce(()=>{
        //     effNode.destroy();
        // }, 1);
    }

    PlayAbsorb(pos: cc.Vec2, srcPos: cc.Vec2, flyTime = 1) {
        let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.eff_absorb_Prefab, cc.v3(srcPos));
        let FA_eff_absorb = effNode.getChildByName('FA_eff_absorb');
        let skillEff = effNode.getComponent(SkillEff);
        if(FA_eff_absorb) {
            FA_eff_absorb.angle = Math.random() * 360 - 180;
        }
        if(skillEff) {
            skillEff.rootPosition = srcPos;
            let posSub = pos.sub(srcPos);
            cc.tween(new TweenObject(0, (value: number)=>{
                skillEff.rootPosition = srcPos.add(posSub.mul(value));
            })).delay(1).to(flyTime, {value: 1}, {easing: 'circIn'}).call(()=>{
                skillEff.rootPosition = pos;
            }).start();
        }
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 5);
    }

    PlayLaserLine(srcPos: cc.Vec2, srcHeight: number, trgPos: cc.Vec2, trgHeight: number) {
        let effNode = LocalUtils.GenerateNode(this.effParent2, this.eff_lasers_line_Prefab, cc.v3(srcPos));
        this.PlayLaserLineUpdate(effNode, srcPos, srcHeight, trgPos, trgHeight);
        return effNode;
    }

    PlayLaserLineUpdate(effNode: cc.Node, srcPos: cc.Vec2, srcHeight: number, trgPos: cc.Vec2, trgHeight: number) {
        let posSub = trgPos.add(cc.v2(0, trgHeight)).sub(srcPos.add(cc.v2(0, srcHeight)));
        let angle = LocalUtils.Vec2ToAngle(posSub.normalize(), cc.v2(1, 0));
        let viewLength = posSub.len();
        let distance = GameUtils.Fake3dDistanceExpand(srcPos, trgPos);
        let deltaHeight = srcHeight - trgHeight;
        let actualLength = Math.sqrt(distance * distance + deltaHeight * deltaHeight);
        let scale = viewLength / actualLength;
        
        let centerNode = effNode.getChildByName('centerNode');
        let group_lasers_line = centerNode.getChildByName('group_lasers_line');
        cc.Tween.stopAllByTarget(effNode);
        effNode.opacity = 255;
        centerNode.angle = angle;
        group_lasers_line.width = actualLength;
        group_lasers_line.scaleX = scale;
    }

    PlayLaserLineHide(effNode: cc.Node) {
        cc.Tween.stopAllByTarget(effNode);
        cc.tween(effNode).to(0.2, {opacity: 120}).to(0.2, {opacity: 0}).start();
    }

    PlayDustSmoke(pos: cc.Vec2) {
        let effNode = LocalUtils.GenerateNode(this.effParent, this.eff_dust_smoke, cc.v3(pos));
        effNode.scaleX = effNode.scaleX * (Math.random() > 0.5 ? 1 : -1);
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 2);
    }

    PlayVfxBlockExplode(pos: cc.Vec2, prefabIndex: number) {
        let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.vfxBlockExplodes[prefabIndex], cc.v3(pos));
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 2);
    }

    PlayVfxGroudBroke(pos: cc.Vec2) {
        let effNode = LocalUtils.GenerateNode(this.backEffParent, this.vfxGroudBroke, cc.v3(pos));
        effNode.active = true;
        GameManager.instance.LateTimeCallOnce(()=>{
            cc.tween(effNode).to(1, {opacity: 0}).start();
        }, 3.5);
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 5);
    }

    PlayDamageText(pos: cc.Vec2, viewDamage: number, prefabIndex: number) {
        let originalNode = this.damageTextNodes[prefabIndex];
        let effNode = LocalUtils.CloneNode(originalNode);
        effNode.active = true;
        effNode.position = cc.v3(pos);
        let label = effNode.getChildByName('node').getChildByName('lab').getComponent(cc.Label);
        label.string = '-' + viewDamage;
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 2);
    }

    PlayFloatTextMax(followingStuff: Stuff, posOffset: cc.Vec2) {
        let pos = followingStuff.airPosition;
        let effNode = LocalUtils.CloneNode(this.floatText_max, cc.v3(pos.add(posOffset)));
        effNode.active = true;
        let runningTime = 0;
        GameManager.instance.AddGameUpdate('#PlayFloatTextMax', (dt: number)=>{
            runningTime += dt;
            if(runningTime < 5) {
                effNode.setPosition(followingStuff.airPosition.add(posOffset));
            } else {
                effNode.destroy();
                GameManager.instance.RemoveUpdateCallback();
            }
        });
    }

    PlayEffSkillCircle(pos: cc.Vec2, scale: number) {
        let effNode = LocalUtils.GenerateNode(this.backEffParent, this.eff_skill_circle, cc.v3(pos));
        let imgNode = effNode.getChildByName('img');
        if(imgNode) {
            imgNode.scale = scale * 0.1;
            cc.tween(imgNode).to(0.2, {scale: scale}).start();
        }
        GameManager.instance.LateTimeCallOnce(()=>{
            cc.tween(effNode).to(0.6, {opacity: 0}).start();
        }, 1.2);
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 2);
    }
    
    PlaySkillFireArrow(pos: cc.Vec2) {
        let poolIndex = this.effectPoolNameIndex['fire_arrow'];
        let effectInfo = this.GenerateSkillEffNode(poolIndex, this.eff_fire_arrow_Prefab, this.MiddleNode, cc.v2());
        let effNode = effectInfo.node;
        // let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.eff_fire_arrow_Prefab, cc.v3());
        // let effNode = LocalUtils.GenerateNode(this.effParent, this.eff_fire_arrow_Prefab, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        if(skillEff) { skillEff.rootPosition = pos; }
        GameManager.instance.LateTimeCallOnce(()=>{
            this.effectPools[poolIndex].Put(effectInfo);
            // effNode.destroy();
        }, 1.2);
    }

    PlaySkillFrostStorm(pos: cc.Vec2) {
        // let effNode = LocalUtils.GenerateNode(this.effParent, this.eff_frost_storm_Prefab, cc.v3());
        let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.eff_frost_storm_Prefab, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        if(skillEff) { skillEff.rootPosition = pos; }
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 2);
    }

    PlaySkillThunder(pos: cc.Vec2) {
        // let effNode = LocalUtils.GenerateNode(this.effParent, this.eff_thunder_Prefab, cc.v3());
        let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.eff_thunder_Prefab, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        if(skillEff) { skillEff.rootPosition = pos; }
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 2);
    }

    PlaySkillTomado(pos: cc.Vec2) {
        // let effNode = LocalUtils.GenerateNode(this.effParent, this.eff_tomado_Prefab, cc.v3());
        let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.eff_tomado_Prefab, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        if(skillEff) { skillEff.rootPosition = pos; }
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 2);
    }

    // PlaySkillMeteorite(pos: cc.Vec2) {
    //     // let effNode = LocalUtils.GenerateNode(this.effParent, this.eff_meteorite_Prefab, cc.v3());
    //     let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.eff_meteorite_Prefab, cc.v3());
    //     let skillEff = effNode.getComponent(SkillEff);
    //     if(skillEff) { skillEff.rootPosition = pos; }
    //     GameManager.instance.LateTimeCallOnce(()=>{
    //         effNode.destroy();
    //     }, 2);
    // }

    PlaySkillFireRain(pos: cc.Vec2) {
        // let effNode = LocalUtils.GenerateNode(this.effParent, this.eff_fire_rain_Prefab, cc.v3());
        let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.eff_fire_rain_Prefab, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        if(skillEff) { skillEff.rootPosition = pos; }
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 2);
    }

    PlaySkillStormWind(pos: cc.Vec2) {
        let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.skillEff_StormWind, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        if(skillEff) { skillEff.rootPosition = pos; }
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 2);
    }

    PlaySkillSoulBubble(pos: cc.Vec2, isFly = false, srcPos?: cc.Vec2, flyTime = 1.25) {
        let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.skillEff_SkillSoulBubble, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        if(skillEff) {
            if(isFly && srcPos) {
                skillEff.rootPosition = srcPos;
                let posSub = pos.sub(srcPos);
                cc.tween(new TweenObject(0, (value: number)=>{
                    skillEff.rootPosition = srcPos.add(posSub.mul(value));
                })).call(()=>{
                    skillEff.rootPosition = pos;
                }).to(flyTime, {value: 1}).start();
            } else {
                skillEff.rootPosition = pos;
            }
        }
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 3);
    }

    PlaySkillRoseOutbreaking(pos: cc.Vec2) {
        let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.skillEff_RoseOutbreaking, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        if(skillEff) { skillEff.rootPosition = pos; }
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 2);
    }

    PlaySkillBunSmashing(pos: cc.Vec2, isFly = false, srcPos?: cc.Vec2, flyTime = 0.25) {
        let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.skillEff_BunSmashing, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        if(skillEff) {
            if(isFly && srcPos) {
                skillEff.rootPosition = srcPos;
                let posSub = pos.sub(srcPos);
                cc.tween(new TweenObject(0, (value: number)=>{
                    skillEff.rootPosition = srcPos.add(posSub.mul(value));
                })).call(()=>{
                    skillEff.rootPosition = pos;
                }).to(flyTime, {value: 1}).start();
            } else {
                skillEff.rootPosition = pos;
            }
        }
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 2);
    }

    PlaySkillDiffusionFlame(pos: cc.Vec2) {
        let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.skillEff_DiffusionFlame, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        if(skillEff) { skillEff.rootPosition = pos; }
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 2);
    }


    PlaySkillGroupLightning(pos: cc.Vec2) {
        let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.skillEff_GroupLightning, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        if(skillEff) { skillEff.rootPosition = pos; }
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 2);
    }

    PlaySkillFlashRush(pos: cc.Vec2) {
        let effNode = LocalUtils.GenerateNode(GameUtils.rootGameWorld.floatStuffParent, this.skillEff_FlashRush, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        if(skillEff) { skillEff.rootPosition = pos; }
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 3);
    }
    
    PlaySkillEnergyStrikes(pos: cc.Vec2) {
        let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.skillEff_EnergyStrikes, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        if(skillEff) { skillEff.rootPosition = pos; }
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 2);
    }

    PlaySkillEnergyBall(pos: cc.Vec2, isFly = false, srcPos?: cc.Vec2, flyTime = 0.25) {
        let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.skillEff_EnergyBall, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        if(skillEff) {
            if(isFly && srcPos) {
                skillEff.rootPosition = srcPos;
                let posSub = pos.sub(srcPos);
                cc.tween(new TweenObject(0, (value: number)=>{
                    skillEff.rootPosition = srcPos.add(posSub.mul(value));
                })).delay(0.2).to(flyTime, {value: 1}, {easing: 'circIn'}).call(()=>{
                    skillEff.rootPosition = pos;
                }).start();
            } else {
                skillEff.rootPosition = pos;
            }
        }
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 3);
    }

    PlaySkillIceFountain(pos: cc.Vec2) {
        let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.skillEff_IceFountain, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        if(skillEff) { skillEff.rootPosition = pos; }
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 2);
    }

    PlaySkillSpiritBombing(pos: cc.Vec2) {
        let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.skillEff_SpiritBombing, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        if(skillEff) { skillEff.rootPosition = pos; }
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 2);
    }

    PlaySkillMusicWave(pos: cc.Vec2) {
        let effNode = LocalUtils.GenerateNode(GameUtils.rootGameWorld.floatStuffParent, this.skillEff_MusicWave, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        if(skillEff) { skillEff.rootPosition = pos; }
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 3);
    }
    
    PlaySkillChristmasTree(pos: cc.Vec2) {
        let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.skillEff_ChristmasTree, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        if(skillEff) { skillEff.rootPosition = pos; }
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 2);
    }
    
    PlaySkillMagicCircle(pos: cc.Vec2) {
        let effNode = LocalUtils.GenerateNode(this.backEffParent, this.skillEff_MagicCircle, cc.v3());
        let effNode2 = LocalUtils.GenerateNode(this.MiddleNode, this.skillEff_MagicCircle, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        let skillEff2 = effNode2.getComponent(SkillEff);
        if(skillEff) { skillEff.rootPosition = pos; }
        if(skillEff2) { skillEff2.rootPosition = pos; }
        effNode.getChildByName('sp_node_1').active = true;
        effNode2.getChildByName('sp_node_2').active = true;
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
            effNode2.destroy();
        }, 2);
    }
    
    PlaySkillLeapSlash(pos: cc.Vec2) {
        let effNode = LocalUtils.GenerateNode(this.MiddleNode, this.skillEff_LeapSlash, cc.v3());
        let skillEff = effNode.getComponent(SkillEff);
        if(skillEff) { skillEff.rootPosition = pos; }
        GameManager.instance.LateTimeCallOnce(()=>{
            effNode.destroy();
        }, 2);
    }

    GenerateSkillEffNode(poolIndex: number, prefab: cc.Prefab, parent: cc.Node, pos: cc.Vec2) {
        let effectInfo: EffectInfo = null;
        let effNode: cc.Node = null;

        if(this.effectPools[poolIndex].Size() > 0) {
            effectInfo = this.effectPools[poolIndex].Take();
            effectInfo.Reset();
            effNode = effectInfo.node;
        } else {
            effNode = LocalUtils.GenerateNode(parent, prefab, cc.v3(pos));
            effectInfo = new EffectInfo(effNode);
            effectInfo.generateParent = parent;
        }

        return effectInfo;
    }

}

export class EffectInfo {
    node: cc.Node;
    skillEffScript: SkillEff;
    generateParent: cc.Node;
    
    constructor(node: cc.Node) {
        this.node = node;
    }

    Reset() {
        this.ResetNodeAnims(this.node);
    }

    ResetNodeAnims(node: cc.Node) {
        if(node) {
            let anim = node.getComponent(cc.Animation);
            if(anim) {
                anim.stop();
                anim.play();
            }
            let spine = node.getComponent(sp.Skeleton);
            if(spine) {
                spine.setAnimation(0, spine.animation, spine.loop);
            }
            for(let i = 0; i < node.children.length; i++) {
                this.ResetNodeAnims(node.children[i]);
            }
        }
    }
}
