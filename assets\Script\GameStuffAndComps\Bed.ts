import GameUtils from "../Game/GameUtils";
import GameManager from "../GameManager";
import Building from "./Building";
import Stuff from "./Stuff";

const {ccclass, property} = cc._decorator;

@ccclass
export default class Bed extends Building {

    @property(cc.Integer)
    bedRef: number = 0;
    // unitPosNode: cc.Node = null;
    mountParent: cc.Node = null;

    unitPos: cc.Vec2 = cc.v2();
    
    protected override InitOnLoad() {
        super.InitOnLoad();
        GameUtils.rootGameWorld.beds[this.bedRef - 1] = this;

        // this.unitPosNode = this.node.getChildByName('unitPosNode');
        this.mountParent = this.node.getChildByName('mountParent');
        
        // this.unitPos = cc.v2(GameUtils.MiddleNodePosition(this.unitPosNode));
        this.SetMountParentPos();

        this.gameUpdateCallbackId = GameManager.instance.AddGameUpdate('Bed', (dt: number)=>{
            this.gameUpdate(dt);
        }, false, 2050);

    }

    override Init() {
        this.SetMountParentPos();
    }
    
    protected override gameUpdate(dt: number) {
    }


    SetMountParentPos() {
        // let gameWorldPosBefore = GameUtils.MiddleNodePosition(this.mountParent);
        let parentGameWorldPos = GameUtils.MiddleNodePosition(this.mountParent.parent);
        let parentGameWorldScale = GameUtils.MiddleNodeScale(this.mountParent.parent);
        this.mountParent.setPosition(cc.v2(-parentGameWorldPos.x, -parentGameWorldPos.y).scale(cc.v2(1 / parentGameWorldScale.x, 1 / parentGameWorldScale.y)));
        
        // let gameWorldPos = GameUtils.MiddleNodePosition(this.mountParent);
        // let posOffset = gameWorldPos.sub(gameWorldPosBefore);
        // this.mountParent.children.forEach((e, i)=>{
        //     let stuffScript = e.getComponent(Stuff);
        //     if(stuffScript) {
        //         stuffScript.rootPosition = stuffScript.rootPosition.add(posOffset.mul(-1));
        //     }
        // });
    }

    protected override SetPos(pos: cc.Vec2): cc.Vec2 {
        let newPos = super.SetPos(pos);
        this.SetMountParentPos();
        // this.unitPos = cc.v2(GameUtils.MiddleNodePosition(this.unitPosNode));
        return newPos;
    }
}