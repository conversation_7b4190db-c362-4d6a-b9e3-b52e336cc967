import GameUtils from "../../Game/GameUtils";
import Building from "../Building";
import Bed from "../Bed";
import FloorUnlockArea from "../FloorUnlockArea";


export enum SceneLoaderType {
    none,
    floorUnlockAreaList,
    wallNodes,
    tables,
    floorNodes,
    bed,
}

const {ccclass, property, executeInEditMode} = cc._decorator;

@ccclass
export default class SceneLoader extends cc.Component {

    @property({type: cc.Enum(SceneLoaderType)})
    sceneLoaderType: SceneLoaderType = SceneLoaderType.none;
    
    @property
    id: number = 0;

    @property({visible: function() {
        return this.sceneLoaderType == SceneLoaderType.wallNodes;
    }, type: [cc.Node]})
    wallNodes: cc.Node[] = [];

    @property({visible: function() {
        return this.sceneLoaderType == SceneLoaderType.floorUnlockAreaList;
    }, type: [FloorUnlockArea]})
    floorUnlockAreaList: FloorUnlockArea[] = [];

    @property({visible: function() {
        return this.sceneLoaderType == SceneLoaderType.tables;
    }, type: [Building]})
    tables: Building[] = [];

    @property({visible: function() {
        return this.sceneLoaderType == SceneLoaderType.floorNodes;
    }, type: [cc.Node]})
    floorNodes: cc.Node[] = [];

    @property({visible: function() {
        return this.sceneLoaderType == SceneLoaderType.bed;
    }, type: [Bed]})
    beds: Bed[] = [];

    protected onLoad(): void {
        if(this.sceneLoaderType == SceneLoaderType.wallNodes) {
            this.wallNodes.forEach((e)=>{
                let walls = GameUtils.rootGameWorld.wallNodes;
                if(!walls[this.id]) {
                    walls[this.id] = [];
                }
                walls[this.id].push(e);
            });
        } else if(this.sceneLoaderType == SceneLoaderType.floorUnlockAreaList) {
            this.tables.forEach((e)=>{
                let table = e.getComponent(Building);
                if(table) {
                    GameUtils.rootGameWorld.buildingTables.push(table);
                }
            })
        } else if(this.sceneLoaderType == SceneLoaderType.tables) {
            this.tables.forEach((e)=>{
                let table = e.getComponent(Building);
                if(table) {
                    GameUtils.rootGameWorld.buildingTables.push(table);
                }
            })
        } else if(this.sceneLoaderType == SceneLoaderType.floorNodes) {
            this.floorNodes.forEach((e)=>{
                let floors = GameUtils.rootGameWorld.floorNodes;
                if(!floors[this.id]) {
                    floors[this.id] = [];
                }
                floors[this.id].push(e);
            });
        } else if(this.sceneLoaderType == SceneLoaderType.bed) {
        }
    }
}