{"skeleton": {"hash": "wKEWPZsGuhF85yFtM5746rPHBD4", "spine": "3.8.75", "x": -120, "y": -0.34, "width": 240, "height": 300.52, "images": "./素材/", "audio": ""}, "bones": [{"name": "root"}, {"name": "ALL", "parent": "root", "x": 4.07, "y": -2.46, "scaleX": 1.1114, "scaleY": 1.1114, "color": "ff0000ff"}, {"name": "ALL2", "parent": "ALL", "x": 2.19, "y": 66.32, "color": "ff0000ff"}, {"name": "ALL3", "parent": "ALL2", "length": 18.73, "rotation": 87.34, "x": 0.14, "y": 6.38, "color": "ff0000ff"}, {"name": "ALL4", "parent": "ALL3", "length": 30.99, "rotation": 7.49, "x": 20.04, "y": -0.23, "color": "ff0000ff"}, {"name": "ALL5", "parent": "ALL4", "length": 14.68, "rotation": -13.92, "x": 37.48, "y": 7.89, "color": "ff0000ff"}, {"name": "ALL6", "parent": "ALL4", "length": 30.13, "rotation": 122.15, "x": 34.85, "y": 35.47, "color": "ff0000ff"}, {"name": "ALL7", "parent": "ALL6", "length": 24.5, "rotation": 31.57, "x": 29, "y": 1.39, "color": "ff0000ff"}, {"name": "ALL8", "parent": "ALL4", "length": 15.82, "rotation": 172.54, "x": -6.87, "y": 68.25, "color": "ff3f00ff"}, {"name": "ALL9", "parent": "ALL4", "length": 25.94, "rotation": -139.6, "x": 16.08, "y": -14.6, "color": "ff0000ff"}, {"name": "ALL10", "parent": "ALL9", "length": 12.82, "rotation": 7.04, "x": 27.58, "y": -0.21, "color": "ff0000ff"}, {"name": "ALL67", "parent": "ALL2", "length": 9.6, "rotation": -98.13, "x": -22.53, "y": 0.54, "color": "ff0000ff"}, {"name": "ALL11", "parent": "ALL67", "length": 15.08, "rotation": 9.23, "x": 9.69, "y": -0.47, "color": "ff0000ff"}, {"name": "ALL12", "parent": "ALL11", "length": 17.69, "rotation": -1.57, "x": 15.52, "y": 0.28, "color": "ff0000ff"}, {"name": "ALL13", "parent": "ALL", "length": 8.41, "rotation": -90.99, "x": -21.74, "y": 23.11, "color": "ff3f00ff"}, {"name": "ALL68", "parent": "ALL2", "length": 7.09, "rotation": -86.86, "x": 12.98, "y": 2.68, "color": "ff0000ff"}, {"name": "ALL14", "parent": "ALL68", "length": 17.4, "rotation": -2.18, "x": 7.02, "y": -0.31, "color": "ff0000ff"}, {"name": "ALL15", "parent": "ALL14", "length": 19.87, "rotation": 0.72, "x": 17.98, "y": -0.3, "color": "ff0000ff"}, {"name": "ALL16", "parent": "ALL", "length": 9.58, "rotation": -87.4, "x": 15.82, "y": 23.11, "color": "ff3f00ff"}, {"name": "ALL30", "parent": "ALL5", "x": 18.82, "y": -15.67, "color": "ff0000ff"}, {"name": "ALL31", "parent": "ALL5", "x": 50.2, "y": -11, "color": "ff0000ff"}, {"name": "ALL33", "parent": "ALL5", "x": 114.64, "y": 21.74, "color": "ff0000ff"}, {"name": "ALL34", "parent": "ALL33", "length": 15.52, "rotation": 157.69, "x": -84.65, "y": -76.25, "color": "ff0000ff"}, {"name": "ALL35", "parent": "ALL34", "length": 5.54, "rotation": 67.56, "x": 8.73, "y": -0.11, "color": "ff0000ff"}, {"name": "ALL36", "parent": "ALL35", "length": 4.16, "rotation": 15.55, "x": 5.6, "y": 0.38, "color": "ff0000ff"}, {"name": "ALL37", "parent": "ALL36", "length": 6.85, "rotation": 26.7, "x": 3.87, "y": -0.45, "color": "ff0000ff"}, {"name": "ALL69", "parent": "ALL4", "length": 9.73, "rotation": -130.58, "x": -13.93, "y": -42.3, "color": "ff3f00ff"}, {"name": "ALL58", "parent": "ALL69", "rotation": -1.98, "x": 7.98, "y": -5.2, "color": "ff0000ff"}, {"name": "ALL61", "parent": "ALL58", "length": 21.31, "rotation": -116.96, "x": -4.49, "y": -16.12, "color": "ff0000ff"}, {"name": "ALL62", "parent": "ALL61", "length": 16.83, "rotation": -13.35, "x": 21.22, "y": -0.23, "color": "ff0000ff"}, {"name": "ALL63", "parent": "ALL5", "x": 44.38, "y": -85.43, "color": "ffffffff"}, {"name": "ALL64", "parent": "ALL5", "x": 44.94, "y": -93.23, "color": "ff0000ff"}, {"name": "ALL65", "parent": "ALL2", "x": 84.4, "y": 6.38, "color": "ffffffff"}, {"name": "ALL66", "parent": "ALL2", "x": 92.42, "y": 6.61, "color": "ffef00ff"}, {"name": "ALL70", "parent": "ALL8", "rotation": 92.63, "x": -388.27, "y": 157.78, "color": "ff0000ff"}, {"name": "ALL17", "parent": "ALL", "x": 292.51, "y": 164.78, "color": "ff0000ff"}, {"name": "ALL18", "parent": "ALL17", "x": -24, "y": -56.53, "color": "3fff00ff"}, {"name": "ALL19", "parent": "ALL17", "x": 10.92, "y": -85.63, "color": "3fff00ff"}, {"name": "ALL20", "parent": "ALL17", "x": 56.99, "y": -122.98, "color": "3fff00ff"}, {"name": "ALL21", "parent": "ALL17", "x": 32.26, "y": -27.92, "color": "3fff00ff"}, {"name": "ALL22", "parent": "ALL17", "x": 59.9, "y": -52.65, "color": "3fff00ff"}, {"name": "ALL23", "parent": "ALL17", "x": 105.01, "y": -88.06, "color": "3fff00ff"}, {"name": "ALL24", "parent": "ALL17", "x": 73.48, "y": -5.12, "color": "3fff00ff"}, {"name": "ALL25", "parent": "ALL17", "x": 102.1, "y": -27.92, "color": "3fff00ff"}, {"name": "ALL26", "parent": "ALL17", "x": 152.05, "y": -54.11, "color": "3fff00ff"}, {"name": "ALL27", "parent": "ALL17", "x": -69.22, "y": -31.76, "color": "3fff00ff"}, {"name": "ALL28", "parent": "ALL17", "x": -13.45, "y": 5.1, "color": "3fff00ff"}, {"name": "ALL29", "parent": "ALL17", "x": 36.99, "y": 27.89, "color": "3fff00ff"}, {"name": "ALL38", "parent": "ALL", "x": 130.78, "y": 130.83, "scaleX": 0.218, "scaleY": 0.218, "color": "ff0000ff"}, {"name": "ALL39", "parent": "ALL", "x": 130.78, "y": 130.83, "scaleX": 0.218, "scaleY": 0.218, "color": "ff0000ff"}, {"name": "ALL40", "parent": "ALL", "x": 130.78, "y": 130.83, "scaleX": 0.218, "scaleY": 0.218, "color": "ff0000ff"}, {"name": "wuti", "parent": "ALL8", "x": 9.55, "y": 6.72, "color": "ff0000ff"}, {"name": "ALL41", "parent": "ALL8", "rotation": 119.62, "x": 8.83, "y": 5.1, "color": "ff0000ff"}, {"name": "ALL42", "parent": "ALL41", "length": 39.24, "rotation": 1.94, "x": -1.02, "y": -95.71, "color": "ff0000ff"}, {"name": "ALL43", "parent": "ALL42", "length": 11.71, "rotation": -86.62, "x": 21.11, "y": -1.06, "color": "ff0000ff"}, {"name": "ALL44", "parent": "ALL43", "length": 12.89, "rotation": -0.12, "x": 12.05, "y": -0.22, "color": "ff0000ff"}, {"name": "ALL45", "parent": "ALL44", "length": 12.38, "rotation": 1.49, "x": 12.89, "color": "ff0000ff"}, {"name": "ALL46", "parent": "ALL45", "length": 11.84, "rotation": -6.25, "x": 11.43, "y": -0.34, "color": "ff0000ff"}, {"name": "ALL47", "parent": "ALL", "x": 87.63, "y": -15.05, "color": "ff0000ff"}, {"name": "ren", "parent": "ALL2", "x": -6.26, "y": -55.98, "color": "ff0000ff"}, {"name": "ALL49", "parent": "ALL58", "rotation": 37.74, "x": 143.09, "y": 4.77, "color": "ff0000ff"}, {"name": "ALL48", "parent": "ALL", "x": 69.92, "y": -43.21, "color": "ff0000ff"}, {"name": "ALL50", "parent": "ALL", "x": 69.92, "y": -43.21, "color": "ff0000ff"}, {"name": "ALL51", "parent": "ALL", "x": 69.92, "y": -43.21, "color": "ff0000ff"}, {"name": "ALL52", "parent": "ALL8", "x": 44.04, "y": 3.88, "color": "ff0000ff"}], "slots": [{"name": "output", "bone": "root"}, {"name": "tong", "bone": "ALL47"}, {"name": "archer10", "bone": "ALL9", "attachment": "archer10"}, {"name": "barb", "bone": "ALL58"}, {"name": "archer8", "bone": "ALL69", "attachment": "archer8"}, {"name": "archer7", "bone": "ALL69", "attachment": "archer7"}, {"name": "archer5", "bone": "ALL68", "attachment": "archer5"}, {"name": "archer4", "bone": "ALL67", "attachment": "archer4"}, {"name": "archer3", "bone": "ALL2", "attachment": "archer3_1"}, {"name": "archer2", "bone": "ALL2", "attachment": "archer2_1"}, {"name": "archer1", "bone": "ALL5", "attachment": "archer1_1"}, {"name": "archer16", "bone": "ALL31", "attachment": "archer16_1"}, {"name": "archer17", "bone": "ALL30", "attachment": "archer17_4"}, {"name": "archer18", "bone": "ALL33", "attachment": "archer18_7"}, {"name": "saoba", "bone": "ALL41"}, {"name": "erduo", "bone": "ALL5", "attachment": "erduo_1"}, {"name": "shiwu", "bone": "ALL52"}, {"name": "archer19", "bone": "ALL6", "attachment": "archer19"}, {"name": "archer12", "bone": "ALL70"}, {"name": "img_v2_3b71fc62-e05e-495d-9d1b-f4ce67b9a12g", "bone": "ALL17"}, {"name": "Z", "bone": "ALL38"}, {"name": "Z2", "bone": "ALL39"}, {"name": "Z3", "bone": "ALL40"}, {"name": "Elements - Liquid 009 Splash Right noCT noRSZ_1", "bone": "ALL49"}, {"name": "tong2", "bone": "ALL47"}, {"name": "penye0001", "bone": "ALL48"}, {"name": "penye1", "bone": "ALL50"}, {"name": "penye2", "bone": "ALL51"}, {"name": "archer18_6", "bone": "ALL33", "attachment": "archer18_3"}], "ik": [{"name": "ALL8", "order": 3, "bones": ["ALL6", "ALL7"], "target": "ALL8"}, {"name": "ALL13", "order": 1, "bones": ["ALL11", "ALL12"], "target": "ALL13", "bendPositive": false}, {"name": "ALL16", "bones": ["ALL14", "ALL15"], "target": "ALL16"}, {"name": "ALL69", "order": 2, "bones": ["ALL9", "ALL10"], "target": "ALL69"}], "transform": [{"name": "ALL64", "order": 4, "bones": ["ALL63"], "target": "ALL64", "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}, {"name": "ALL66", "order": 5, "bones": ["ALL65"], "target": "ALL66", "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}], "skins": [{"name": "default", "attachments": {"shiwu": {"shiwu": {"x": 0.96, "y": 1.67, "width": 35, "height": 28}}, "Elements - Liquid 009 Splash Right noCT noRSZ_1": {"Elements - Liquid 009 Splash Right noCT noRSZ_00001": {"x": 0.5, "y": 0.5, "width": 44, "height": 26}}, "archer1": {"archer1_1": {"type": "mesh", "uvs": [0.74095, 0.0401, 0.83834, 0.10618, 0.92164, 0.20589, 1, 0.35246, 1, 0.45458, 1, 0.5543, 0.93317, 0.70567, 0.92805, 0.78977, 0.88704, 0.88828, 0.77939, 0.95916, 0.64099, 1, 0.49747, 1, 0.31209, 0.93721, 0.19908, 0.87214, 0.11947, 0.79473, 0.05408, 0.68765, 0, 0.53628, 0, 0.39812, 0, 0.29119, 0.08227, 0.14102, 0.22195, 0.04851, 0.36804, 0, 0.62049, 0, 0.37701, 0.04371, 0.24502, 0.10017, 0.12328, 0.19028, 0.06048, 0.30801, 0.04382, 0.41974, 0.05792, 0.53988, 0.11046, 0.67443, 0.17882, 0.7618, 0.24449, 0.82454, 0.33524, 0.88723, 0.50131, 0.95075, 0.61921, 0.95556, 0.7653, 0.91712, 0.83322, 0.85945, 0.86782, 0.79217, 0.87935, 0.69966, 0.9208, 0.54653, 0.94359, 0.45931, 0.94471, 0.35607, 0.87551, 0.23713, 0.80759, 0.15303, 0.71532, 0.08936, 0.5923, 0.05092, 0.37701, 0.84984, 0.26937, 0.69486, 0.20914, 0.53748, 0.20401, 0.34165, 0.26808, 0.18067, 0.41802, 0.08335, 0.52684, 0.04872], "triangles": [10, 11, 34, 11, 33, 34, 10, 35, 9, 10, 34, 35, 11, 12, 33, 33, 12, 32, 9, 36, 8, 9, 35, 36, 38, 35, 34, 32, 46, 33, 34, 33, 46, 12, 13, 32, 32, 13, 31, 37, 36, 35, 36, 37, 8, 8, 37, 7, 32, 31, 46, 14, 30, 13, 13, 30, 31, 38, 37, 35, 31, 47, 46, 34, 46, 38, 38, 46, 47, 44, 43, 51, 31, 30, 47, 15, 29, 14, 14, 29, 30, 37, 38, 7, 39, 38, 43, 48, 43, 38, 7, 38, 6, 51, 43, 48, 51, 45, 44, 51, 52, 45, 30, 29, 47, 5, 6, 39, 6, 38, 39, 39, 43, 42, 29, 48, 47, 47, 48, 38, 50, 51, 49, 15, 28, 29, 15, 16, 28, 29, 28, 48, 39, 40, 5, 40, 4, 5, 39, 42, 40, 48, 28, 27, 28, 16, 27, 27, 49, 48, 51, 48, 49, 16, 17, 27, 40, 41, 4, 40, 42, 41, 41, 3, 4, 27, 26, 49, 27, 17, 26, 17, 18, 26, 3, 41, 2, 41, 42, 2, 26, 25, 49, 49, 25, 50, 26, 18, 25, 18, 19, 25, 42, 43, 2, 43, 1, 2, 25, 24, 50, 25, 19, 24, 51, 24, 23, 51, 50, 24, 43, 44, 1, 19, 20, 24, 44, 0, 1, 24, 20, 23, 45, 22, 44, 44, 22, 0, 51, 23, 52, 45, 52, 22, 22, 52, 21, 20, 21, 23, 52, 23, 21], "vertices": [2, 5, 134.45, -12.77, 0.99, 30, 90.61, 64.87, 0.01, 2, 5, 127.13, -27.26, 0.99, 30, 83.3, 50.38, 0.01, 2, 5, 114.73, -40.63, 0.99, 30, 70.89, 37.01, 0.01, 2, 5, 95.56, -54.41, 0.99, 30, 51.72, 23.23, 0.01, 2, 5, 81.04, -56.73, 0.99, 30, 37.2, 20.9, 0.01, 2, 5, 66.86, -59, 0.99, 30, 23.02, 18.63, 0.01, 2, 5, 43.91, -53.54, 0.99, 30, 0.08, 24.1, 0.01, 2, 5, 31.84, -54.77, 0.99, 30, -11.99, 22.87, 0.01, 2, 5, 16.96, -51.54, 0.99, 30, -26.87, 26.09, 0.01, 2, 5, 4.58, -38.8, 0.99, 31, -40.35, 54.42, 0.01, 2, 5, -4.18, -21.28, 0.97, 31, -49.11, 71.95, 0.03, 2, 5, -7.24, -2.15, 0.97, 31, -52.17, 91.08, 0.03, 2, 5, -2.26, 23.99, 0.99, 31, -47.2, 117.22, 0.01, 2, 5, 4.58, 40.53, 0.99, 30, -39.25, 118.17, 0.01, 2, 5, 13.89, 52.91, 0.99, 30, -29.94, 130.54, 0.01, 2, 5, 27.72, 64.06, 0.99, 30, -16.11, 141.7, 0.01, 2, 5, 48.09, 74.71, 0.99, 30, 4.26, 152.35, 0.01, 2, 5, 67.74, 77.86, 0.99, 30, 23.9, 155.49, 0.01, 2, 5, 82.94, 80.29, 0.99, 30, 39.11, 157.93, 0.01, 2, 5, 106.05, 72.74, 0.99, 30, 62.22, 150.37, 0.01, 2, 5, 122.18, 56.22, 0.99, 30, 78.35, 133.86, 0.01, 2, 5, 132.19, 37.85, 0.99, 30, 88.36, 115.49, 0.01, 2, 5, 137.58, 4.2, 0.99, 30, 93.75, 81.84, 0.01, 2, 5, 126.17, 35.66, 0.99, 30, 82.34, 113.3, 0.01, 2, 5, 115.33, 51.97, 0.99, 30, 71.49, 129.61, 0.01, 2, 5, 99.92, 66.15, 0.99, 30, 56.09, 143.79, 0.01, 2, 5, 81.84, 71.84, 0.99, 30, 38.01, 149.48, 0.01, 2, 5, 65.6, 71.52, 0.99, 30, 21.76, 149.16, 0.01, 2, 5, 48.81, 66.91, 0.99, 30, 4.98, 144.55, 0.01, 2, 5, 30.8, 56.85, 0.99, 30, -13.03, 134.48, 0.01, 2, 5, 19.84, 45.75, 0.99, 30, -24, 123.38, 0.01, 2, 5, 12.32, 35.56, 0.99, 30, -31.52, 113.2, 0.01, 2, 5, 5.34, 22.04, 0.99, 31, -39.6, 115.27, 0.01, 2, 5, -0.15, -1.54, 0.97, 31, -45.09, 91.69, 0.03, 2, 5, 1.68, -17.37, 0.97, 31, -43.26, 75.86, 0.03, 2, 5, 10.26, -35.97, 0.99, 31, -34.67, 57.26, 0.01, 2, 5, 19.91, -43.71, 0.99, 30, -23.92, 33.93, 0.01, 2, 5, 30.21, -46.79, 0.99, 30, -13.62, 30.84, 0.01, 2, 5, 43.61, -46.23, 0.99, 30, -0.22, 31.41, 0.01, 2, 5, 66.27, -48.27, 0.99, 30, 22.44, 29.37, 0.01, 2, 5, 79.16, -49.32, 0.99, 30, 35.33, 28.32, 0.01, 2, 5, 93.86, -47.12, 0.99, 30, 50.03, 30.52, 0.01, 2, 5, 109.3, -35.19, 0.99, 30, 65.47, 42.45, 0.01, 2, 5, 119.81, -24.22, 0.99, 30, 75.98, 53.41, 0.01, 2, 5, 126.89, -10.47, 0.99, 30, 83.06, 67.16, 0.01, 2, 5, 129.74, 6.8, 0.99, 30, 85.91, 84.44, 0.01, 2, 5, 11.55, 17.32, 0.99, 31, -33.39, 110.55, 0.01, 2, 5, 31.29, 35.2, 0.99, 31, -13.65, 128.43, 0.01, 2, 5, 52.38, 46.81, 0.99, 31, 7.44, 140.04, 0.01, 2, 5, 80.12, 51.95, 0.99, 31, 35.18, 145.18, 0.01, 1, 5, 104.37, 47.07, 1, 1, 5, 121.41, 29.29, 1, 2, 5, 128.65, 15.58, 0.99, 30, 84.82, 93.21, 0.01], "hull": 23, "edges": [42, 40, 40, 38, 38, 36, 34, 36, 32, 34, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 20, 22, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 42, 44, 0, 44, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 66, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 46, 104, 104, 90, 102, 104], "width": 135, "height": 144}}, "archer2": {"archer2_1": {"type": "mesh", "uvs": [1, 1, 0.75053, 1, 0.34857, 1, 0, 1, 0, 0, 0.38161, 0, 0.73768, 0, 1, 0, 0.42015, 0.78477, 0.46787, 0.44774, 0.3761, 0.26747, 0.7487, 0.31058, 0.67344, 0.46734, 0.72851, 0.82396], "triangles": [10, 4, 5, 11, 6, 7, 9, 5, 6, 12, 9, 6, 10, 5, 9, 11, 12, 6, 8, 10, 9, 13, 12, 11, 3, 4, 10, 2, 3, 10, 8, 2, 10, 11, 0, 13, 0, 1, 13, 8, 9, 12, 12, 13, 8, 1, 8, 13, 2, 8, 1, 7, 0, 11], "vertices": [1, 2, 36.14, -42.37, 1, 2, 2, 14.94, -42.37, 0.98, 33, -77.48, -48.98, 0.02, 2, 2, -19.21, -42.37, 0.98, 33, -111.63, -48.98, 0.02, 1, 2, -48.82, -42.37, 1, 1, 2, -48.82, 11.29, 1, 2, 2, -16.4, 11.29, 0.98, 33, -108.82, 4.68, 0.02, 2, 2, 13.85, 11.29, 0.98, 33, -78.57, 4.68, 0.02, 1, 2, 36.14, 11.29, 1, 2, 2, -13.13, -30.82, 0.98, 33, -105.55, -37.43, 0.02, 2, 2, -9.07, -12.74, 0.98, 33, -101.49, -19.35, 0.02, 2, 2, -16.87, -3.06, 0.98, 33, -109.29, -9.68, 0.02, 2, 2, 14.79, -5.38, 0.98, 33, -77.63, -11.99, 0.02, 2, 2, 8.39, -13.79, 0.98, 33, -84.03, -20.4, 0.02, 2, 2, 13.07, -32.92, 0.98, 33, -79.35, -39.54, 0.02], "hull": 8, "edges": [6, 8, 0, 14, 4, 6, 4, 16, 16, 18, 18, 20, 8, 10, 20, 10, 10, 12, 12, 14, 12, 22, 22, 24, 24, 26, 0, 2, 2, 4, 26, 2], "width": 95, "height": 64}}, "archer3": {"archer3_1": {"type": "mesh", "uvs": [1, 0.45212, 1, 0.52701, 1, 0.66744, 1, 1, 0.8202, 1, 0.75638, 1, 0.61763, 1, 0.35262, 1, 0.25965, 1, 0, 1, 0, 0.36118, 0, 0.28094, 0, 0.17663, 0, 0, 0.13894, 0, 0.26243, 0, 0.41644, 0, 0.51495, 0, 1, 0, 0.10425, 0.95953, 0.03555, 0.87808, 0.05245, 0.78783, 0.07164, 0.67351, 0.05364, 0.5695, 0.02057, 0.47386, 0.04182, 0.08435, 0.69255, 0.02952, 0.83408, 0.09238, 0.92981, 0.18465, 0.9756, 0.30234, 0.99769, 0.85737, 0.9245, 0.94011, 0.6426, 0.82524, 0.63566, 0.54306, 0.61763, 0.30501, 0.58016, 0.09505, 0.24439, 0.09371, 0.26243, 0.2542, 0.2763, 0.51364, 0.27908, 0.80652, 0.8424, 0.83594, 0.86599, 0.58051, 0.84656, 0.30635, 0.78968, 0.13383], "triangles": [32, 33, 41, 33, 38, 34, 33, 39, 38, 41, 1, 2, 33, 42, 41, 1, 41, 0, 0, 41, 42, 34, 37, 35, 35, 37, 16, 16, 17, 35, 23, 24, 38, 33, 34, 42, 0, 42, 29, 38, 10, 37, 37, 34, 38, 38, 24, 10, 29, 18, 0, 37, 11, 36, 25, 36, 12, 11, 37, 10, 34, 43, 42, 42, 43, 28, 42, 28, 29, 28, 43, 27, 43, 35, 26, 43, 34, 35, 29, 28, 18, 25, 14, 36, 36, 11, 12, 37, 36, 16, 16, 36, 15, 28, 27, 18, 12, 13, 25, 43, 26, 27, 35, 17, 26, 36, 14, 15, 27, 26, 18, 25, 13, 14, 26, 17, 18, 4, 31, 3, 31, 30, 3, 2, 3, 30, 31, 4, 40, 6, 32, 5, 4, 5, 40, 5, 32, 40, 6, 7, 32, 8, 39, 7, 7, 39, 32, 9, 19, 8, 8, 19, 39, 9, 20, 19, 9, 24, 20, 24, 9, 10, 20, 21, 19, 19, 21, 39, 31, 40, 30, 20, 24, 21, 30, 40, 2, 40, 41, 2, 40, 32, 41, 39, 33, 32, 21, 22, 39, 22, 38, 39, 21, 23, 22, 21, 24, 23, 22, 23, 38], "vertices": [2, 3, 31.14, -30.55, 0.09657, 4, 7.04, -31.51, 0.90343, 2, 3, 24.93, -30.84, 0.20884, 4, 0.85, -30.98, 0.79116, 2, 3, 13.28, -31.38, 0.51466, 4, -10.76, -30, 0.48534, 2, 3, -14.29, -32.66, 0.9225, 4, -38.27, -27.68, 0.0775, 2, 3, -14.96, -18.29, 0.95898, 4, -37.06, -13.35, 0.04102, 2, 3, -15.19, -13.19, 0.97854, 4, -36.63, -8.26, 0.02146, 2, 3, -15.71, -2.11, 0.99972, 4, -35.69, 2.8, 0.00028, 2, 3, -16.7, 19.07, 0.9997, 4, -33.91, 23.93, 0.0003, 2, 3, -17.04, 26.5, 0.99522, 4, -33.28, 31.34, 0.00478, 2, 3, -18.01, 47.25, 0.97948, 4, -31.53, 52.04, 0.02052, 2, 3, 34.96, 49.72, 0.29898, 4, 21.3, 47.57, 0.70102, 2, 3, 41.61, 50.02, 0.20499, 4, 27.94, 47.01, 0.79501, 2, 3, 50.26, 50.43, 0.11818, 4, 36.57, 46.28, 0.88182, 2, 3, 64.9, 51.11, 0.06215, 4, 51.17, 45.05, 0.93785, 2, 3, 65.42, 40, 0.05168, 4, 50.24, 33.97, 0.94832, 2, 3, 65.88, 30.14, 0.03046, 4, 49.41, 24.13, 0.96954, 2, 3, 66.45, 17.83, 0.00589, 4, 48.37, 11.85, 0.99411, 2, 3, 66.82, 9.96, 0.00022, 4, 47.71, 4, 0.99978, 1, 4, 44.44, -34.67, 1, 2, 3, -14.26, 39.08, 0.98512, 4, -28.89, 43.44, 0.01488, 2, 3, -7.77, 44.88, 0.95381, 4, -21.69, 48.35, 0.04619, 2, 3, -0.22, 43.88, 0.8815, 4, -14.34, 46.37, 0.1185, 2, 3, 9.33, 42.79, 0.73593, 4, -5.01, 44.04, 0.26407, 2, 3, 17.89, 44.63, 0.57336, 4, 3.71, 44.75, 0.42664, 2, 3, 25.69, 47.64, 0.4352, 4, 11.85, 46.72, 0.5648, 2, 3, 58.07, 47.44, 0.07372, 4, 43.92, 42.3, 0.92628, 1, 4, 44.07, -9.95, 1, 1, 4, 37.92, -20.8, 1, 1, 4, 29.64, -27.78, 1, 2, 3, 43.46, -28.02, 0.00631, 4, 19.6, -30.61, 0.99369, 2, 3, -2.47, -31.93, 0.81093, 4, -26.46, -28.49, 0.18907, 2, 3, -9.6, -26.4, 0.9241, 4, -32.81, -22.08, 0.0759, 3, 3, -1.13, -3.43, 0.97231, 4, -21.41, -0.41, 0.00769, 33, -88.9, -1.52, 0.02, 2, 4, 1.98, -1.83, 0.98, 33, -89.46, 21.9, 0.02, 2, 4, 21.79, -2.05, 0.99, 33, -90.9, 41.66, 0.01, 1, 4, 39.4, -0.54, 1, 2, 3, 58.04, 31.22, 0.04567, 4, 41.78, 26.22, 0.95433, 3, 3, 44.8, 29.16, 0.11892, 4, 28.38, 25.9, 0.87108, 33, -119.32, 45.88, 0.01, 3, 3, 23.35, 27.05, 0.48549, 4, 6.83, 26.61, 0.50451, 33, -118.21, 24.34, 0.01, 3, 3, -0.93, 25.7, 0.93321, 4, -17.41, 28.44, 0.05679, 33, -117.99, 0.04, 0.01, 3, 3, -1.27, -19.44, 0.86586, 4, -23.64, -16.26, 0.12414, 33, -72.92, -2.41, 0.01, 3, 3, 19.99, -20.33, 0.31362, 4, -2.67, -19.93, 0.67638, 33, -71.03, 18.79, 0.01, 3, 3, 42.65, -17.73, 0.00223, 4, 20.13, -20.3, 0.98777, 33, -72.59, 41.55, 0.01, 1, 4, 34.79, -16.97, 1], "hull": 19, "edges": [16, 18, 16, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 18, 20, 48, 20, 20, 22, 22, 24, 24, 26, 24, 50, 26, 28, 50, 28, 28, 30, 30, 32, 32, 34, 34, 36, 34, 52, 52, 54, 54, 56, 56, 58, 0, 36, 58, 0, 0, 2, 2, 4, 4, 6, 4, 60, 60, 62, 6, 8, 62, 8, 12, 64, 64, 66, 66, 68, 68, 70, 72, 74, 74, 76, 76, 78, 12, 14, 14, 16, 78, 14, 8, 10, 10, 12, 10, 80, 80, 82, 82, 84, 84, 86], "width": 80, "height": 83}}, "archer4": {"archer4": {"type": "mesh", "uvs": [1, 0.80645, 1, 1, 0, 1, 0, 0.80645, 0, 0.71852, 0, 0.61757, 0, 0.49996, 0, 0.44435, 0, 0.37926, 0, 0.22186, 0, 0.13947, 0, 0, 0.8563, 0, 1, 0, 0.89095, 0.71694, 0.85877, 0.42944, 0.45535, 0.44707, 0.85852, 0.38604, 0.86479, 0.48315, 0.47268, 0.70745, 0.6385, 0.70202, 0.4207, 0.70474, 0.19795, 0.73457, 0.85716, 0.14878, 0.85762, 0.22851, 0.87217, 0.54915, 0.88374, 0.65253], "triangles": [5, 6, 16, 6, 7, 16, 8, 16, 7, 24, 16, 9, 16, 8, 9, 9, 10, 23, 2, 19, 1, 19, 20, 1, 1, 20, 0, 20, 14, 0, 19, 2, 21, 2, 22, 21, 2, 3, 22, 3, 4, 22, 21, 22, 5, 22, 4, 5, 20, 26, 14, 0, 14, 26, 0, 26, 25, 20, 19, 16, 16, 19, 21, 21, 5, 16, 20, 25, 26, 25, 16, 18, 25, 20, 16, 0, 25, 18, 16, 15, 18, 17, 15, 16, 24, 17, 16, 18, 15, 17, 17, 13, 18, 0, 18, 13, 17, 24, 13, 9, 23, 24, 24, 23, 13, 10, 12, 23, 23, 12, 13, 10, 11, 12], "vertices": [3, 11, 47.39, 27.61, 0.00488, 13, 25.6, 22.1, 0.20387, 14, 6.69, 22.15, 0.79125, 2, 13, 39.73, 22.21, 0.01005, 14, 20.82, 22.4, 0.98995, 3, 13, 40.05, -17.78, 9e-05, 14, 21.51, -17.6, 0.99978, 12, 55.07, -18.59, 0.00013, 3, 13, 25.92, -17.9, 0.09475, 14, 7.38, -17.84, 0.8521, 12, 40.95, -18.32, 0.05315, 3, 13, 19.05, -18.12, 0.10869, 14, 0.89, -18.1, 0.62777, 12, 34.89, -17.49, 0.26354, 3, 13, 11.58, -18.14, 0.12468, 14, -6.57, -18.35, 0.37021, 12, 27.43, -17.65, 0.50511, 3, 13, 3.55, -18.08, 0.14332, 14, -14.99, -18.23, 0.07012, 12, 18.58, -17.89, 0.78655, 3, 13, -0.51, -18.12, 0.0553, 14, -19.05, -18.3, 0.02644, 12, 14.52, -17.81, 0.91826, 3, 13, -5.26, -18.16, 0.00667, 14, -23.8, -18.38, 0.00527, 12, 9.77, -17.72, 0.98806, 2, 11, 10.8, -18.02, 0.0593, 12, -1.72, -17.5, 0.9407, 2, 11, 4.85, -18.87, 0.20784, 12, -7.73, -17.39, 0.79216, 2, 11, -5.23, -20.31, 0.38978, 12, -17.91, -17.19, 0.61022, 2, 11, -10.07, 13.6, 0.96656, 13, -33.23, 15.87, 0.03344, 2, 11, -10.88, 19.29, 0.95804, 13, -33.27, 21.62, 0.04196, 3, 11, 41.54, 22.37, 0.01915, 13, 19.1, 17.68, 0.41809, 14, 0.23, 17.68, 0.56276, 4, 11, 20.95, 18.13, 0.30648, 13, -1.88, 16.22, 0.65499, 14, -20.73, 16.03, 0.0232, 12, 14.09, 16.55, 0.01533, 3, 11, 24.5, 2.34, 0.00136, 13, -0.46, 0.1, 0.64058, 12, 15.07, 0.39, 0.35806, 4, 11, 17.81, 17.67, 0.41013, 13, -5.05, 16.19, 0.55569, 14, -23.9, 15.96, 0.00968, 12, 10.92, 16.6, 0.02449, 4, 11, 24.8, 18.92, 0.20516, 13, 2.04, 16.5, 0.73472, 14, -16.81, 16.34, 0.05605, 12, 18.02, 16.72, 0.00407, 3, 11, 43.22, 5.71, 0.00016, 13, 18.54, 0.95, 0.2582, 14, -0.17, 0.94, 0.74165, 3, 11, 41.89, 12.22, 0.00651, 13, 18.09, 7.58, 0.44249, 14, -0.68, 7.56, 0.551, 3, 13, 18.36, -1.13, 0.42505, 14, -0.33, -1.15, 0.57022, 12, 33.85, -1.36, 0.00474, 3, 13, 20.61, -10.03, 0.22502, 14, 2, -10.02, 0.69246, 12, 35.85, -10.31, 0.08252, 2, 11, 0.68, 15.17, 0.8979, 13, -22.36, 15.99, 0.1021, 3, 11, 6.43, 16.01, 0.7938, 13, -16.54, 16.06, 0.2041, 12, -0.57, 16.79, 0.0021, 4, 11, 29.33, 19.69, 0.15265, 13, 6.98, 16.89, 0.64533, 14, -12.22, 16.53, 0.1991, 12, 22.2, 17.3, 0.00292, 4, 11, 36.76, 21.33, 0.0704, 13, 14.58, 17.37, 0.50532, 14, -4.64, 17.24, 0.42317, 12, 29.78, 17.91, 0.00112], "hull": 14, "edges": [2, 4, 2, 0, 0, 26, 0, 28, 22, 24, 24, 26, 32, 14, 32, 30, 14, 16, 16, 32, 12, 14, 32, 12, 34, 30, 32, 34, 36, 30, 32, 36, 38, 40, 40, 28, 42, 44, 4, 6, 6, 44, 24, 46, 34, 48, 48, 46, 20, 22, 16, 18, 18, 20, 6, 8, 8, 10, 10, 12, 50, 36, 28, 52, 52, 50], "width": 40, "height": 73}}, "archer5": {"archer5": {"type": "mesh", "uvs": [1, 0.79032, 1, 1, 0, 1, 0, 0.78105, 0, 0.45656, 0, 0.41464, 0, 0.38012, 0, 0.18731, 0, 0.0943, 0, 0, 0.89185, 0, 1, 0, 0.44807, 0.42358, 0.87245, 0.39435, 0.89185, 0.62024, 0.89185, 0.72123, 0.8749, 0.42297, 0.87412, 0.36027, 0.87451, 0.72237, 0.72061, 0.70412, 0.53341, 0.69722, 0.36692, 0.70166, 0.15992, 0.72927, 0.88741, 0.09023, 0.88302, 0.17941], "triangles": [11, 24, 23, 11, 17, 24, 11, 23, 10, 16, 11, 0, 24, 8, 23, 8, 9, 23, 23, 9, 10, 20, 1, 2, 2, 22, 21, 2, 21, 20, 20, 19, 1, 1, 19, 0, 0, 19, 18, 18, 15, 0, 2, 3, 22, 3, 4, 22, 15, 18, 14, 18, 19, 14, 0, 15, 14, 0, 14, 16, 22, 4, 21, 19, 20, 14, 21, 12, 20, 21, 4, 12, 14, 12, 16, 14, 20, 12, 4, 5, 12, 12, 5, 6, 12, 24, 17, 12, 6, 7, 17, 11, 16, 12, 13, 16, 17, 13, 12, 13, 17, 16, 24, 12, 7, 7, 8, 24], "vertices": [3, 16, 45.12, 21.31, 0.05899, 17, 27.41, 21.27, 0.18394, 18, 6.86, 21.19, 0.75706, 3, 16, 60.42, 21.05, 0.00048, 17, 42.71, 20.82, 0.00856, 18, 22.15, 20.49, 0.99096, 2, 17, 41.54, -19.16, 8e-05, 18, 20.34, -19.47, 0.99992, 2, 17, 25.56, -18.7, 0.22004, 18, 4.37, -18.74, 0.77996, 3, 16, 20.09, -18.28, 0.2339, 17, 1.89, -18, 0.74661, 18, -19.29, -17.66, 0.01948, 3, 16, 17.03, -18.23, 0.36363, 17, -1.17, -17.92, 0.63025, 18, -22.35, -17.53, 0.00612, 3, 16, 14.51, -18.19, 0.47906, 17, -3.69, -17.84, 0.51942, 18, -24.87, -17.41, 0.00152, 3, 16, 0.44, -17.95, 0.83897, 17, -17.76, -17.43, 0.0795, 15, 6.77, -18.27, 0.08154, 3, 16, -6.35, -17.84, 0.71851, 17, -24.55, -17.23, 0.01588, 15, -0.01, -17.89, 0.26561, 3, 16, -13.24, -17.72, 0.62421, 17, -31.43, -17.03, 0.00015, 15, -6.88, -17.52, 0.37564, 2, 16, -12.64, 17.94, 0.03286, 15, -4.93, 18.1, 0.96714, 2, 16, -12.57, 22.27, 0.04343, 15, -4.69, 22.42, 0.95657, 1, 17, 0, -0.02, 1, 4, 16, 16.13, 16.69, 0.72036, 17, -1.64, 17.01, 0.11254, 18, -22.24, 17.4, 0.0093, 15, 23.77, 15.75, 0.15781, 4, 16, 32.63, 17.19, 0.26597, 17, 14.87, 17.31, 0.44812, 18, -5.74, 17.43, 0.27763, 15, 40.28, 15.63, 0.00828, 4, 16, 40, 17.07, 0.11152, 17, 22.24, 17.09, 0.31257, 18, 1.63, 17.1, 0.57557, 15, 47.64, 15.22, 0.00033, 4, 16, 18.22, 16.75, 0.69553, 17, 0.46, 17.05, 0.16576, 18, -20.15, 17.41, 0.01798, 15, 25.86, 15.74, 0.12073, 4, 16, 13.64, 16.8, 0.71838, 17, -4.12, 17.15, 0.06416, 18, -24.73, 17.58, 0.00352, 15, 21.29, 15.96, 0.21393, 4, 16, 40.07, 16.37, 0.10713, 17, 22.3, 16.39, 0.31232, 18, 1.68, 16.4, 0.58027, 15, 47.69, 14.53, 0.00028, 4, 16, 38.64, 10.24, 0.07577, 17, 20.79, 10.28, 0.39592, 18, 0.07, 10.31, 0.52827, 15, 46.02, 8.45, 4e-05, 3, 16, 38.01, 2.76, 0.01066, 17, 20.07, 2.81, 0.51879, 18, -0.77, 2.85, 0.47055, 2, 17, 20.2, -3.86, 0.44773, 18, -0.75, -3.82, 0.55227, 2, 17, 21.97, -12.19, 0.35092, 18, 0.89, -12.18, 0.64908, 2, 16, -6.06, 17.66, 0.10519, 15, 1.64, 17.56, 0.89481, 2, 16, 0.45, 17.37, 0.30827, 15, 8.13, 17.03, 0.69173], "hull": 12, "edges": [2, 4, 18, 20, 20, 22, 28, 30, 2, 0, 0, 22, 30, 0, 26, 32, 32, 28, 24, 32, 24, 26, 34, 26, 24, 34, 24, 12, 10, 12, 10, 24, 8, 10, 24, 8, 36, 38, 38, 40, 40, 42, 42, 44, 4, 6, 6, 8, 44, 6, 16, 18, 12, 14, 14, 16, 20, 46, 34, 48, 48, 46], "width": 40, "height": 73}}, "img_v2_3b71fc62-e05e-495d-9d1b-f4ce67b9a12g": {"img_v2_3b71fc62-e05e-495d-9d1b-f4ce67b9a12g": {"type": "mesh", "uvs": [0.59098, 0.08682, 0.67627, 0.12492, 0.69487, 0.24819, 0.76155, 0.3356, 0.84529, 0.37146, 0.92592, 0.43646, 1, 0.52387, 1, 0.6068, 0.94918, 0.70317, 0.84994, 0.77713, 0.74915, 0.86902, 0.67317, 0.9385, 0.58478, 1, 0.45142, 1, 0.38475, 0.90713, 0.30529, 0.83372, 0.23433, 0.76817, 0.17501, 0.71281, 0.11183, 0.65386, 0.05027, 0.56996, 0, 0.50146, 0, 0.36026, 0.07772, 0.24819, 0.18937, 0.20113, 0.28861, 0.12941, 0.35994, 0.06441, 0.42041, 0, 0.5057, 0, 0.49174, 0.84885, 0.4018, 0.76368, 0.31807, 0.683, 0.23278, 0.60904, 0.13199, 0.49697, 0.07617, 0.40284, 0.21418, 0.34457, 0.2731, 0.42525, 0.31342, 0.49922, 0.37079, 0.57094, 0.45918, 0.63369, 0.55997, 0.7211, 0.65456, 0.77937, 0.55532, 0.90712, 0.7569, 0.7211, 0.63285, 0.58887, 0.53516, 0.51042, 0.44367, 0.44767, 0.3925, 0.38715, 0.35684, 0.31991, 0.31342, 0.23699, 0.41731, 0.18096, 0.47468, 0.26164, 0.5243, 0.3244, 0.60184, 0.40284, 0.69953, 0.48577, 0.79877, 0.59783, 0.88095, 0.65834, 0.91352, 0.53283, 0.82048, 0.47456, 0.73519, 0.40956, 0.65611, 0.34681, 0.59408, 0.25492, 0.54291, 0.18544, 0.48399, 0.10924], "triangles": [44, 51, 52, 44, 52, 43, 38, 45, 44, 39, 44, 43, 38, 44, 39, 46, 47, 50, 35, 47, 46, 45, 46, 50, 45, 50, 51, 45, 51, 44, 37, 46, 45, 37, 45, 38, 48, 24, 25, 48, 25, 49, 23, 24, 48, 47, 48, 49, 34, 23, 48, 35, 34, 48, 35, 48, 47, 22, 23, 34, 33, 21, 22, 33, 22, 34, 32, 33, 34, 32, 34, 35, 20, 21, 33, 19, 20, 33, 32, 19, 33, 18, 19, 32, 36, 35, 46, 37, 36, 46, 31, 32, 35, 31, 35, 36, 18, 32, 31, 30, 36, 37, 31, 36, 30, 17, 18, 31, 16, 31, 30, 17, 31, 16, 29, 37, 38, 30, 37, 29, 15, 16, 30, 15, 30, 29, 29, 38, 28, 14, 15, 29, 14, 29, 28, 28, 38, 39, 41, 28, 39, 41, 39, 40, 41, 40, 11, 13, 14, 28, 13, 28, 41, 12, 41, 11, 13, 41, 12, 42, 43, 54, 9, 42, 55, 40, 43, 42, 39, 43, 40, 10, 40, 42, 10, 42, 9, 11, 40, 10, 57, 3, 4, 57, 4, 5, 56, 57, 5, 56, 5, 6, 54, 57, 56, 54, 53, 57, 56, 6, 7, 55, 54, 56, 8, 55, 56, 7, 8, 56, 42, 54, 55, 9, 55, 8, 59, 2, 3, 58, 59, 3, 58, 3, 57, 53, 59, 58, 52, 59, 53, 43, 52, 53, 57, 53, 58, 54, 43, 53, 60, 0, 1, 60, 1, 2, 61, 0, 60, 51, 50, 61, 51, 61, 60, 59, 60, 2, 52, 60, 59, 51, 60, 52, 62, 26, 27, 62, 27, 0, 49, 25, 26, 49, 26, 62, 61, 62, 0, 50, 49, 62, 50, 62, 61, 47, 49, 50], "vertices": [3, 47, 35.17, 10.27, 0.47525, 42, -1.32, 43.28, 0.52191, 43, -29.94, 66.08, 0.00284, 3, 47, 60.97, 2.29, 0.15581, 42, 24.47, 35.31, 0.78148, 43, -4.14, 58.11, 0.06271, 4, 47, 66.59, -23.5, 0.02269, 42, 30.1, 9.52, 0.58907, 43, 1.49, 32.31, 0.38658, 44, -48.47, 58.5, 0.00166, 3, 42, 50.27, -8.78, 0.04748, 43, 21.65, 14.02, 0.76012, 44, -28.3, 40.21, 0.19239, 2, 43, 46.98, 6.52, 0.33005, 44, -2.97, 32.71, 0.66995, 2, 43, 71.37, -7.09, 0.02731, 44, 21.41, 19.1, 0.97269, 2, 44, 43.82, 0.81, 0.99802, 41, 90.86, 34.76, 0.00198, 2, 44, 43.82, -16.54, 0.97456, 41, 90.86, 17.41, 0.02544, 2, 44, 28.45, -36.71, 0.85375, 41, 75.49, -2.76, 0.14625, 3, 44, -1.57, -52.18, 0.45004, 41, 45.48, -18.23, 0.54389, 38, 93.49, 16.69, 0.00608, 3, 44, -32.05, -71.41, 0.05293, 41, 14.99, -37.46, 0.75693, 38, 63.01, -2.54, 0.19014, 3, 44, -55.03, -85.95, 0.00047, 41, -7.99, -52, 0.45038, 38, 40.03, -17.08, 0.54915, 3, 41, -34.72, -64.87, 0.10009, 38, 13.29, -29.95, 0.89907, 37, 59.37, -67.3, 0.00084, 2, 38, -27.04, -29.95, 0.7911, 37, 19.03, -67.3, 0.2089, 3, 38, -47.21, -10.52, 0.44452, 37, -1.13, -47.86, 0.54143, 36, 33.79, -76.96, 0.01406, 4, 38, -71.24, 4.85, 0.09881, 37, -25.16, -32.5, 0.7151, 36, 9.76, -61.6, 0.18446, 45, 54.98, -86.37, 0.00164, 4, 38, -92.7, 18.56, 0.00767, 37, -46.63, -18.78, 0.44911, 36, -11.71, -47.88, 0.4972, 45, 33.52, -72.66, 0.04602, 3, 37, -64.57, -7.2, 0.18734, 36, -29.65, -36.3, 0.61951, 45, 15.57, -61.07, 0.19316, 3, 37, -83.68, 5.14, 0.04558, 36, -48.76, -23.96, 0.47409, 45, -3.54, -48.74, 0.48034, 3, 37, -102.3, 22.69, 0.00241, 36, -67.38, -6.41, 0.16193, 45, -22.16, -31.18, 0.83566, 2, 36, -82.58, 7.93, 0.02541, 45, -37.36, -16.84, 0.97459, 2, 45, -37.36, 12.7, 0.9946, 46, -93.14, -24.16, 0.0054, 2, 45, -13.85, 36.15, 0.8544, 46, -69.63, -0.71, 0.1456, 3, 36, -25.31, 70.77, 0.00784, 45, 19.91, 46, 0.37877, 46, -35.86, 9.14, 0.61339, 3, 47, -56.28, 1.35, 0.13564, 45, 49.93, 61.01, 0.01925, 46, -5.84, 24.15, 0.84511, 3, 47, -34.71, 14.96, 0.5785, 46, 15.73, 37.75, 0.42107, 39, -29.98, 70.77, 0.00043, 2, 47, -16.42, 28.43, 0.87252, 46, 34.02, 51.23, 0.12748, 3, 47, 9.38, 28.43, 0.93633, 42, -27.12, 61.45, 0.05785, 46, 59.82, 51.23, 0.00582, 3, 38, -14.85, 1.68, 0.76387, 37, 31.23, -35.67, 0.21496, 40, -17.76, -68.65, 0.02118, 4, 38, -42.05, 19.5, 0.20166, 37, 4.03, -17.84, 0.77149, 36, 38.95, -46.94, 0.01305, 40, -44.96, -50.82, 0.0138, 5, 38, -67.38, 36.38, 0.00681, 37, -21.3, -0.96, 0.63305, 36, 13.62, -30.06, 0.35499, 45, 58.84, -54.83, 0.00291, 39, -42.64, -58.68, 0.00224, 3, 37, -47.1, 14.52, 0.12914, 36, -12.18, -14.58, 0.76015, 45, 33.05, -39.36, 0.1107, 4, 37, -77.58, 37.97, 0.00165, 36, -42.66, 8.87, 0.22742, 45, 2.56, -15.91, 0.76882, 46, -53.21, -52.77, 0.00211, 2, 45, -14.32, 3.79, 0.99254, 46, -70.1, -33.07, 0.00746, 4, 36, -17.8, 40.76, 0.16619, 45, 27.42, 15.99, 0.41141, 46, -28.36, -20.87, 0.41311, 39, -74.06, 12.14, 0.0093, 5, 37, -34.9, 52.97, 0.00083, 36, 0.02, 23.87, 0.51255, 45, 45.24, -0.9, 0.12941, 46, -10.53, -37.76, 0.26463, 39, -56.24, -4.74, 0.09258, 6, 37, -22.71, 37.5, 0.07068, 36, 12.21, 8.4, 0.68095, 45, 57.43, -16.38, 0.00813, 46, 1.66, -53.24, 0.08164, 39, -44.05, -20.22, 0.15518, 40, -71.69, 4.52, 0.00342, 5, 37, -5.35, 22.49, 0.44228, 36, 29.57, -6.61, 0.3064, 46, 19.01, -68.24, 0.01136, 39, -26.69, -35.23, 0.19122, 40, -54.34, -10.49, 0.04874, 6, 41, -72.71, 11.78, 0.00926, 38, -24.7, 46.7, 0.08619, 37, 21.38, 9.36, 0.49955, 36, 56.3, -19.74, 0.00658, 39, 0.04, -48.36, 0.08923, 40, -27.61, -23.62, 0.30919, 6, 43, -39.32, -66.65, 0.00075, 41, -42.23, -6.51, 0.20315, 38, 5.79, 28.41, 0.39936, 37, 51.86, -8.93, 0.10532, 39, 30.52, -66.65, 0.00012, 40, 2.88, -41.91, 0.29129, 4, 41, -13.62, -18.7, 0.63677, 38, 34.4, 16.22, 0.31752, 37, 80.47, -21.13, 0.00069, 40, 31.49, -54.11, 0.04502, 3, 41, -43.63, -45.44, 0.04605, 38, 4.38, -10.52, 0.95037, 37, 50.46, -47.86, 0.00357, 4, 43, 20.25, -66.65, 0.00513, 44, -29.71, -40.46, 0.1711, 41, 17.34, -6.51, 0.80483, 38, 65.35, 28.41, 0.01894, 7, 42, 11.34, -61.77, 0.00024, 43, -17.27, -38.98, 0.15253, 44, -67.23, -12.79, 0.00566, 41, -20.18, 21.16, 0.36156, 38, 27.83, 56.08, 0.03155, 37, 73.91, 18.74, 0.00327, 40, 24.92, -14.24, 0.4452, 5, 42, -18.21, -45.36, 0.00704, 37, 44.36, 35.15, 0.01768, 36, 79.28, 6.05, 0.00033, 39, 23.02, -22.56, 0.08722, 40, -4.63, 2.17, 0.88773, 5, 37, 16.69, 48.28, 0.0651, 36, 51.61, 19.18, 0.05019, 46, 41.06, -42.45, 0.01432, 39, -4.65, -9.43, 0.77843, 40, -32.3, 15.3, 0.09195, 8, 47, -24.86, -52.58, 0.0237, 42, -61.35, -19.56, 1e-05, 37, 1.21, 60.95, 0.02124, 36, 36.13, 31.85, 0.12916, 45, 81.35, 7.07, 0.00011, 46, 25.58, -29.79, 0.19863, 39, -20.13, 3.23, 0.62638, 40, -47.77, 27.97, 0.00077, 7, 47, -35.65, -38.51, 0.05421, 42, -72.14, -5.49, 0.00013, 37, -9.57, 75.02, 0.00133, 36, 25.35, 45.92, 0.09918, 45, 70.57, 21.14, 0.00259, 46, 14.79, -15.72, 0.51059, 39, -30.91, 17.3, 0.33197, 3, 47, -48.78, -21.16, 0.0225, 46, 1.66, 1.64, 0.97069, 39, -44.05, 34.66, 0.00681, 5, 47, -17.36, -9.43, 0.58835, 42, -53.85, 23.59, 0.00731, 36, 43.64, 75, 0.00014, 46, 33.08, 13.36, 0.2878, 39, -12.62, 46.38, 0.11641, 6, 47, 0, -26.32, 0.36896, 42, -36.5, 6.7, 0.18577, 36, 60.99, 58.11, 0.00017, 46, 50.44, -3.52, 0.07588, 39, 4.73, 29.5, 0.36842, 40, -22.92, 54.23, 0.0008, 6, 47, 15, -39.45, 0.10699, 42, -21.49, -6.43, 0.44326, 43, -50.1, 16.36, 0.00129, 46, 65.44, -16.65, 0.00535, 39, 19.74, 16.36, 0.38036, 40, -7.91, 41.1, 0.06275, 5, 42, 1.96, -22.85, 0.36574, 43, -26.65, -0.05, 0.27293, 41, -29.56, 60.09, 0.0064, 39, 43.19, -0.05, 0.05481, 40, 15.54, 24.68, 0.30012, 4, 43, 2.89, -17.4, 0.63281, 44, -47.06, 8.79, 0.09773, 41, -0.02, 42.74, 0.16073, 40, 45.09, 7.33, 0.10874, 4, 43, 32.91, -40.85, 0.08653, 44, -17.04, -14.66, 0.5686, 41, 30, 19.29, 0.343, 40, 75.11, -16.12, 0.00187, 2, 44, 7.81, -27.33, 0.76777, 41, 54.86, 6.62, 0.23223, 2, 44, 17.66, -1.06, 0.99176, 41, 64.71, 32.89, 0.00824, 3, 43, 39.48, -15.06, 0.24184, 44, -10.48, 11.13, 0.73172, 41, 36.57, 45.08, 0.02644, 5, 42, 42.3, -24.25, 0.00438, 43, 13.68, -1.46, 0.80751, 44, -36.27, 24.73, 0.16444, 41, 10.77, 58.68, 0.02264, 40, 55.88, 23.28, 0.00103, 3, 42, 18.38, -11.12, 0.4059, 43, -10.24, 11.67, 0.56594, 40, 31.96, 36.41, 0.02816, 4, 47, 36.11, -24.91, 0.08477, 42, -0.38, 8.11, 0.89719, 43, -29, 30.9, 0.00989, 39, 40.84, 30.9, 0.00816, 3, 47, 20.63, -10.37, 0.47949, 42, -15.86, 22.65, 0.47587, 39, 25.37, 45.44, 0.04464, 2, 47, 2.81, 5.58, 0.96919, 42, -33.68, 38.59, 0.03081], "hull": 28, "edges": [28, 26, 32, 30, 30, 28, 36, 34, 34, 32, 40, 38, 38, 36, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 24, 26, 22, 24], "width": 172, "height": 119}}, "archer7": {"archer7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 26, 18.66, 9.14, 0.99, 32, -11.28, 6.43, 0.01, 2, 26, 4.05, -1.38, 0.99, 32, -29.28, 6.43, 0.01, 2, 26, -7.05, 14.04, 0.99, 32, -29.28, 25.43, 0.01, 2, 26, 7.55, 24.56, 0.99, 32, -11.28, 25.43, 0.01], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 18, "height": 19}}, "archer8": {"archer8": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 26, 33.13, -7.54, 0.99, 32, -9.28, -15.57, 0.01, 2, 26, 8.79, -25.07, 0.99, 32, -39.28, -15.57, 0.01, 2, 26, -6.4, -3.97, 0.99, 32, -39.28, 10.43, 0.01, 2, 26, 17.94, 13.56, 0.99, 32, -9.28, 10.43, 0.01], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 26}}, "Z2": {"Z": {"x": 1.32, "y": 1.31, "width": 116, "height": 132}}, "Z3": {"Z": {"x": 1.32, "y": 1.31, "width": 116, "height": 132}}, "Z": {"Z": {"x": 1.32, "y": 1.31, "width": 116, "height": 132}}, "saoba": {"saoba": {"type": "mesh", "uvs": [0.57749, 0.58877, 0.67852, 0.60629, 0.75798, 0.69601, 0.74715, 0.73523, 0.75976, 0.74276, 0.78426, 0.7574, 0.81569, 0.77616, 0.85047, 0.79694, 0.87791, 0.81333, 0.89387, 0.83074, 0.90602, 0.84399, 0.92646, 0.8663, 0.94223, 0.8835, 0.95299, 0.89524, 0.96606, 0.90713, 0.99005, 0.92897, 1, 0.93802, 0.94798, 0.95204, 0.89608, 0.96603, 0.86228, 0.97345, 0.81801, 0.98316, 0.75747, 0.99226, 0.70589, 1, 0.64247, 1, 0.58383, 1, 0.52526, 1, 0.44153, 1, 0.365, 1, 0.31549, 0.99464, 0.26832, 0.98953, 0.22242, 0.98456, 0.15842, 0.97763, 0.14739, 0.95105, 0.14063, 0.93476, 0.13378, 0.91823, 0.13837, 0.90556, 0.14511, 0.88699, 0.15266, 0.86617, 0.16022, 0.84534, 0.16642, 0.83194, 0.17487, 0.81367, 0.18508, 0.79159, 0.19523, 0.76966, 0.20379, 0.75116, 0.21069, 0.61534, 0.27183, 0.6042, 0, 0.06542, 0, 0, 0.31361, 0], "triangles": [38, 23, 24, 38, 24, 25, 38, 25, 26, 38, 26, 27, 27, 37, 38, 37, 27, 28, 37, 29, 30, 29, 37, 28, 31, 32, 30, 36, 37, 30, 32, 33, 30, 35, 36, 30, 34, 35, 33, 30, 33, 35, 22, 23, 21, 21, 23, 38, 39, 20, 21, 39, 21, 38, 20, 39, 7, 10, 19, 20, 18, 19, 13, 7, 9, 20, 14, 17, 18, 12, 19, 11, 11, 19, 10, 9, 10, 20, 17, 15, 16, 17, 14, 15, 14, 18, 13, 12, 13, 19, 6, 40, 41, 6, 41, 42, 40, 7, 39, 4, 5, 42, 3, 4, 42, 3, 42, 43, 5, 6, 42, 6, 7, 40, 9, 7, 8, 3, 43, 2, 2, 43, 0, 43, 45, 0, 43, 44, 45, 1, 2, 0, 45, 46, 0, 46, 48, 0, 46, 47, 48], "vertices": [1, 53, 29.19, 30.71, 1, 1, 53, 37.45, 26.93, 1, 1, 53, 43.43, 8.77, 1, 3, 53, 42.27, 0.96, 0.98674, 54, -0.77, 21.24, 0.00753, 56, -25.19, 22.09, 0.00573, 3, 53, 43.26, -0.58, 0.93293, 54, 0.83, 22.14, 0.04213, 56, -23.57, 22.96, 0.02494, 3, 53, 45.2, -3.58, 0.79978, 54, 3.93, 23.89, 0.12133, 56, -20.43, 24.63, 0.07888, 4, 53, 47.68, -7.42, 0.62622, 54, 7.91, 26.14, 0.20281, 56, -16.39, 26.79, 0.16917, 55, -4.2, 26.35, 0.0018, 4, 53, 50.42, -11.67, 0.47373, 54, 12.32, 28.63, 0.2404, 56, -11.93, 29.17, 0.28067, 55, 0.2, 28.85, 0.0052, 4, 53, 52.59, -15.02, 0.38687, 54, 15.79, 30.6, 0.24154, 56, -8.41, 31.05, 0.36468, 55, 3.67, 30.82, 0.0069, 4, 53, 53.79, -18.55, 0.31953, 54, 19.38, 31.59, 0.2326, 56, -4.8, 31.96, 0.44018, 55, 7.26, 31.82, 0.00768, 4, 53, 54.71, -21.23, 0.27259, 54, 22.12, 32.35, 0.21985, 56, -2.05, 32.65, 0.50025, 55, 9.99, 32.59, 0.00731, 4, 53, 56.25, -25.74, 0.20889, 54, 26.72, 33.63, 0.19352, 56, 2.58, 33.82, 0.59276, 55, 14.59, 33.87, 0.00483, 4, 53, 57.45, -29.23, 0.17247, 54, 30.26, 34.61, 0.17395, 56, 6.15, 34.72, 0.65101, 55, 18.13, 34.86, 0.00256, 4, 53, 58.26, -31.6, 0.1534, 54, 32.68, 35.28, 0.16222, 56, 8.59, 35.33, 0.68309, 55, 20.55, 35.54, 0.00129, 4, 53, 59.26, -34.02, 0.13802, 54, 35.15, 36.14, 0.15215, 56, 11.08, 36.13, 0.7094, 55, 23.02, 36.41, 0.00044, 3, 53, 61.1, -38.45, 0.122, 54, 39.69, 37.72, 0.141, 56, 15.65, 37.6, 0.737, 3, 53, 61.87, -40.29, 0.12061, 54, 41.57, 38.37, 0.14, 56, 17.54, 38.21, 0.73939, 4, 53, 57.46, -42.94, 0.1132, 54, 43.96, 33.81, 0.13389, 56, 19.82, 33.59, 0.7528, 57, 4.65, 34.64, 0.00011, 4, 53, 53.06, -45.59, 0.09762, 54, 46.34, 29.26, 0.11994, 56, 22.1, 28.99, 0.77867, 57, 7.41, 30.31, 0.00377, 4, 53, 50.2, -46.98, 0.08526, 54, 47.56, 26.33, 0.10779, 56, 23.24, 26.03, 0.79516, 57, 8.88, 27.49, 0.01178, 4, 53, 46.47, -48.8, 0.06749, 54, 49.15, 22.49, 0.08893, 56, 24.75, 22.15, 0.80907, 57, 10.79, 23.8, 0.03451, 4, 53, 41.38, -50.44, 0.04518, 54, 50.5, 17.32, 0.06303, 56, 25.97, 16.95, 0.79161, 57, 12.57, 18.77, 0.10018, 4, 53, 37.05, -51.85, 0.02907, 54, 51.64, 12.92, 0.04257, 56, 27.01, 12.52, 0.72364, 57, 14.08, 14.47, 0.20472, 4, 53, 31.79, -51.67, 0.01427, 54, 51.15, 7.67, 0.02208, 56, 26.39, 7.29, 0.54658, 57, 14.04, 9.21, 0.41706, 4, 53, 26.93, -51.5, 0.00395, 54, 50.7, 2.83, 0.00646, 56, 25.82, 2.46, 0.23395, 57, 14.01, 4.34, 0.75564, 2, 55, 38.2, -1.71, 0.00091, 57, 13.97, -0.52, 0.99909, 3, 53, 15.12, -51.1, 0.00287, 55, 37.57, -8.63, 0.02889, 57, 13.91, -7.47, 0.96824, 3, 53, 8.77, -50.89, 0.01262, 55, 37, -14.96, 0.07858, 57, 13.87, -13.82, 0.9088, 3, 53, 4.7, -49.68, 0.02262, 55, 35.56, -18.96, 0.11744, 57, 12.76, -17.92, 0.85994, 3, 53, 0.82, -48.52, 0.0345, 55, 34.18, -22.76, 0.15669, 57, 11.71, -21.83, 0.80881, 3, 53, -2.95, -47.4, 0.04604, 55, 32.85, -26.47, 0.18937, 57, 10.69, -25.63, 0.76459, 3, 53, -8.21, -45.83, 0.05623, 55, 30.99, -31.63, 0.21444, 57, 9.26, -30.93, 0.72933, 4, 53, -8.95, -40.49, 0.07108, 56, 11.88, -32.38, 0.00092, 55, 25.61, -32.06, 0.2425, 57, 3.94, -31.81, 0.6855, 4, 53, -9.4, -37.21, 0.08867, 56, 8.58, -32.56, 0.00255, 55, 22.31, -32.32, 0.27142, 57, 0.67, -32.34, 0.63736, 4, 53, -9.85, -33.89, 0.1095, 56, 5.23, -32.74, 0.00441, 55, 18.97, -32.59, 0.30046, 57, -2.64, -32.88, 0.58564, 4, 53, -9.39, -31.37, 0.12951, 56, 2.76, -32.06, 0.00583, 55, 16.48, -31.98, 0.32312, 57, -5.17, -32.48, 0.54154, 4, 53, -8.7, -27.68, 0.17004, 56, -0.87, -31.08, 0.00782, 55, 12.83, -31.09, 0.35871, 57, -8.88, -31.9, 0.46343, 4, 53, -7.93, -23.54, 0.23211, 56, -4.93, -29.97, 0.0082, 55, 8.74, -30.09, 0.39134, 57, -13.04, -31.24, 0.36834, 4, 53, -7.16, -19.4, 0.31422, 56, -9, -28.86, 0.00624, 55, 4.65, -29.08, 0.40256, 57, -17.2, -30.58, 0.27699, 4, 53, -6.56, -16.73, 0.38105, 56, -11.6, -28.04, 0.00424, 55, 2.03, -28.33, 0.39225, 57, -19.87, -30.04, 0.22246, 4, 53, -5.73, -13.11, 0.49401, 56, -15.15, -26.91, 0.00169, 55, -1.55, -27.3, 0.34919, 57, -23.52, -29.31, 0.15512, 4, 53, -4.74, -8.72, 0.66261, 56, -19.43, -25.56, 0.00013, 55, -5.87, -26.05, 0.25081, 57, -27.93, -28.43, 0.08645, 3, 53, -3.75, -4.37, 0.84817, 55, -10.16, -24.82, 0.1179, 57, -32.31, -27.55, 0.03393, 3, 53, -2.91, -0.69, 0.97517, 55, -13.78, -23.78, 0.01964, 57, -36.01, -26.81, 0.0052, 1, 53, -1.42, 26.43, 1, 1, 53, 3.73, 28.49, 1, 1, 53, -15.16, 136.95, 1, 1, 53, -14.72, 150.03, 1, 1, 53, 11.3, 149.14, 1], "hull": 49, "edges": [86, 6, 44, 42, 42, 40, 32, 34, 34, 36, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 60, 62, 58, 60, 54, 56, 56, 58, 86, 84, 82, 84, 80, 82, 76, 78, 78, 80, 74, 76, 72, 74, 68, 70, 70, 72, 66, 68, 62, 64, 64, 66, 32, 30, 26, 28, 28, 30, 36, 38, 38, 40, 24, 26, 22, 24, 20, 22, 16, 18, 18, 20, 14, 16, 12, 14, 10, 12, 6, 8, 8, 10, 86, 88, 88, 90, 92, 94, 90, 92, 94, 96, 96, 0, 0, 2, 2, 4, 4, 6], "width": 83, "height": 200}}, "archer18_6": {"archer18_3": {"type": "mesh", "uvs": [1, 1, 0.78388, 1, 0.62138, 1, 0.44588, 1, 0.22487, 1, 0, 1, 0, 0, 0.21512, 0, 0.46213, 0, 0.61813, 0, 0.79038, 0, 1, 0], "triangles": [5, 6, 7, 3, 4, 7, 5, 7, 4, 8, 3, 7, 1, 2, 9, 8, 9, 2, 3, 8, 2, 10, 1, 9, 10, 11, 0, 1, 10, 0], "vertices": [2, 21, -30.17, -96.76, 0.99, 30, 40.64, 2.62, 0.01, 2, 21, -35.88, -61.11, 0.99, 31, 33.83, 53.87, 0.01, 2, 21, -40.17, -34.3, 0.98, 31, 29.54, 80.67, 0.02, 2, 21, -44.8, -5.34, 0.98, 31, 24.91, 109.63, 0.02, 2, 21, -50.63, 31.12, 0.99, 31, 19.07, 146.09, 0.01, 2, 21, -56.57, 68.22, 0.99, 30, 14.24, 167.59, 0.01, 2, 21, -17.91, 74.4, 0.99, 30, 52.9, 173.78, 0.01, 2, 21, -12.24, 38.91, 0.99, 31, 57.47, 153.88, 0.01, 2, 21, -5.72, -1.84, 0.98, 31, 63.99, 113.13, 0.02, 2, 21, -1.6, -27.58, 0.98, 31, 68.11, 87.4, 0.02, 2, 21, 2.95, -55.99, 0.99, 31, 72.66, 58.98, 0.01, 2, 21, 8.48, -90.57, 0.99, 30, 79.29, 8.8, 0.01], "hull": 12, "edges": [10, 12, 0, 22, 16, 6, 16, 18, 4, 6, 18, 4, 18, 20, 20, 22, 0, 2, 2, 4, 20, 2, 12, 14, 14, 16, 6, 8, 8, 10, 14, 8], "width": 239, "height": 56}}, "archer10": {"archer10": {"type": "mesh", "uvs": [1, 0.56485, 1, 0.71003, 1, 0.82366, 1, 1, 0.78745, 1, 0.6716, 1, 0.5602, 1, 0, 1, 0, 0.38021, 0, 0.16085, 0, 0, 0.18591, 0, 0.31364, 0, 1, 0, 0.07006, 0.063, 0.44732, 0.0488, 0.53495, 0.16558, 0.62258, 0.29657, 0.80231, 0.34549, 0.9241, 0.42439, 0.70846, 0.31994, 0.92707, 0.94517, 0.44584, 0.92939, 0.31364, 0.81577, 0.2275, 0.72582, 0.15026, 0.64375, 0.0552, 0.51119], "triangles": [22, 23, 17, 7, 24, 23, 6, 22, 20, 7, 23, 22, 7, 22, 6, 18, 20, 13, 19, 18, 13, 0, 19, 13, 19, 0, 1, 19, 1, 18, 2, 21, 1, 1, 6, 20, 18, 1, 20, 1, 5, 6, 1, 4, 5, 21, 4, 1, 21, 2, 3, 4, 21, 3, 15, 12, 13, 14, 10, 11, 9, 10, 14, 16, 15, 13, 20, 17, 16, 13, 20, 16, 8, 9, 14, 11, 26, 8, 14, 11, 8, 16, 25, 26, 16, 26, 12, 11, 12, 26, 16, 12, 15, 17, 25, 16, 17, 24, 25, 23, 24, 17, 22, 17, 20, 7, 8, 26, 7, 26, 25, 7, 25, 24], "vertices": [3, 9, 52.28, 22, 0.08391, 26, 14.52, 18.3, 0.90609, 32, -9.2, 16.5, 0.01, 3, 9, 58.83, 15.4, 0.01572, 26, 19.95, 10.76, 0.97428, 32, -9.26, 7.03, 0.01, 3, 9, 63.95, 10.24, 0.00081, 26, 24.2, 4.86, 0.98919, 32, -9.28, -0.28, 0.01, 2, 26, 30.8, -4.3, 0.99, 32, -9.28, -11.57, 0.01, 3, 26, 19.07, -12.74, 0.88296, 32, -23.91, -11.62, 0.01, 10, 32.85, -11.85, 0.10704, 4, 9, 56.04, -13.5, 0.00043, 26, 12.67, -17.35, 0.65496, 32, -32.14, -11.51, 0.01, 10, 26.62, -16.68, 0.33461, 4, 9, 50.67, -18.84, 0.0185, 26, 6.53, -21.77, 0.37523, 32, -40.06, -11.07, 0.01, 10, 20.63, -21.31, 0.59627, 3, 9, 23.62, -45.67, 0.39372, 32, -77.18, -9.47, 0.01, 10, -9.49, -44.63, 0.59628, 3, 9, -4.31, -17.51, 0.92908, 32, -76.73, 27.65, 0.01, 10, -33.77, -13.26, 0.06092, 3, 9, -14.2, -7.54, 0.9888, 32, -77.48, 41.4, 0.01, 10, -42.36, -2.15, 0.0012, 2, 9, -21.45, -0.23, 0.99, 32, -77.98, 51.68, 0.01, 3, 9, -12.48, 8.67, 0.9858, 26, -51.52, 15.29, 0.0042, 32, -65.35, 52.29, 0.01, 3, 9, -6.31, 14.79, 0.95482, 26, -44.48, 20.37, 0.03518, 32, -56.65, 52.69, 0.01, 3, 9, 26.82, 47.66, 0.42961, 26, -6.6, 47.64, 0.56039, 32, -9.62, 53.52, 0.01, 2, 9, -15.23, 0.26, 0.99, 32, -73.03, 47.88, 0.01, 3, 9, 2.34, 18.97, 0.87139, 26, -35.27, 23.15, 0.11861, 32, -47.38, 49.93, 0.01, 4, 9, 11.84, 17.87, 0.79385, 26, -26.07, 20.56, 0.19544, 32, -41.09, 42.64, 0.01, 10, -13.41, 19.87, 0.00071, 4, 9, 21.97, 16.11, 0.63203, 26, -16.34, 17.24, 0.3203, 32, -34.76, 34.29, 0.01, 10, -3.57, 16.89, 0.03767, 4, 9, 32.85, 22.5, 0.33596, 26, -4.59, 21.84, 0.63885, 32, -22.6, 30.95, 0.01, 10, 8.01, 21.89, 0.01519, 4, 9, 42.29, 24.74, 0.20374, 26, 5.08, 22.58, 0.78618, 32, -14.33, 25.74, 0.01, 10, 17.65, 22.97, 9e-05, 4, 9, 27.17, 19.16, 0.46403, 26, -10.72, 19.44, 0.48583, 32, -28.92, 32.69, 0.01, 10, 1.97, 19.28, 0.04014, 3, 26, 24.72, -4.35, 0.98475, 32, -14.25, -8.06, 0.01, 10, 38.21, -3.27, 0.00525, 4, 9, 41.96, -21.11, 0.08883, 26, -2.43, -22.65, 0.14682, 32, -47.63, -5.9, 0.01, 10, 11.71, -22.5, 0.75435, 4, 9, 30.46, -22.27, 0.28294, 26, -13.97, -22, 0.01776, 32, -55.79, 1.8, 0.01, 10, 0.16, -22.25, 0.68931, 4, 9, 22.25, -22.31, 0.48211, 26, -22.09, -20.75, 0.00051, 32, -61.08, 7.33, 0.01, 10, -8, -21.28, 0.50737, 3, 9, 14.82, -22.28, 0.64128, 32, -66.09, 12.15, 0.01, 10, -15.37, -20.34, 0.34872, 3, 9, 4.26, -20.81, 0.81878, 32, -72.61, 19.88, 0.01, 10, -25.67, -17.59, 0.17122], "hull": 14, "edges": [20, 22, 22, 28, 18, 20, 28, 18, 22, 24, 24, 26, 24, 30, 30, 32, 32, 34, 36, 38, 34, 40, 40, 36, 0, 26, 38, 0, 0, 2, 2, 4, 4, 6, 4, 42, 6, 8, 42, 8, 8, 10, 10, 12, 12, 14, 12, 44, 44, 46, 46, 48, 48, 50, 50, 52, 14, 16, 16, 18, 52, 16], "width": 68, "height": 64}}, "penye0001": {"penye0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [243, -120, -243, -120, -243, 120, 243, 120], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 67}, "penye0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [240, -113, -240, -113, -240, 113, 240, 113], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 133, "height": 63}, "penye0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -136, -150, -136, -150, 136, 150, 136], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 83, "height": 76}, "penye0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [112, -139, -112, -139, -112, 139, 112, 139], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 62, "height": 77}, "penye0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [160, -132, -160, -132, -160, 132, 160, 132], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 89, "height": 73}, "penye0006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [212, -86, -212, -86, -212, 86, 212, 86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 118, "height": 48}, "penye0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [215, -55, -215, -55, -215, 55, 215, 55], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 119, "height": 31}, "penye0008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [250, -118, -250, -118, -250, 118, 250, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 139, "height": 66}}, "archer16": {"Dead": {"type": "mesh", "uvs": [1, 1, 0.60307, 1, 0.4581, 1, 0, 1, 0, 0, 0.46989, 0, 0.60189, 0, 1, 0], "triangles": [2, 3, 4, 5, 2, 4, 1, 6, 7, 5, 6, 1, 2, 5, 1, 1, 7, 0], "vertices": [2, 20, -5.03, -35, 0.99, 30, -7.18, 30.27, 0.01, 2, 20, -9.68, -5.41, 0.98, 31, -12.94, 75.46, 0.02, 2, 20, -11.41, 5.4, 0.98, 31, -14.66, 86.26, 0.02, 2, 20, -16.87, 39.54, 0.98, 31, -20.13, 120.4, 0.02, 2, 20, -12.14, 40.29, 0.98, 31, -15.4, 121.16, 0.02, 2, 20, -6.54, 5.28, 0.98, 31, -9.8, 86.14, 0.02, 2, 20, -4.97, -4.56, 0.98, 31, -8.22, 76.3, 0.02, 2, 20, -0.3, -34.24, 0.99, 30, -2.46, 31.03, 0.01], "hull": 8, "edges": [6, 8, 0, 14, 8, 10, 4, 6, 10, 4, 0, 2, 2, 4, 10, 12, 12, 14, 2, 12], "width": 83, "height": 5}, "Dead1": {"type": "mesh", "uvs": [1, 1, 0.60307, 1, 0.4581, 1, 0, 1, 0, 0, 0.46989, 0, 0.60189, 0, 1, 0], "triangles": [2, 3, 4, 5, 2, 4, 1, 6, 7, 5, 6, 1, 2, 5, 1, 1, 7, 0], "vertices": [2, 20, -42.22, -40.41, 0.99, 30, -44.37, 24.86, 0.01, 2, 20, -46.8, -11.23, 0.98, 31, -50.06, 69.63, 0.02, 2, 20, -48.51, -0.58, 0.98, 31, -51.76, 80.28, 0.02, 2, 20, -53.89, 33.08, 0.98, 31, -57.15, 113.94, 0.02, 2, 20, -9.72, 40.15, 0.98, 31, -12.97, 121.01, 0.02, 2, 20, -4.19, 5.62, 0.98, 31, -7.45, 86.49, 0.02, 2, 20, -2.64, -4.08, 0.98, 31, -5.9, 76.79, 0.02, 2, 20, 1.96, -33.35, 0.99, 30, -0.2, 31.93, 0.01], "hull": 8, "edges": [6, 8, 0, 14, 8, 10, 4, 6, 10, 4, 0, 2, 2, 4, 10, 12, 12, 14, 2, 12], "width": 83, "height": 44}, "archer16_1": {"type": "mesh", "uvs": [1, 1, 0.60307, 1, 0.4581, 1, 0, 1, 0, 0, 0.46989, 0, 0.60189, 0, 1, 0], "triangles": [2, 3, 4, 5, 2, 4, 1, 6, 7, 5, 6, 1, 2, 5, 1, 1, 7, 0], "vertices": [2, 20, -10.53, -31.62, 0.9798, 31, -13.79, 49.24, 0.0202, 2, 20, -14.31, -7.99, 0.98, 31, -17.57, 72.87, 0.02, 2, 20, -15.55, -0.27, 0.98, 31, -18.8, 80.6, 0.02, 2, 20, -19.91, 26.99, 0.98, 31, -23.16, 107.85, 0.02, 2, 20, 8.15, 31.48, 0.98, 31, 4.89, 112.34, 0.02, 2, 20, 12.69, 3.05, 0.98, 31, 9.44, 83.92, 0.02, 2, 20, 13.72, -3.39, 0.98, 31, 10.47, 77.48, 0.02, 2, 20, 17.52, -27.13, 0.9798, 31, 14.27, 53.73, 0.0202], "hull": 8, "edges": [6, 8, 0, 14, 8, 10, 4, 6, 10, 4, 0, 2, 2, 4, 10, 12, 12, 14, 2, 12], "width": 48, "height": 24}}, "archer17": {"archer17_4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 19, -1.7, -13.73, 0.97, 31, -36.54, 62.44, 0.03, 2, 19, -6.1, 13.74, 0.97, 31, -40.93, 89.91, 0.03, 2, 19, 0.99, 14.88, 0.97, 31, -33.85, 91.04, 0.03, 2, 19, 5.39, -12.6, 0.97, 31, -29.45, 63.57, 0.03], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 24, "height": 5}, "archer17_6": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 19, -3.54, -14.7, 0.97, 31, -38.38, 61.47, 0.03, 2, 19, -8.69, 17.51, 0.97, 31, -43.53, 93.68, 0.03, 2, 19, 0.37, 18.96, 0.97, 31, -34.47, 95.13, 0.03, 2, 19, 6.36, -13.12, 0.97, 31, -28.48, 63.05, 0.03], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 9}, "archer17_7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 19, -6.39, -6.53, 0.97, 31, -41.23, 69.63, 0.03, 2, 19, -9.23, 11.25, 0.97, 31, -44.07, 87.41, 0.03, 2, 19, 7.66, 13.95, 0.97, 31, -27.18, 90.12, 0.03, 2, 19, 10.5, -3.83, 0.97, 31, -24.34, 72.34, 0.03], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 16, "height": 12}}, "archer18": {"archer18_7": {"type": "mesh", "uvs": [0.8232, 0.06942, 0.93141, 0.16579, 1, 0.32397, 1, 0.42942, 1, 0.55124, 0.92454, 0.67306, 0.94, 0.7876, 1, 0.92578, 1, 1, 0.74763, 1, 0.80431, 0.89851, 0.84553, 0.82578, 0.85584, 0.70579, 0.85748, 0.67277, 0.85927, 0.63669, 0.78885, 0.63306, 0.68064, 0.62397, 0.55354, 0.62215, 0.423, 0.61124, 0.30964, 0.59851, 0.31479, 0.73124, 0.35258, 0.8676, 0.41957, 0.94033, 0.43846, 1, 0.24609, 1, 0.07948, 0.87851, 0, 0.69306, 0, 0.56942, 0, 0.35488, 0.05887, 0.20579, 0.16021, 0.08397, 0.31479, 0, 0.67205, 0], "triangles": [16, 17, 1, 1, 17, 0, 19, 27, 28, 19, 30, 31, 29, 30, 19, 19, 28, 29, 18, 31, 32, 17, 18, 32, 0, 17, 32, 2, 16, 1, 19, 31, 18, 2, 3, 16, 3, 15, 16, 3, 14, 15, 4, 14, 3, 5, 14, 4, 13, 14, 5, 26, 27, 19, 26, 19, 20, 12, 13, 5, 12, 5, 6, 25, 26, 20, 25, 20, 21, 11, 12, 6, 24, 25, 21, 24, 21, 22, 24, 22, 23, 11, 6, 7, 7, 10, 11, 8, 10, 7, 9, 10, 8], "vertices": [2, 21, 24.36, -49.28, 0.99, 30, 95.18, 50.1, 0.01, 2, 21, 13.26, -67.77, 0.99, 30, 84.07, 31.61, 0.01, 2, 21, -7.59, -81.7, 0.99, 30, 63.22, 17.67, 0.01, 2, 21, -22.6, -84.11, 0.99, 30, 48.21, 15.27, 0.01, 2, 21, -39.93, -86.88, 0.99, 30, 30.88, 12.5, 0.01, 2, 21, -59.08, -78.29, 0.99, 30, 11.73, 21.09, 0.01, 2, 21, -75.01, -83.22, 0.99, 30, -4.2, 16.16, 0.01, 2, 21, -93.23, -95.41, 0.99, 30, -22.41, 3.97, 0.01, 2, 21, -103.79, -97.1, 0.99, 30, -32.98, 2.28, 0.01, 2, 21, -109.87, -59.08, 0.99, 30, -39.06, 40.3, 0.01, 2, 21, -94.06, -65.31, 0.99, 30, -23.25, 34.07, 0.01, 2, 21, -82.72, -69.86, 0.99, 30, -11.91, 29.51, 0.01, 2, 21, -65.4, -68.68, 0.99, 30, 5.41, 30.69, 0.01, 2, 21, -60.66, -68.18, 0.99, 30, 10.15, 31.2, 0.01, 2, 21, -55.48, -67.63, 0.99, 30, 15.33, 31.75, 0.01, 2, 21, -56.66, -56.94, 0.99, 31, 13.04, 58.03, 0.01, 2, 21, -57.98, -40.43, 0.98, 31, 11.73, 74.54, 0.02, 2, 21, -60.78, -21.25, 0.98, 31, 8.93, 93.73, 0.02, 2, 21, -62.38, -1.34, 0.99, 31, 7.33, 113.64, 0.01, 2, 21, -63.3, 16.03, 0.99, 31, 6.41, 131, 0.01, 2, 21, -82.06, 12.23, 0.99, 31, -12.35, 127.2, 0.01, 2, 21, -100.55, 3.43, 0.99, 31, -30.84, 118.41, 0.01, 2, 21, -109.28, -8.31, 0.99, 31, -39.58, 106.66, 0.01, 2, 21, -117.32, -12.52, 0.99, 31, -47.61, 102.46, 0.01, 2, 21, -121.96, 16.46, 0.99, 30, -51.14, 115.84, 0.01, 2, 21, -108.68, 44.32, 0.99, 30, -37.87, 143.7, 0.01, 2, 21, -84.21, 60.51, 0.99, 30, -13.4, 159.89, 0.01, 2, 21, -66.62, 63.33, 0.99, 30, 4.19, 162.71, 0.01, 2, 21, -36.09, 68.21, 0.99, 30, 34.72, 167.59, 0.01, 2, 21, -13.46, 62.74, 0.99, 30, 57.35, 162.12, 0.01, 2, 21, 6.32, 50.25, 0.99, 30, 77.13, 149.63, 0.01, 2, 21, 21.99, 28.88, 0.99, 30, 92.8, 128.26, 0.01, 2, 21, 30.6, -24.93, 0.99, 30, 101.41, 74.44, 0.01], "hull": 33, "edges": [46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 24, 22, 22, 20, 16, 18, 20, 18, 16, 14, 14, 12, 12, 10, 28, 26, 26, 24, 10, 26, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 64, 62, 64, 62, 60, 60, 58, 58, 56, 54, 56, 52, 54, 52, 50, 46, 48, 50, 48], "width": 235, "height": 222}}, "archer19": {"archer19": {"type": "mesh", "uvs": [1, 0.13366, 1, 0.34766, 1, 1, 0.51182, 1, 0.0961, 1, 0, 1, 0, 0.84183, 0, 0.68415, 0, 0.62502, 0, 0.50535, 0, 0, 0.57146, 0, 0.71705, 0, 0.8679, 0, 1, 0, 0.51883, 0.89252, 0.57146, 0.82775, 0.6732, 0.78411, 0.68372, 0.69964, 0.68372, 0.64332, 0.68021, 0.54899, 0.70652, 0.51943, 0.7837, 0.47156, 0.89597, 0.43495, 0.33115, 0.08298, 0.20485, 0.20265, 0.11715, 0.30965, 0.04523, 0.41102], "triangles": [19, 20, 21, 18, 17, 19, 19, 16, 7, 17, 16, 19, 15, 6, 7, 16, 15, 7, 4, 5, 6, 15, 4, 6, 3, 4, 15, 23, 1, 2, 19, 22, 23, 19, 21, 22, 19, 23, 18, 2, 18, 23, 17, 18, 2, 15, 16, 17, 17, 3, 15, 2, 3, 17, 19, 8, 9, 19, 7, 8, 27, 20, 19, 9, 27, 19, 24, 10, 11, 13, 14, 0, 25, 10, 24, 26, 10, 25, 13, 0, 12, 27, 10, 26, 1, 23, 0, 0, 22, 12, 23, 22, 0, 27, 9, 10, 11, 22, 24, 22, 11, 12, 21, 24, 22, 25, 24, 21, 20, 26, 25, 21, 20, 25, 26, 20, 27], "vertices": [4, 6, -10.51, 7.79, 0.96938, 7, -30.32, 26.14, 0.0128, 8, -43.17, 40.29, 0.00782, 33, -116.37, 44.24, 0.01, 4, 6, -0.73, 20.78, 0.67691, 7, -15.18, 32.08, 0.17771, 8, -26.93, 41.04, 0.13538, 33, -117.16, 29.15, 0.01, 4, 6, 29.09, 60.39, 0.00015, 7, 30.97, 50.21, 0.07843, 8, 22.6, 43.31, 0.91141, 33, -115.92, -20.53, 0.01, 3, 7, 41.85, 22.5, 0.00294, 8, 23.96, 13.56, 0.98706, 33, -146.08, -21.02, 0.01, 3, 7, 51.13, -1.11, 0.0259, 8, 25.13, -11.77, 0.9641, 33, -171.31, -21.05, 0.01, 3, 7, 53.27, -6.56, 0.03659, 8, 25.4, -17.63, 0.95341, 33, -177.11, -21.09, 0.01, 4, 6, 70.59, 14.09, 0.00048, 7, 42.08, -10.96, 0.1868, 8, 13.39, -18.18, 0.80272, 33, -176.58, -9.36, 0.01, 4, 6, 63.38, 4.52, 0.0379, 7, 30.93, -15.34, 0.63246, 8, 1.42, -18.73, 0.31963, 33, -175.9, 1.81, 0.01, 4, 6, 60.68, 0.93, 0.08536, 7, 26.74, -16.98, 0.75696, 8, -3.07, -18.93, 0.14768, 33, -176.13, 6.14, 0.01, 4, 6, 55.21, -6.34, 0.2726, 7, 18.28, -20.31, 0.71095, 8, -12.16, -19.35, 0.00646, 33, -177.29, 15.74, 0.01, 2, 6, 32.11, -37.02, 0.99, 33, -176.73, 57.39, 0.01, 2, 6, 4.26, -16.05, 0.99, 33, -141.92, 55.62, 0.01, 2, 6, -2.83, -10.71, 0.99, 33, -133.05, 55.17, 0.01, 2, 6, -10.19, -5.17, 0.99, 33, -123.86, 54.71, 0.01, 2, 6, -16.62, -0.33, 0.99, 33, -115.81, 54.3, 0.01, 3, 7, 34.09, 19.91, 0.00933, 8, 15.79, 13.61, 0.98067, 33, -145.62, -12.83, 0.01, 4, 6, 42.1, 34.2, 0.00068, 7, 28.34, 21.09, 0.04677, 8, 10.72, 16.6, 0.94255, 33, -142.29, -7.81, 0.01, 4, 6, 35.15, 35.29, 0.00962, 7, 22.98, 25.66, 0.12413, 8, 7.12, 22.64, 0.85625, 33, -135.9, -4.18, 0.01, 4, 6, 30.78, 30.55, 0.02989, 7, 16.77, 23.91, 0.23798, 8, 0.68, 22.99, 0.72213, 33, -135.16, 2.65, 0.01, 4, 6, 28.2, 27.13, 0.05269, 7, 12.79, 22.34, 0.33262, 8, -3.59, 22.79, 0.60469, 33, -135.18, 7.26, 0.01, 4, 6, 24.06, 21.27, 0.12242, 7, 6.19, 19.52, 0.47978, 8, -10.75, 22.25, 0.3878, 33, -135.71, 14.92, 0.01, 4, 6, 21.43, 20.44, 0.18584, 7, 3.52, 20.19, 0.46948, 8, -13.06, 23.75, 0.33469, 33, -134.33, 17.23, 0.01, 4, 6, 15.48, 20.37, 0.34057, 7, -1.59, 23.25, 0.37858, 8, -16.91, 28.29, 0.27085, 33, -130.02, 20.72, 0.01, 4, 6, 8.33, 22.26, 0.47326, 7, -6.68, 28.6, 0.27889, 8, -20.01, 35, 0.23785, 33, -123.38, 23.18, 0.01, 2, 6, 19.76, -19.83, 0.99, 33, -156.88, 50.07, 0.01, 3, 6, 31.39, -17.2, 0.96309, 7, -7.7, -17.09, 0.02691, 33, -165.09, 41.31, 0.01, 3, 6, 40.55, -13.92, 0.77995, 7, 1.82, -19.1, 0.21005, 33, -170.96, 32.81, 0.01, 3, 6, 48.69, -10.41, 0.49973, 7, 10.6, -20.36, 0.49027, 33, -175.26, 24.01, 0.01], "hull": 15, "edges": [4, 6, 6, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 4, 2, 46, 2, 2, 0, 0, 28, 26, 28, 0, 26, 24, 26, 20, 22, 22, 24, 22, 48, 48, 50, 50, 52, 52, 54, 18, 20, 54, 18, 16, 18, 14, 16, 10, 12, 12, 14, 6, 8, 8, 10, 12, 8], "width": 61, "height": 76}}, "penye1": {"penye0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [243, -120, -243, -120, -243, 120, 243, 120], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 67}, "penye0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [240, -113, -240, -113, -240, 113, 240, 113], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 133, "height": 63}, "penye0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -136, -150, -136, -150, 136, 150, 136], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 83, "height": 76}, "penye0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [112, -139, -112, -139, -112, 139, 112, 139], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 62, "height": 77}, "penye0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [160, -132, -160, -132, -160, 132, 160, 132], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 89, "height": 73}, "penye0006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [212, -86, -212, -86, -212, 86, 212, 86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 118, "height": 48}, "penye0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [215, -55, -215, -55, -215, 55, 215, 55], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 119, "height": 31}, "penye0008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [250, -118, -250, -118, -250, 118, 250, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 139, "height": 66}}, "penye2": {"penye0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [243, -120, -243, -120, -243, 120, 243, 120], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 67}, "penye0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [240, -113, -240, -113, -240, 113, 240, 113], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 133, "height": 63}, "penye0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -136, -150, -136, -150, 136, 150, 136], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 83, "height": 76}, "penye0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [112, -139, -112, -139, -112, 139, 112, 139], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 62, "height": 77}, "penye0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [160, -132, -160, -132, -160, 132, 160, 132], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 89, "height": 73}, "penye0006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [212, -86, -212, -86, -212, 86, 212, 86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 118, "height": 48}, "penye0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [215, -55, -215, -55, -215, 55, 215, 55], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 119, "height": 31}, "penye0008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [250, -118, -250, -118, -250, 118, 250, 118], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 139, "height": 66}}, "barb": {"BarbarianAxe": {"x": 19.46, "y": 36.82, "rotation": -15.78, "width": 83, "height": 175}, "barb": {"x": 9.44, "y": 31.75, "rotation": -15.78, "width": 128, "height": 163}, "barb1": {"x": 9.44, "y": 31.75, "rotation": -15.78, "width": 70, "height": 89}, "barb5": {"x": 0.77, "y": 33.71, "rotation": -15.78, "width": 57, "height": 130}, "barb5_1": {"x": 0.77, "y": 33.71, "rotation": -15.78, "width": 57, "height": 130}, "barb6": {"x": -2.22, "y": -0.38, "rotation": -15.78, "width": 54, "height": 95}, "barb6_1": {"type": "mesh", "uvs": [1, 1, 0.67356, 1, 0, 1, 0, 0.10013, 0.66085, 0.12503, 0.65833, 0, 1, 0, 0.66705, 0.565, 0.62602, 0.47266, 0.66498, 0.32994], "triangles": [4, 5, 6, 9, 4, 6, 8, 3, 4, 8, 4, 9, 7, 9, 6, 8, 9, 7, 2, 3, 8, 0, 1, 7, 8, 7, 1, 2, 8, 1, 0, 7, 6], "vertices": [10.21, -54.53, -35.97, -41.48, -131.25, -14.55, -107.75, 68.58, -14.92, 39.86, -12.01, 51.51, 36.32, 37.85, -25.53, -1.04, -28.93, 9.13, -19.69, 20.76], "hull": 7, "edges": [0, 12, 0, 2, 2, 4, 2, 14, 14, 16, 16, 18, 10, 12, 10, 8, 8, 18, 4, 6, 8, 6], "width": 147, "height": 96}, "img_v2_75d8f8eb-25ec-4d25-822f-3c3c72e1558g": {"x": 3.06, "y": 17.42, "width": 120, "height": 120}}, "erduo": {"erduo_1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 5, 13.47, 29.35, 0.99, 31, -31.47, 122.58, 0.01, 2, 5, 6.13, 75.21, 0.99, 30, -37.71, 152.85, 0.01, 2, 5, 51.99, 82.55, 0.99, 30, 8.16, 160.19, 0.01, 2, 5, 59.33, 36.69, 0.99, 31, 14.4, 129.92, 0.01], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 31}}, "tong": {"tong1": {"x": 0.27, "y": 31.27, "width": 100, "height": 89}}, "tong2": {"tong": {"x": 0.27, "y": 31.27, "width": 100, "height": 89}}}}], "animations": {"build": {"slots": {"barb": {"attachment": [{"name": "barb1"}]}}, "bones": {"ALL66": {"translate": [{"x": -21.15, "y": 8.08}, {"time": 0.2333, "x": 507.82, "y": 181.73}, {"time": 0.3, "x": -229.21, "y": -13.86}, {"time": 0.4333, "x": -718.2, "y": -26.89}, {"time": 0.5, "x": -21.15, "y": 8.08}]}, "ALL70": {"rotate": [{"angle": -32.27}], "translate": [{"x": 395.69, "y": -153.25}]}, "ALL69": {"rotate": [{"angle": -14.12}, {"time": 0.2, "angle": 100.54}, {"time": 0.2667, "angle": -18.65}, {"time": 0.4, "angle": -13.38}, {"time": 0.5, "angle": -14.12}], "translate": [{"x": 11.43, "y": -3.29}, {"time": 0.2, "x": 81.05, "y": -11.22}, {"time": 0.2667, "x": 19.77, "y": -8.52}, {"time": 0.4, "x": 26.46, "y": -3.52}, {"time": 0.5, "x": 11.43, "y": -3.29}]}, "ALL2": {"rotate": [{}, {"time": 0.2, "angle": 3.33}, {"time": 0.2667}], "translate": [{"y": -3.1}, {"time": 0.2, "x": -2.39, "y": 3.83}, {"time": 0.2667, "x": 1.06, "y": -5.21}, {"time": 0.4, "x": 4.98, "y": -5.21}, {"time": 0.5, "y": -3.1}]}, "ALL5": {"rotate": [{"angle": 1.39}, {"time": 0.2, "angle": 8.75}, {"time": 0.2667, "angle": 9.2}, {"time": 0.4, "angle": 14.15}, {"time": 0.5, "angle": 1.39}], "translate": [{"time": 0.2}, {"time": 0.2667, "x": -3.67, "y": -6.82, "curve": "stepped"}, {"time": 0.4, "x": -3.67, "y": -6.82}, {"time": 0.5}], "scale": [{}, {"time": 0.3, "x": 0.926, "y": 1.035}, {"time": 0.5}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL6": {"rotate": [{"angle": -2.9}]}, "ALL8": {"rotate": [{"angle": 31.63}, {"time": 0.3667, "angle": 24.15}, {"time": 0.5, "angle": 31.63}], "translate": [{"x": -3.74, "y": -19.38}, {"time": 0.3667, "x": -2.29, "y": -10.14}, {"time": 0.5, "x": -3.74, "y": -19.38}]}, "ALL64": {"translate": [{"x": 61.46, "y": -3.87}, {"time": 0.2, "x": 247.15, "y": -314.7}, {"time": 0.4, "x": -131.1, "y": 19.46}, {"time": 0.5, "x": 61.46, "y": -3.87}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL3": {"rotate": [{"angle": -5.23, "curve": "stepped"}, {"time": 0.2, "angle": -5.23}, {"time": 0.2667, "angle": -7.1}, {"time": 0.4, "angle": -8.31}, {"time": 0.5, "angle": -5.23}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL4": {"rotate": [{"angle": -4.41}, {"time": 0.2, "angle": 10.24}, {"time": 0.2667, "angle": -10.01}, {"time": 0.4, "angle": -15.74}, {"time": 0.5, "angle": -4.41}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL11": {"rotate": [{"angle": -1.41}]}, "ALL37": {"rotate": [{"angle": 3.5}, {"time": 0.2333, "angle": 17.26}, {"time": 0.4667}, {"time": 0.5, "angle": 3.5}]}, "ALL36": {"rotate": [{"angle": 12.37}, {"time": 0.2333}, {"time": 0.4333, "angle": 17.26}, {"time": 0.5, "angle": 12.37}]}}, "ik": {"ALL16": [{"bendPositive": false}]}, "deform": {"default": {"archer2": {"archer2_1": [{}, {"time": 0.2, "offset": 36, "vertices": [0.23717, -0.02951, 0.23717, -0.02952]}, {"time": 0.2667}]}}}}, "cheer": {"slots": {"archer17": {"attachment": [{"time": 0.4, "name": "archer17_6"}, {"time": 0.8333, "name": "archer17_4"}]}, "archer16": {"attachment": [{"time": 0.4, "name": "Dead"}, {"time": 0.8333, "name": "archer16_1"}]}}, "bones": {"ALL4": {"rotate": [{"angle": -12.79}, {"time": 0.2, "angle": -15.97}, {"time": 0.3333, "angle": -21.76}, {"time": 0.4, "angle": -20.48}, {"time": 0.7333, "angle": -18.37}, {"time": 0.9333, "angle": -12.79}]}, "ALL69": {"rotate": [{"angle": 73.83}, {"time": 0.2, "angle": 93.56}, {"time": 0.3333, "angle": 110.98}, {"time": 0.3667, "angle": 128.07}, {"time": 0.4, "angle": 105.74}, {"time": 0.7333, "angle": 113.45}, {"time": 0.9333, "angle": -3.21}], "translate": [{"x": 12.77, "y": 5.36}, {"time": 0.2, "x": 10.78, "y": 13.12}, {"time": 0.3333, "x": 36.58, "y": 3.47}, {"time": 0.3667, "x": 59.39, "y": 6.37}, {"time": 0.4, "x": 99.11, "y": 10.51}, {"time": 0.7333, "x": 98.11, "y": -0.71}, {"time": 0.9333, "x": 9.05, "y": -7.22}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL6": {"rotate": [{"angle": -2.9}], "translate": [{"time": 0.3333}, {"time": 0.4, "x": -19.72, "y": 1.09, "curve": "stepped"}, {"time": 0.7333, "x": -19.72, "y": 1.09}, {"time": 0.9333}]}, "ALL5": {"rotate": [{"angle": 11.57}, {"time": 0.2, "angle": 12.42}, {"time": 0.3333, "angle": 22.6}, {"time": 0.4, "angle": 27.39}, {"time": 0.7333, "angle": 30.81}, {"time": 0.9333, "angle": 11.57}]}, "ALL2": {"rotate": [{"angle": -4.11}, {"time": 0.2, "angle": -2.67}, {"time": 0.3333, "angle": 2.97}, {"time": 0.4, "angle": 2.7}, {"time": 0.7333, "angle": 2.51}, {"time": 0.9333, "angle": -4.11}], "translate": [{"y": -7.23}, {"time": 0.2, "x": 1.86, "y": -10.22}, {"time": 0.3333, "x": 1.86, "y": -6.86}, {"time": 0.3667, "x": -0.24, "y": -2.77}, {"time": 0.4, "x": -1.49, "y": 6.94}, {"time": 0.7333, "x": 1.9, "y": 9.85}, {"time": 0.9333, "y": -7.23}]}, "ALL8": {"rotate": [{"angle": 105.63}, {"time": 0.2, "angle": 103.1}, {"time": 0.3333, "angle": 139.3}, {"time": 0.4, "angle": -117.08, "curve": "stepped"}, {"time": 0.7333, "angle": -117.08}, {"time": 0.9333, "angle": 67.29}], "translate": [{"x": -1.89, "y": -54.32}, {"time": 0.2, "x": -0.18, "y": -45.22}, {"time": 0.3333, "x": 8.41, "y": -68.09}, {"time": 0.3667, "x": 51.04, "y": -60.41}, {"time": 0.4, "x": 71.24, "y": 18.72}, {"time": 0.7333, "x": 60.59, "y": 27.89}, {"time": 0.8, "x": 54.66, "y": -61.94}, {"time": 0.8333, "x": 33.71, "y": -78.8}, {"time": 0.9333, "x": -5.71, "y": -64.68}]}, "ALL64": {"translate": [{"x": 61.46, "y": -3.87, "curve": "stepped"}, {"time": 0.4, "x": 61.46, "y": -3.87}, {"time": 0.7333, "x": 154.3, "y": 6.81}, {"time": 0.9333, "x": 61.46, "y": -3.87}]}, "ALL66": {"translate": [{"x": -21.15, "y": 8.08}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL11": {"rotate": [{"angle": -1.41}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL37": {"rotate": [{"angle": 3.5}]}, "ALL36": {"rotate": [{"angle": 12.37}]}, "ALL3": {"rotate": [{"angle": -10.82}, {"time": 0.2, "angle": -13.7}, {"time": 0.3333, "angle": -11.08}, {"time": 0.4, "angle": 3.13, "curve": "stepped"}, {"time": 0.7333, "angle": 3.13}, {"time": 0.9333, "angle": -10.82}]}}, "ik": {"ALL16": [{"bendPositive": false}]}}, "clean": {"slots": {"saoba": {"attachment": [{"name": "saoba"}]}, "archer16": {"attachment": [{"name": "Dead"}]}}, "bones": {"ALL41": {"rotate": [{"angle": -117.77}, {"time": 0.2, "angle": -102.89}, {"time": 0.7, "angle": -143.34}, {"time": 1, "angle": -117.77}], "translate": [{"x": -2.71, "y": 29.85}, {"time": 0.2, "x": -11.17, "y": 32.63}, {"time": 0.7, "x": 11.83, "y": 25.06}, {"time": 1, "x": -2.71, "y": 29.85}]}, "ALL4": {"rotate": [{"angle": -10.3}, {"time": 0.5, "angle": -12.29}, {"time": 1, "angle": -10.3}]}, "ALL66": {"translate": [{"x": -128.69, "y": -24.32}, {"time": 0.2, "x": -21.15, "y": 8.08}, {"time": 0.7, "x": -313.48, "y": -80.01}, {"time": 1, "x": -128.69, "y": -24.32}]}, "ALL3": {"rotate": [{"angle": -4.76}, {"time": 0.5, "angle": -3.54}, {"time": 1, "angle": -4.76}]}, "ALL58": {"rotate": [{"angle": 2.39}], "translate": [{"x": -0.35, "y": 11.64}]}, "ALL69": {"rotate": [{"angle": 54.45}], "translate": [{"x": 30.5, "y": 22.37}, {"time": 0.2, "x": 32.28, "y": 21.69}, {"time": 0.7, "x": 27.42, "y": 23.54}, {"time": 1, "x": 30.5, "y": 22.37}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL6": {"rotate": [{"angle": -2.9}], "translate": [{"x": -5.54, "y": 1.29}], "scale": [{"x": 1.243}]}, "ALL5": {"rotate": [{"angle": 12.32}, {"time": 0.5, "angle": 12.81}, {"time": 1, "angle": 12.32}], "translate": [{"x": -1.89, "y": -7.19}, {"time": 0.5}, {"time": 1, "x": -1.89, "y": -7.19}], "scale": [{}, {"time": 0.5, "x": 0.942, "y": 1.02}, {"time": 1}]}, "ALL2": {"rotate": [{"angle": -9.57}, {"time": 0.5, "angle": -5.48}, {"time": 1, "angle": -9.57}], "translate": [{"y": 1.32}, {"time": 0.5, "x": -3.63, "y": 1.32}, {"time": 1, "y": 1.32}]}, "ALL13": {"translate": [{"x": -4.98}]}, "ALL8": {"rotate": [{"angle": 99.38}], "translate": [{"x": -6.54, "y": -75.89}, {"time": 0.2, "x": -3.95, "y": -83.53}, {"time": 0.7, "x": -11, "y": -62.75}, {"time": 1, "x": -6.54, "y": -75.89}]}, "ALL64": {"translate": [{"x": -113.98, "y": 27.72}, {"time": 0.3, "x": -40.58, "y": -75.73}, {"time": 0.8, "x": -156.69, "y": 87.92}, {"time": 1, "x": -113.98, "y": 27.72}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL11": {"rotate": [{"angle": -1.41}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL7": {"rotate": [{"angle": 10.4}], "scale": [{"x": 1.243}]}, "ALL37": {"rotate": [{"angle": 3.5}, {"time": 0.3667, "angle": 17.26}, {"time": 0.8667}, {"time": 1, "angle": 3.5}]}, "ALL36": {"rotate": [{"angle": 12.37}, {"time": 0.3333}, {"time": 0.8333, "angle": 17.26}, {"time": 1, "angle": 12.37}]}, "ALL46": {"rotate": [{"angle": -1.22}, {"time": 0.2, "angle": 21.2}, {"time": 0.3, "angle": 27.06}, {"time": 0.8, "angle": -18}, {"time": 1, "angle": -1.22}]}, "ALL45": {"rotate": [{"angle": -15.03}, {"time": 0.2, "angle": 6.51}, {"time": 0.4333, "angle": 27.06}, {"time": 0.9333, "angle": -18}, {"time": 1, "angle": -15.03}]}, "ALL44": {"rotate": [{"angle": -14.87}, {"time": 0.0667, "angle": -18}, {"time": 0.2, "angle": -8.87}, {"time": 0.5667, "angle": 27.06}, {"time": 1, "angle": -14.87}]}}, "ik": {"ALL16": [{"bendPositive": false}]}}, "cook": {"slots": {"barb": {"attachment": [{"name": "barb5_1"}]}}, "bones": {"ALL58": {"rotate": [{"angle": -25.15}, {"time": 0.5}, {"time": 1, "angle": -25.15}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL6": {"rotate": [{"angle": -2.9}], "translate": [{"x": -5.02, "y": 1.05}]}, "ALL4": {"rotate": [{"angle": -12.26}, {"time": 0.5, "angle": -19.23}, {"time": 1, "angle": -12.26}]}, "ALL2": {"translate": [{"x": 1.29, "y": -3.06}, {"time": 0.5, "x": 2.87, "y": -3.59}, {"time": 1, "x": 1.29, "y": -3.06}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL36": {"rotate": [{"angle": 12.37}, {"time": 0.3333}, {"time": 0.8333, "angle": 17.26}, {"time": 1, "angle": 12.37}]}, "ALL8": {"rotate": [{"angle": 18.02}, {"time": 0.2667, "angle": 9.4}, {"time": 0.7667, "angle": 25.24}, {"time": 1, "angle": 18.02}], "translate": [{"x": -4.11, "y": -2.57}, {"time": 0.2667, "x": -0.59, "y": 3.53}, {"time": 0.7667, "x": -7.05, "y": -7.68}, {"time": 1, "x": -4.11, "y": -2.57}]}, "ALL64": {"translate": [{"x": 61.46, "y": -3.87}, {"time": 0.4, "x": -51.63, "y": 41.15}, {"time": 0.9, "x": 78.37, "y": -10.6}, {"time": 1, "x": 61.46, "y": -3.87}]}, "ALL37": {"rotate": [{"angle": 3.5}, {"time": 0.3667, "angle": 17.26}, {"time": 0.8667}, {"time": 1, "angle": 3.5}]}, "ALL69": {"rotate": [{"angle": -44.32}, {"time": 0.5, "angle": -41.44}, {"time": 1, "angle": -44.32}], "translate": [{"x": 6.33, "y": -11.29}, {"time": 0.1667, "x": 9.34, "y": -19.8}, {"time": 0.3333, "x": 22.36, "y": -19.55}, {"time": 0.5, "x": 36.15, "y": -14.62}, {"time": 0.6667, "x": 28.27, "y": -5.99}, {"time": 0.8333, "x": 14.22, "y": -6.75}, {"time": 1, "x": 6.33, "y": -11.29}]}, "ALL66": {"translate": [{"x": -21.15, "y": 8.08}, {"time": 0.2333, "x": -65.95, "y": -22.96}, {"time": 0.7333, "x": 32.31, "y": 45.12}, {"time": 1, "x": -21.15, "y": 8.08}]}, "ALL3": {"rotate": [{"angle": -5.98}, {"time": 0.5, "angle": -18.02}, {"time": 1, "angle": -5.98}]}, "ALL5": {"rotate": [{"angle": -0.44}, {"time": 0.5, "angle": 21.5}, {"time": 1, "angle": -0.44}], "scale": [{}, {"time": 0.5, "x": 0.942, "y": 1.02}, {"time": 1}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL11": {"rotate": [{"angle": -1.41}]}}, "ik": {"ALL16": [{"bendPositive": false}]}}, "cut": {"slots": {"barb": {"attachment": [{"name": "BarbarianAxe"}]}}, "bones": {"ALL69": {"rotate": [{"angle": -14.12}, {"time": 0.2, "angle": 100.54}, {"time": 0.2667, "angle": -18.65}, {"time": 0.6, "angle": -13.38}, {"time": 0.8333, "angle": -14.12}], "translate": [{"x": 11.43, "y": -3.29}, {"time": 0.2, "x": 81.05, "y": -11.22}, {"time": 0.2667, "x": 15.77, "y": 2.66}, {"time": 0.6, "x": 21.5, "y": 11.37}, {"time": 0.8333, "x": 11.43, "y": -3.29}]}, "ALL2": {"rotate": [{}, {"time": 0.2, "angle": 12.52}, {"time": 0.2667}], "translate": [{"y": -3.1}, {"time": 0.2, "x": -2.39, "y": 3.83}, {"time": 0.2667, "x": 1.06, "y": -5.21}, {"time": 0.6, "x": 4.98, "y": -5.21}, {"time": 0.8333, "y": -3.1}]}, "ALL5": {"rotate": [{"angle": 1.39}, {"time": 0.2, "angle": 8.75}, {"time": 0.2667, "angle": 9.2}, {"time": 0.6, "angle": 14.15}, {"time": 0.8333, "angle": 1.39}], "translate": [{"time": 0.2}, {"time": 0.2667, "x": -3.67, "y": -6.82, "curve": "stepped"}, {"time": 0.6, "x": -3.67, "y": -6.82}, {"time": 0.8333}], "scale": [{}, {"time": 0.3, "x": 0.926, "y": 1.035}, {"time": 0.8333}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL30": {"scale": [{}, {"time": 0.2, "x": 1.441, "y": 1.441}, {"time": 0.2667, "x": 0.726, "y": 0.726}, {"time": 0.8333}]}, "ALL6": {"rotate": [{"angle": -2.9}]}, "ALL8": {"rotate": [{"angle": 62.63}, {"time": 0.2, "angle": 152.06}, {"time": 0.2667, "angle": 68.98, "curve": "stepped"}, {"time": 0.6, "angle": 68.98}, {"time": 0.8333, "angle": 62.63}], "translate": [{"x": -12.1, "y": -85.48}, {"time": 0.2, "x": 70.98, "y": -114.38}, {"time": 0.2667, "x": -4.21, "y": -92.94}, {"time": 0.6, "x": 1.52, "y": -84.24}, {"time": 0.8333, "x": -12.1, "y": -85.48}]}, "ALL64": {"translate": [{"x": 61.46, "y": -3.87}, {"time": 0.2, "x": 247.15, "y": -314.7}, {"time": 0.6, "x": -131.1, "y": 19.46}, {"time": 0.8333, "x": 61.46, "y": -3.87}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL66": {"translate": [{"x": -21.15, "y": 8.08}, {"time": 0.2333, "x": 507.82, "y": 181.73}, {"time": 0.3, "x": -229.21, "y": -13.86}, {"time": 0.6333, "x": -718.2, "y": -26.89}, {"time": 0.8333, "x": -21.15, "y": 8.08}]}, "ALL3": {"rotate": [{"angle": -5.23, "curve": "stepped"}, {"time": 0.2, "angle": -5.23}, {"time": 0.2667, "angle": -7.1}, {"time": 0.6, "angle": -8.31}, {"time": 0.8333, "angle": -5.23}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL4": {"rotate": [{"angle": -4.41}, {"time": 0.2, "angle": 10.24}, {"time": 0.2667, "angle": -10.01}, {"time": 0.6, "angle": -15.74}, {"time": 0.8333, "angle": -4.41}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL11": {"rotate": [{"angle": -1.41}]}, "ALL37": {"rotate": [{"angle": 3.5}, {"time": 0.2333, "angle": 17.26}, {"time": 0.6667}, {"time": 0.8333, "angle": 3.5}]}, "ALL36": {"rotate": [{"angle": 12.37}, {"time": 0.2333}, {"time": 0.6333, "angle": 17.26}, {"time": 0.8333, "angle": 12.37}]}, "ALL70": {"rotate": [{"angle": -32.27}], "translate": [{"x": 395.69, "y": -153.25}]}, "ren": {"rotate": [{"angle": 5.11}], "translate": [{"x": 9.21, "y": 288.06}]}}, "ik": {"ALL16": [{"bendPositive": false}]}, "deform": {"default": {"archer2": {"archer2_1": [{}, {"time": 0.2, "offset": 36, "vertices": [0.23717, -0.02951, 0.23717, -0.02952]}, {"time": 0.2667}]}}}}, "die": {"slots": {"archer17": {"attachment": [{"name": "archer17_7"}]}, "archer16": {"attachment": [{"time": 0.1, "name": "Dead1"}]}}, "bones": {"ALL12": {"rotate": [{"angle": 2.61}]}, "ALL69": {"rotate": [{"angle": -30.09}, {"time": 0.5667, "angle": 35.57}, {"time": 0.7333, "angle": 53.72}, {"time": 0.9667, "angle": 72.67}], "translate": [{"x": -9.12, "y": 11.84}, {"time": 0.5667, "x": 16.22, "y": -7.12}, {"time": 0.7333, "x": 64.42, "y": -11.1}, {"time": 0.9667, "x": 69.39, "y": 21.8}, {"time": 1.1667, "x": 66.53, "y": 17.65}]}, "ALL6": {"rotate": [{"angle": -2.9}]}, "ALL16": {"rotate": [{}, {"time": 0.1, "angle": 49.74}, {"time": 0.2333, "angle": 19.76}, {"time": 0.4, "angle": 75.53}, {"time": 0.5667, "angle": -24.63}, {"time": 0.7333, "angle": -126.27}, {"time": 0.8333, "angle": -63.28}, {"time": 0.9667, "angle": -91.44}], "translate": [{}, {"time": 0.1, "x": 7.92, "y": 9.57}, {"time": 0.2333, "x": 29.59, "y": 17.38}, {"time": 0.4, "x": 29.59, "y": 39.69}, {"time": 0.5667, "x": 21.01, "y": 11.09}, {"time": 0.7333, "x": -25.69, "y": -2.08}, {"time": 0.8333, "x": -34.14, "y": 5.99}, {"time": 0.9667, "x": -43.82, "y": 0.7}, {"time": 1.1667, "x": -38.56, "y": -6.3}]}, "ALL5": {"rotate": [{}, {"time": 0.1, "angle": 3.52}, {"time": 0.2333, "angle": 13.73}, {"time": 0.4, "angle": 17.71}, {"time": 0.5667, "angle": 25.98}, {"time": 0.7333, "angle": 3.65}, {"time": 0.8333, "angle": -3.96}, {"time": 0.9667, "angle": 15.5}, {"time": 1.1667, "angle": 15.14}]}, "ALL13": {"rotate": [{}, {"time": 0.1, "angle": 38.12}, {"time": 0.2333, "angle": 0.19, "curve": "stepped"}, {"time": 0.4, "angle": 0.19}, {"time": 0.5667, "angle": -45.09}, {"time": 0.7333, "angle": -128.2}, {"time": 0.8333, "angle": -81.37}, {"time": 0.9667, "angle": -107.44}, {"time": 1.1667, "angle": -87.66}], "translate": [{}, {"time": 0.1, "x": 10.23, "y": -3.46, "curve": "stepped"}, {"time": 0.5667, "x": 10.23, "y": -3.46}, {"time": 0.7333, "x": 5.15, "y": 37.14}, {"time": 0.8333, "x": -0.01, "y": 34.89}, {"time": 0.9667, "x": -3.72, "y": 37.53}, {"time": 1.1667, "x": 2.85, "y": 28.77}]}, "ALL8": {"rotate": [{"angle": 9.4}, {"time": 0.3, "angle": 65.34}, {"time": 0.5667, "angle": 56.12}, {"time": 0.7333, "angle": 147.91}, {"time": 0.8333, "angle": 95.21}, {"time": 0.9667, "angle": 74.71}], "translate": [{"x": -6.12, "y": -6.15}, {"time": 0.3, "x": -6.65, "y": -39.46}, {"time": 0.5667, "x": -2.45, "y": -35.59}, {"time": 0.7333, "x": 54.31, "y": -79.03}, {"time": 0.8333, "x": 14.36, "y": -74.24}, {"time": 0.9667, "x": -0.06, "y": -49.37}, {"time": 1.1667, "x": 1.66, "y": -59.45}]}, "ALL66": {"translate": [{"x": -21.15, "y": 8.08}]}, "ALL2": {"rotate": [{"time": 0.1}, {"time": 0.2333, "angle": 26.95}, {"time": 0.4, "angle": 26.44}, {"time": 0.5667, "angle": 15.52}, {"time": 0.7333, "angle": -84.53}, {"time": 0.8333, "angle": -85.59}, {"time": 0.9667, "angle": -89.51}], "translate": [{"y": 2.09}, {"time": 0.1, "x": -13.86, "y": 2.09}, {"time": 0.2333, "x": 9.01, "y": -1.77}, {"time": 0.4, "x": -3.23, "y": 2.07}, {"time": 0.5667, "x": 22.51, "y": 1.21}, {"time": 0.7333, "x": 21.93, "y": -36.99}, {"time": 0.8333, "x": 25.27, "y": -11.51}, {"time": 0.9667, "x": 24.57, "y": -29.04}, {"time": 1.1667, "x": 24.57, "y": -35.83}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL64": {"translate": [{"x": 61.46, "y": -3.87}]}, "ALL4": {"rotate": [{}, {"time": 0.1, "angle": -13.53}, {"time": 0.2333, "angle": 1.75}, {"time": 0.4, "angle": 11.17}, {"time": 0.5667, "angle": 0.84}, {"time": 0.7333, "angle": 2.54}, {"time": 0.8333, "angle": -19.88}, {"time": 0.9667, "angle": -7.84}], "translate": [{}, {"time": 0.1, "x": -4.55, "y": -0.79}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL11": {"rotate": [{"angle": -1.41}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL3": {"rotate": [{}, {"time": 0.1, "angle": -7.12}, {"time": 0.2333, "angle": -4.95, "curve": "stepped"}, {"time": 0.5667, "angle": -4.95}, {"time": 0.7333, "angle": 18.1}, {"time": 0.8333, "angle": 13.85}, {"time": 0.9667, "angle": 2.74}]}, "ALL35": {"rotate": [{}, {"time": 0.1667, "angle": 16.35}, {"time": 0.4333, "angle": -12.24}, {"time": 0.8, "angle": -22.61}, {"time": 1.1667, "angle": -2.94}]}, "ALL40": {"rotate": [{"angle": 9.19}, {"time": 0.1667, "angle": 22.25}, {"time": 0.4333, "angle": -6.33}, {"time": 0.8, "angle": -16.7}, {"time": 1.1667, "angle": 2.97}]}, "ALL37": {"rotate": [{"angle": 3.5}, {"time": 0.1667, "angle": 28.18}, {"time": 0.4333, "angle": -0.41}, {"time": 0.8, "angle": -10.78}, {"time": 1.1667, "angle": 8.89}]}, "ALL39": {"rotate": [{"angle": 4.02}, {"time": 0.1667, "angle": 17.09}, {"time": 0.4333, "angle": -11.5}, {"time": 0.8, "angle": -21.87}, {"time": 1.1667, "angle": -2.2}]}, "ALL36": {"rotate": [{"angle": 12.37}, {"time": 0.1667, "angle": 19.76}, {"time": 0.4333, "angle": -8.82}, {"time": 0.8, "angle": -19.19}, {"time": 1.1667, "angle": 0.48}]}}, "ik": {"ALL16": [{"curve": "stepped"}, {"time": 0.1, "bendPositive": false}]}}, "dig": {"slots": {"barb": {"attachment": [{"name": "barb"}]}}, "bones": {"ALL58": {"rotate": [{"time": 0.2}, {"time": 0.2667, "angle": -13.89, "curve": "stepped"}, {"time": 0.6, "angle": -13.89}, {"time": 0.8333}], "scale": [{"time": 0.2}, {"time": 0.2667, "x": 0.763, "curve": "stepped"}, {"time": 0.6, "x": 0.763}, {"time": 0.8333}]}, "ALL69": {"rotate": [{"angle": -14.12}, {"time": 0.2, "angle": 83.26}, {"time": 0.2667, "angle": -18.65}, {"time": 0.6, "angle": -13.38}, {"time": 0.8333, "angle": -14.12}], "translate": [{"x": 11.43, "y": -3.29}, {"time": 0.2, "x": 44.52, "y": 4.15}, {"time": 0.2667, "x": 15.77, "y": 2.66}, {"time": 0.6, "x": 21.5, "y": 11.37}, {"time": 0.8333, "x": 11.43, "y": -3.29}]}, "ALL2": {"rotate": [{}, {"time": 0.2, "angle": 12.88}, {"time": 0.2667}], "translate": [{"y": -3.1}, {"time": 0.2, "x": -2.39, "y": 3.83}, {"time": 0.2667, "x": 1.06, "y": -5.21}, {"time": 0.6, "x": 4.98, "y": -5.21}, {"time": 0.8333, "y": -3.1}]}, "ALL5": {"rotate": [{"angle": 1.39}, {"time": 0.2, "angle": 8.75}, {"time": 0.2667, "angle": 9.2}, {"time": 0.6, "angle": 14.15}, {"time": 0.8333, "angle": 1.39}], "translate": [{"time": 0.2}, {"time": 0.2667, "x": -3.67, "y": -6.82, "curve": "stepped"}, {"time": 0.6, "x": -3.67, "y": -6.82}, {"time": 0.8333}], "scale": [{}, {"time": 0.3, "x": 0.926, "y": 1.035}, {"time": 0.8333}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL30": {"scale": [{}, {"time": 0.2, "x": 1.441, "y": 1.441}, {"time": 0.2667, "x": 0.726, "y": 0.726}, {"time": 0.8333}]}, "ALL6": {"rotate": [{"angle": -2.9}]}, "ALL8": {"rotate": [{"angle": 62.63}, {"time": 0.2, "angle": 126.63}, {"time": 0.2667, "angle": 68.98, "curve": "stepped"}, {"time": 0.6, "angle": 68.98}, {"time": 0.8333, "angle": 62.63}], "translate": [{"x": -12.1, "y": -85.48}, {"time": 0.2, "x": 35.13, "y": -110.49}, {"time": 0.2667, "x": -4.21, "y": -92.94}, {"time": 0.6, "x": 1.52, "y": -84.24}, {"time": 0.8333, "x": -12.1, "y": -85.48}]}, "ALL64": {"translate": [{"x": 61.46, "y": -3.87}, {"time": 0.2, "x": 247.15, "y": -314.7}, {"time": 0.6, "x": -131.1, "y": 19.46}, {"time": 0.8333, "x": 61.46, "y": -3.87}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL66": {"translate": [{"x": -21.15, "y": 8.08}, {"time": 0.2333, "x": 507.82, "y": 181.73}, {"time": 0.3, "x": -229.21, "y": -13.86}, {"time": 0.6333, "x": -718.2, "y": -26.89}, {"time": 0.8333, "x": -21.15, "y": 8.08}]}, "ALL3": {"rotate": [{"angle": -5.23}, {"time": 0.2, "angle": -3.15}, {"time": 0.2667, "angle": -20.42}, {"time": 0.6, "angle": -24.34}, {"time": 0.8333, "angle": -5.23}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL4": {"rotate": [{"angle": -4.41}, {"time": 0.2, "angle": 8.92}, {"time": 0.2667, "angle": -10.01}, {"time": 0.6, "angle": -15.74}, {"time": 0.8333, "angle": -4.41}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL11": {"rotate": [{"angle": -1.41}]}, "ALL37": {"rotate": [{"angle": 3.5}, {"time": 0.2333, "angle": 17.26}, {"time": 0.6667}, {"time": 0.8333, "angle": 3.5}]}, "ALL36": {"rotate": [{"angle": 12.37}, {"time": 0.2333}, {"time": 0.6333, "angle": 17.26}, {"time": 0.8333, "angle": 12.37}]}, "ALL70": {"rotate": [{"angle": -32.27}], "translate": [{"x": 395.69, "y": -153.25}]}, "ren": {"rotate": [{"angle": 5.11}], "translate": [{"x": 9.21, "y": 288.06}]}}, "ik": {"ALL16": [{"bendPositive": false}]}, "deform": {"default": {"archer2": {"archer2_1": [{}, {"time": 0.2, "offset": 36, "vertices": [0.23717, -0.02951, 0.23717, -0.02952]}, {"time": 0.2667}]}}}}, "eat_1": {"slots": {"archer17": {"attachment": [{"name": "archer17_7"}, {"time": 1.3333, "name": "archer17_4"}, {"time": 2.6667, "name": "archer17_7"}]}, "shiwu": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffff00"}], "attachment": [{"name": "shiwu"}, {"time": 0.5, "name": null}, {"time": 0.6667, "name": "shiwu"}, {"time": 1.1667, "name": null}, {"time": 2.6667, "name": "shiwu"}]}, "barb": {"attachment": [{"name": "img_v2_75d8f8eb-25ec-4d25-822f-3c3c72e1558g"}]}}, "bones": {"ALL30": {"scale": [{}, {"time": 0.4, "x": 1.557, "y": 1.933}, {"time": 0.5, "x": 0.609, "y": 1.16}, {"time": 0.6667}, {"time": 0.8333, "x": 0.609, "y": 1.16}, {"time": 1.1333, "x": 1.557, "y": 1.933}, {"time": 1.3333, "x": 0.479, "y": 0.7}, {"time": 1.5, "x": 0.668, "y": 1.107}, {"time": 1.6667, "x": 1.088, "y": 0.649}, {"time": 1.8333, "x": 0.479, "y": 0.7}, {"time": 2, "x": 0.668, "y": 1.107}, {"time": 2.1667, "x": 1.088, "y": 0.649}, {"time": 2.3333, "x": 0.479, "y": 0.7}, {"time": 2.6667}], "shear": [{"time": 0.4}, {"time": 0.5, "y": 9.43}, {"time": 0.6667}, {"time": 0.8333, "y": 9.43}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.5}, {"time": 1.6667, "y": -12.42}, {"time": 1.8333, "curve": "stepped"}, {"time": 2}, {"time": 2.1667, "y": -10.55}, {"time": 2.3333}]}, "ALL58": {"rotate": [{"angle": 16.64}]}, "ALL52": {"translate": [{"x": -23.77, "y": 1.24}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL37": {"rotate": [{"angle": 3.5}, {"time": 0.5, "angle": 17.26}, {"time": 1.1667}, {"time": 1.3333, "angle": 3.5}, {"time": 1.8333, "angle": 17.26}, {"time": 2.5}, {"time": 2.6667, "angle": 3.5}]}, "ALL16": {"rotate": [{"angle": 18.02}, {"time": 0.3333, "angle": 44.06}, {"time": 0.6667, "angle": 18.02}, {"time": 1, "angle": -17.28}, {"time": 1.3333, "angle": 18.02}, {"time": 1.6667, "angle": 44.06}, {"time": 2, "angle": 18.02}, {"time": 2.3333, "angle": -17.28}, {"time": 2.6667, "angle": 18.02}], "translate": [{"x": 6.53, "y": -13.52}, {"time": 0.3333, "x": 11.83, "y": -9.81}, {"time": 0.6667, "x": 6.53, "y": -13.52}, {"time": 1, "x": -7.78, "y": -17.76}, {"time": 1.3333, "x": 6.53, "y": -13.52}, {"time": 1.6667, "x": 11.83, "y": -9.81}, {"time": 2, "x": 6.53, "y": -13.52}, {"time": 2.3333, "x": -7.78, "y": -17.76}, {"time": 2.6667, "x": 6.53, "y": -13.52}]}, "ALL36": {"rotate": [{"angle": 12.37}, {"time": 0.4333}, {"time": 1.1, "angle": 17.26}, {"time": 1.3333, "angle": 12.37}, {"time": 1.7667}, {"time": 2.4333, "angle": 17.26}, {"time": 2.6667, "angle": 12.37}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL13": {"rotate": [{"angle": 13.1}, {"time": 0.3333, "angle": -0.75}, {"time": 0.6667, "angle": 13.1}, {"time": 1, "angle": 44.11}, {"time": 1.3333, "angle": 13.1}, {"time": 1.6667, "angle": -0.75}, {"time": 2, "angle": 13.1}, {"time": 2.3333, "angle": 44.11}, {"time": 2.6667, "angle": 13.1}], "translate": [{"x": 5.45, "y": -25.16}, {"time": 0.3333, "x": -6.21, "y": -26.22}, {"time": 0.6667, "x": 5.45, "y": -25.16}, {"time": 1, "x": 4.65, "y": -25.42}, {"time": 1.3333, "x": 5.45, "y": -25.16}, {"time": 1.6667, "x": -6.21, "y": -26.22}, {"time": 2, "x": 5.45, "y": -25.16}, {"time": 2.3333, "x": 4.65, "y": -25.42}, {"time": 2.6667, "x": 5.45, "y": -25.16}]}, "ALL64": {"translate": [{"x": 61.46, "y": -3.87}, {"time": 0.5333, "x": -51.63, "y": 41.15}, {"time": 1.2, "x": 78.37, "y": -10.6}, {"time": 1.3333, "x": 61.46, "y": -3.87}, {"time": 1.8667, "x": -51.63, "y": 41.15}, {"time": 2.5333, "x": 78.37, "y": -10.6}, {"time": 2.6667, "x": 61.46, "y": -3.87}]}, "ALL4": {"rotate": [{"angle": -19.33, "curve": "stepped"}, {"time": 1.0667, "angle": -19.33}, {"time": 1.6667, "angle": -11.19, "curve": "stepped"}, {"time": 2.3333, "angle": -11.19}, {"time": 2.6667, "angle": -19.33}]}, "ALL69": {"rotate": [{"angle": 29.19}], "translate": [{"x": 21.04, "y": -1.24}, {"time": 0.6667, "x": 10.24, "y": -0.78}, {"time": 1.3333, "x": 6.63, "y": -1.03, "curve": "stepped"}, {"time": 2, "x": 6.63, "y": -1.03}, {"time": 2.6667, "x": 21.04, "y": -1.24}]}, "ALL66": {"translate": [{"x": -21.15, "y": 8.08}, {"time": 0.3, "x": -65.95, "y": -22.96}, {"time": 0.9667, "x": 32.31, "y": 45.12}, {"time": 1.3333, "x": -21.15, "y": 8.08}, {"time": 1.6333, "x": -65.95, "y": -22.96}, {"time": 2.3, "x": 32.31, "y": 45.12}, {"time": 2.6667, "x": -21.15, "y": 8.08}]}, "ALL3": {"rotate": [{"angle": -3.6}]}, "ALL5": {"rotate": [{}, {"time": 0.6667, "angle": 0.14}, {"time": 1.3333}, {"time": 2, "angle": 9.62}, {"time": 2.6667}], "scale": [{}, {"time": 0.6667, "x": 0.942, "y": 1.02}, {"time": 1.3333}, {"time": 2, "x": 0.942, "y": 1.02}, {"time": 2.6667}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL6": {"rotate": [{"angle": -2.9}]}, "ALL2": {"rotate": [{"angle": 11.79}], "translate": [{"x": -18.65, "y": -32.32}]}, "ALL8": {"rotate": [{"angle": 101.05, "curve": "stepped"}, {"time": 0.1667, "angle": 101.05}, {"time": 0.4333, "angle": 135.51}, {"time": 0.6667, "angle": 101.05, "curve": "stepped"}, {"time": 0.8333, "angle": 101.05}, {"time": 1.1, "angle": 135.51}, {"time": 1.3333, "angle": 90.83, "curve": "stepped"}, {"time": 2, "angle": 90.83}, {"time": 2.6667, "angle": 101.05}], "translate": [{"x": 12.58, "y": -75.94}, {"time": 0.1667, "x": 7.41, "y": -64.11}, {"time": 0.4333, "x": 33.1, "y": -67.73}, {"time": 0.6667, "x": 12.58, "y": -75.94}, {"time": 0.8333, "x": 7.41, "y": -64.11}, {"time": 1.1, "x": 33.1, "y": -67.73}, {"time": 1.3333, "x": -3.68, "y": -47.72}, {"time": 2, "x": -3.68, "y": -42.32}, {"time": 2.6667, "x": 12.58, "y": -75.94}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL11": {"rotate": [{"angle": -1.41}]}}, "ik": {"ALL16": [{"bendPositive": false}]}, "deform": {"default": {"archer4": {"archer4": [{"offset": 22, "vertices": [-0.03033, -2.51537, 0.6277, -2.43597, -1.53028, -1.99656, 0.13129, -3.02048, 0.91564, -2.88134, -1.70325, -2.49789, 0.32663, -0.7307, 0.50614, -0.62002, -0.17586, -0.78082]}, {"time": 0.3333, "offset": 22, "vertices": [0.49741, -2.47151, 1.1545, -2.48992, -1.38089, -2.50463, 0.23479, -3.82178, 0.86998, -3.688, -2.46721, -2.76084, 2.16288, -4.37755, 1.64033, -4.54237, -3.37755, -3.31466, 0.56269, -3.06551, -0.01073, -3.1167, -2.88906, -1.16922, 0.2115, -0.31199, 0.15051, -0.34557, -0.26293, -0.27007]}, {"time": 0.6667, "offset": 22, "vertices": [-0.03033, -2.51537, 0.6277, -2.43597, -1.53028, -1.99656, 0.13129, -3.02048, 0.91564, -2.88134, -1.70325, -2.49789, 0.32663, -0.7307, 0.50614, -0.62002, -0.17586, -0.78082]}, {"time": 1}, {"time": 1.3333, "offset": 22, "vertices": [-0.03033, -2.51537, 0.6277, -2.43597, -1.53028, -1.99656, 0.13129, -3.02048, 0.91564, -2.88134, -1.70325, -2.49789, 0.32663, -0.7307, 0.50614, -0.62002, -0.17586, -0.78082]}, {"time": 1.6667, "offset": 22, "vertices": [0.49741, -2.47151, 1.1545, -2.48992, -1.38089, -2.50463, 0.23479, -3.82178, 0.86998, -3.688, -2.46721, -2.76084, 2.16288, -4.37755, 1.64033, -4.54237, -3.37755, -3.31466, 0.56269, -3.06551, -0.01073, -3.1167, -2.88906, -1.16922, 0.2115, -0.31199, 0.15051, -0.34557, -0.26293, -0.27007]}, {"time": 2, "offset": 22, "vertices": [-0.03033, -2.51537, 0.6277, -2.43597, -1.53028, -1.99656, 0.13129, -3.02048, 0.91564, -2.88134, -1.70325, -2.49789, 0.32663, -0.7307, 0.50614, -0.62002, -0.17586, -0.78082]}, {"time": 2.3333}, {"time": 2.6667, "offset": 22, "vertices": [-0.03033, -2.51537, 0.6277, -2.43597, -1.53028, -1.99656, 0.13129, -3.02048, 0.91564, -2.88134, -1.70325, -2.49789, 0.32663, -0.7307, 0.50614, -0.62002, -0.17586, -0.78082]}]}}}, "drawOrder": [{"offsets": [{"slot": "barb", "offset": 12}]}]}, "eat_2": {"slots": {"archer17": {"attachment": [{"name": "archer17_7"}, {"time": 1.3333, "name": "archer17_4"}, {"time": 2.6667, "name": "archer17_7"}]}, "shiwu": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffff00"}], "attachment": [{"name": "shiwu"}, {"time": 0.5, "name": null}, {"time": 0.6667, "name": "shiwu"}, {"time": 1.1667, "name": null}, {"time": 2.6667, "name": "shiwu"}]}, "barb": {"attachment": [{"name": "img_v2_75d8f8eb-25ec-4d25-822f-3c3c72e1558g"}]}}, "bones": {"ALL30": {"scale": [{}, {"time": 0.4, "x": 1.557, "y": 1.933}, {"time": 0.5, "x": 0.609, "y": 1.16}, {"time": 0.6667}, {"time": 0.8333, "x": 0.609, "y": 1.16}, {"time": 1.1333, "x": 1.557, "y": 1.933}, {"time": 1.3333, "x": 0.479, "y": 0.7}, {"time": 1.5, "x": 0.668, "y": 1.107}, {"time": 1.6667, "x": 1.088, "y": 0.649}, {"time": 1.8333, "x": 0.479, "y": 0.7}, {"time": 2, "x": 0.668, "y": 1.107}, {"time": 2.1667, "x": 1.088, "y": 0.649}, {"time": 2.3333, "x": 0.479, "y": 0.7}, {"time": 2.6667}], "shear": [{"time": 0.4}, {"time": 0.5, "y": 9.43}, {"time": 0.6667}, {"time": 0.8333, "y": 9.43}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.5}, {"time": 1.6667, "y": -12.42}, {"time": 1.8333, "curve": "stepped"}, {"time": 2}, {"time": 2.1667, "y": -10.55}, {"time": 2.3333}]}, "ALL58": {"rotate": [{"angle": 16.64}]}, "ALL52": {"translate": [{"x": -23.77, "y": 1.24}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL37": {"rotate": [{"angle": 3.5}, {"time": 0.5, "angle": 17.26}, {"time": 1.1667}, {"time": 1.3333, "angle": 3.5}, {"time": 1.8333, "angle": 17.26}, {"time": 2.5}, {"time": 2.6667, "angle": 3.5}]}, "ALL36": {"rotate": [{"angle": 12.37}, {"time": 0.4333}, {"time": 1.1, "angle": 17.26}, {"time": 1.3333, "angle": 12.37}, {"time": 1.7667}, {"time": 2.4333, "angle": 17.26}, {"time": 2.6667, "angle": 12.37}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL64": {"translate": [{"x": 61.46, "y": -3.87}, {"time": 0.5333, "x": -51.63, "y": 41.15}, {"time": 1.2, "x": 78.37, "y": -10.6}, {"time": 1.3333, "x": 61.46, "y": -3.87}, {"time": 1.8667, "x": -51.63, "y": 41.15}, {"time": 2.5333, "x": 78.37, "y": -10.6}, {"time": 2.6667, "x": 61.46, "y": -3.87}]}, "ALL4": {"rotate": [{"angle": -19.33, "curve": "stepped"}, {"time": 1.0667, "angle": -19.33}, {"time": 1.6667, "angle": -11.19, "curve": "stepped"}, {"time": 2.3333, "angle": -11.19}, {"time": 2.6667, "angle": -19.33}]}, "ALL69": {"rotate": [{"angle": 29.19}], "translate": [{"x": 21.04, "y": -1.24}, {"time": 0.6667, "x": 10.24, "y": -0.78}, {"time": 1.3333, "x": 6.63, "y": -1.03, "curve": "stepped"}, {"time": 2, "x": 6.63, "y": -1.03}, {"time": 2.6667, "x": 21.04, "y": -1.24}]}, "ALL66": {"translate": [{"x": -21.15, "y": 8.08}, {"time": 0.3, "x": -65.95, "y": -22.96}, {"time": 0.9667, "x": 32.31, "y": 45.12}, {"time": 1.3333, "x": -21.15, "y": 8.08}, {"time": 1.6333, "x": -65.95, "y": -22.96}, {"time": 2.3, "x": 32.31, "y": 45.12}, {"time": 2.6667, "x": -21.15, "y": 8.08}]}, "ALL3": {"rotate": [{"angle": 6.02}]}, "ALL5": {"rotate": [{}, {"time": 0.6667, "angle": 0.14}, {"time": 1.3333}, {"time": 2, "angle": 9.62}, {"time": 2.6667}], "scale": [{}, {"time": 0.6667, "x": 0.942, "y": 1.02}, {"time": 1.3333}, {"time": 2, "x": 0.942, "y": 1.02}, {"time": 2.6667}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL6": {"rotate": [{"angle": -2.9}]}, "ALL2": {"translate": [{"y": 2.09}, {"time": 0.3667}, {"time": 1.0333, "y": 3.84}, {"time": 1.3333, "y": 2.09}, {"time": 1.7}, {"time": 2.3667, "y": 3.84}, {"time": 2.6667, "y": 2.09}]}, "ALL8": {"rotate": [{"angle": 101.05, "curve": "stepped"}, {"time": 0.1667, "angle": 101.05}, {"time": 0.4333, "angle": 135.51}, {"time": 0.6667, "angle": 101.05, "curve": "stepped"}, {"time": 0.8333, "angle": 101.05}, {"time": 1.1, "angle": 135.51}, {"time": 1.3333, "angle": 90.83, "curve": "stepped"}, {"time": 2, "angle": 90.83}, {"time": 2.6667, "angle": 101.05}], "translate": [{"x": 12.58, "y": -75.94}, {"time": 0.1667, "x": 7.41, "y": -64.11}, {"time": 0.4333, "x": 33.1, "y": -67.73}, {"time": 0.6667, "x": 12.58, "y": -75.94}, {"time": 0.8333, "x": 7.41, "y": -64.11}, {"time": 1.1, "x": 33.1, "y": -67.73}, {"time": 1.3333, "x": -3.68, "y": -47.72}, {"time": 2, "x": -3.68, "y": -42.32}, {"time": 2.6667, "x": 12.58, "y": -75.94}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL11": {"rotate": [{"angle": -1.41}]}}, "deform": {"default": {"archer4": {"archer4": [{"offset": 22, "vertices": [-0.03033, -2.51537, 0.6277, -2.43597, -1.53028, -1.99656, 0.13129, -3.02048, 0.91564, -2.88134, -1.70325, -2.49789, 0.32663, -0.7307, 0.50614, -0.62002, -0.17586, -0.78082]}, {"time": 0.3333, "offset": 22, "vertices": [0.49741, -2.47151, 1.1545, -2.48992, -1.38089, -2.50463, 0.23479, -3.82178, 0.86998, -3.688, -2.46721, -2.76084, 2.16288, -4.37755, 1.64033, -4.54237, -3.37755, -3.31466, 0.56269, -3.06551, -0.01073, -3.1167, -2.88906, -1.16922, 0.2115, -0.31199, 0.15051, -0.34557, -0.26293, -0.27007]}, {"time": 0.6667, "offset": 22, "vertices": [-0.03033, -2.51537, 0.6277, -2.43597, -1.53028, -1.99656, 0.13129, -3.02048, 0.91564, -2.88134, -1.70325, -2.49789, 0.32663, -0.7307, 0.50614, -0.62002, -0.17586, -0.78082]}, {"time": 1}, {"time": 1.3333, "offset": 22, "vertices": [-0.03033, -2.51537, 0.6277, -2.43597, -1.53028, -1.99656, 0.13129, -3.02048, 0.91564, -2.88134, -1.70325, -2.49789, 0.32663, -0.7307, 0.50614, -0.62002, -0.17586, -0.78082]}, {"time": 1.6667, "offset": 22, "vertices": [0.49741, -2.47151, 1.1545, -2.48992, -1.38089, -2.50463, 0.23479, -3.82178, 0.86998, -3.688, -2.46721, -2.76084, 2.16288, -4.37755, 1.64033, -4.54237, -3.37755, -3.31466, 0.56269, -3.06551, -0.01073, -3.1167, -2.88906, -1.16922, 0.2115, -0.31199, 0.15051, -0.34557, -0.26293, -0.27007]}, {"time": 2, "offset": 22, "vertices": [-0.03033, -2.51537, 0.6277, -2.43597, -1.53028, -1.99656, 0.13129, -3.02048, 0.91564, -2.88134, -1.70325, -2.49789, 0.32663, -0.7307, 0.50614, -0.62002, -0.17586, -0.78082]}, {"time": 2.3333}, {"time": 2.6667, "offset": 22, "vertices": [-0.03033, -2.51537, 0.6277, -2.43597, -1.53028, -1.99656, 0.13129, -3.02048, 0.91564, -2.88134, -1.70325, -2.49789, 0.32663, -0.7307, 0.50614, -0.62002, -0.17586, -0.78082]}]}}}, "drawOrder": [{"offsets": [{"slot": "barb", "offset": 12}]}]}, "handclap": {"slots": {"archer17": {"attachment": [{"name": "archer17_6"}]}}, "bones": {"ALL30": {"scale": [{}, {"time": 0.2667, "x": 1.271, "y": 1.073}, {"time": 0.5}]}, "ALL69": {"rotate": [{"angle": 4.69}, {"time": 0.0667, "angle": 26.84}, {"time": 0.1333, "angle": 39.09}, {"time": 0.2, "angle": 4.69, "curve": "stepped"}, {"time": 0.2667, "angle": 4.69}, {"time": 0.3333, "angle": 26.84}, {"time": 0.4, "angle": 39.09}, {"time": 0.4333, "angle": 4.69}], "translate": [{"x": 10.18, "y": 4.91}, {"time": 0.0667, "x": 17.32, "y": -9.6}, {"time": 0.1333, "x": 20.45, "y": -10.91}, {"time": 0.2, "x": 9.06, "y": 7.19}, {"time": 0.2667, "x": 10.18, "y": 4.91}, {"time": 0.3333, "x": 17.32, "y": -9.6}, {"time": 0.4, "x": 20.45, "y": -10.91}, {"time": 0.4333, "x": 9.06, "y": 7.19}, {"time": 0.5, "x": 10.18, "y": 4.91}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL6": {"rotate": [{"angle": -2.9}], "scale": [{"x": 1.5}]}, "ALL5": {"rotate": [{}, {"time": 0.2667, "angle": 0.14}, {"time": 0.5}], "scale": [{}, {"time": 0.2667, "x": 0.942, "y": 1.02}, {"time": 0.5}]}, "ALL2": {"translate": [{"y": 2.09}, {"time": 0.1333}, {"time": 0.4, "y": 3.84}, {"time": 0.5, "y": 2.09}]}, "ALL8": {"rotate": [{"angle": 81.64}, {"time": 0.0667, "angle": 40.28}, {"time": 0.1333, "angle": 29.9}, {"time": 0.2, "angle": 80.15}, {"time": 0.2667, "angle": 81.64}, {"time": 0.3333, "angle": 40.28}, {"time": 0.4, "angle": 29.9}, {"time": 0.4333, "angle": 80.15}, {"time": 0.5, "angle": 81.64}], "translate": [{"x": -5.2, "y": -87.32}, {"time": 0.0667, "x": -10.52, "y": -56.52}, {"time": 0.1333, "x": -21.16, "y": -55.5}, {"time": 0.2, "x": -6.25, "y": -89.42}, {"time": 0.2667, "x": -5.2, "y": -87.32}, {"time": 0.3333, "x": -10.52, "y": -56.52}, {"time": 0.4, "x": -21.16, "y": -55.5}, {"time": 0.4333, "x": -6.25, "y": -89.42}, {"time": 0.5, "x": -5.2, "y": -87.32}]}, "ALL64": {"translate": [{"x": 61.46, "y": -3.87}, {"time": 0.2, "x": -51.63, "y": 41.15}, {"time": 0.4667, "x": 78.37, "y": -10.6}, {"time": 0.5, "x": 61.46, "y": -3.87}]}, "ALL66": {"translate": [{"x": -21.15, "y": 8.08}, {"time": 0.1333, "x": -65.95, "y": -22.96}, {"time": 0.3667, "x": 32.31, "y": 45.12}, {"time": 0.5, "x": -21.15, "y": 8.08}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL11": {"rotate": [{"angle": -1.41}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL37": {"rotate": [{"angle": 3.5}, {"time": 0.2, "angle": 17.26}, {"time": 0.4333}, {"time": 0.5, "angle": 3.5}]}, "ALL36": {"rotate": [{"angle": 12.37}, {"time": 0.1667}, {"time": 0.4333, "angle": 17.26}, {"time": 0.5, "angle": 12.37}]}}}, "idle": {"bones": {"ALL69": {"rotate": [{"angle": -30.09}], "translate": [{"x": -9.12, "y": 11.84}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL6": {"rotate": [{"angle": -2.9}]}, "ALL5": {"rotate": [{}, {"time": 0.5, "angle": 0.14}, {"time": 1}], "scale": [{}, {"time": 0.5, "x": 0.942, "y": 1.02}, {"time": 1}]}, "ALL2": {"translate": [{"y": 2.09}, {"time": 0.2667}, {"time": 0.7667, "y": 3.84}, {"time": 1, "y": 2.09}]}, "ALL8": {"rotate": [{"angle": 9.4}], "translate": [{"x": -6.12, "y": -6.15}, {"time": 0.5, "x": -5.17, "y": -6.23}, {"time": 1, "x": -6.12, "y": -6.15}]}, "ALL64": {"translate": [{"x": 61.46, "y": -3.87}, {"time": 0.4, "x": -51.63, "y": 41.15}, {"time": 0.9, "x": 78.37, "y": -10.6}, {"time": 1, "x": 61.46, "y": -3.87}]}, "ALL66": {"translate": [{"x": -21.15, "y": 8.08}, {"time": 0.2333, "x": -65.95, "y": -22.96}, {"time": 0.7333, "x": 32.31, "y": 45.12}, {"time": 1, "x": -21.15, "y": 8.08}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL11": {"rotate": [{"angle": -1.41}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL37": {"rotate": [{"angle": 3.5}, {"time": 0.3667, "angle": 17.26}, {"time": 0.8667}, {"time": 1, "angle": 3.5}]}, "ALL36": {"rotate": [{"angle": 12.37}, {"time": 0.3333}, {"time": 0.8333, "angle": 17.26}, {"time": 1, "angle": 12.37}]}}}, "idle_carry": {"bones": {"ALL69": {"rotate": [{"angle": 58.21}], "translate": [{"x": 20.29, "y": -12.14}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL6": {"rotate": [{"angle": -2.9}]}, "ALL5": {"rotate": [{}, {"time": 0.5, "angle": 0.14}, {"time": 1}], "scale": [{}, {"time": 0.5, "x": 0.942, "y": 1.02}, {"time": 1}]}, "ALL2": {"translate": [{"y": 2.09}, {"time": 0.2667}, {"time": 0.7667, "y": 3.84}, {"time": 1, "y": 2.09}]}, "ALL8": {"rotate": [{"angle": 93.03}], "translate": [{"x": 10.05, "y": -71.53}]}, "ALL64": {"translate": [{"x": 61.46, "y": -3.87}, {"time": 0.4, "x": -51.63, "y": 41.15}, {"time": 0.9, "x": 78.37, "y": -10.6}, {"time": 1, "x": 61.46, "y": -3.87}]}, "ALL66": {"translate": [{"x": -21.15, "y": 8.08}, {"time": 0.2333, "x": -65.95, "y": -22.96}, {"time": 0.7333, "x": 32.31, "y": 45.12}, {"time": 1, "x": -21.15, "y": 8.08}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL11": {"rotate": [{"angle": -1.41}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL37": {"rotate": [{"angle": 3.5}, {"time": 0.3667, "angle": 17.26}, {"time": 0.8667}, {"time": 1, "angle": 3.5}]}, "ALL36": {"rotate": [{"angle": 12.37}, {"time": 0.3333}, {"time": 0.8333, "angle": 17.26}, {"time": 1, "angle": 12.37}]}}}, "idle_help": {"bones": {"ALL69": {"rotate": [{"angle": 106.43}], "translate": [{"x": 88.63, "y": -4.04}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL6": {"rotate": [{"angle": -2.9}], "translate": [{"x": -11.39, "y": 5.79}]}, "ALL5": {"rotate": [{"angle": 12.64}], "scale": [{}, {"time": 0.5, "x": 0.942, "y": 1.02}, {"time": 1}]}, "ALL2": {"translate": [{"y": 2.09}, {"time": 0.2667}, {"time": 0.7667, "y": 3.84}, {"time": 1, "y": 2.09}]}, "ALL8": {"rotate": [{"angle": -156.33}], "translate": [{"x": 91.15, "y": -13.8}]}, "ALL64": {"translate": [{"x": 61.46, "y": -3.87}, {"time": 0.4, "x": -51.63, "y": 41.15}, {"time": 0.9, "x": 78.37, "y": -10.6}, {"time": 1, "x": 61.46, "y": -3.87}]}, "ALL66": {"translate": [{"x": -21.15, "y": 8.08}, {"time": 0.2333, "x": -65.95, "y": -22.96}, {"time": 0.7333, "x": 32.31, "y": 45.12}, {"time": 1, "x": -21.15, "y": 8.08}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL11": {"rotate": [{"angle": -1.41}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL37": {"rotate": [{"angle": 3.5}, {"time": 0.3667, "angle": 17.26}, {"time": 0.8667}, {"time": 1, "angle": 3.5}]}, "ALL36": {"rotate": [{"angle": 12.37}, {"time": 0.3333}, {"time": 0.8333, "angle": 17.26}, {"time": 1, "angle": 12.37}]}, "ren": {"rotate": [{"angle": 5.11}], "translate": [{"x": -33, "y": 288.06}]}}}, "read_book": {"slots": {"barb": {"attachment": [{"name": "barb6_1"}, {"time": 1.7667, "name": "barb6"}, {"time": 3.6667, "name": "barb6_1"}]}, "archer16": {"attachment": [{"name": "Dead"}, {"time": 2.0333, "name": "archer16_1"}, {"time": 3.3667, "name": "Dead"}]}}, "bones": {"ALL58": {"rotate": [{"angle": 44.93, "curve": "stepped"}, {"time": 1.3333, "angle": 44.93}, {"time": 1.7333, "angle": 24.69}, {"time": 2.0333, "angle": 33.37, "curve": "stepped"}, {"time": 3.3667, "angle": 33.37}, {"time": 3.6667, "angle": 24.69}, {"time": 4.0667, "angle": 44.93}], "translate": [{"x": 27.86, "y": 9.72, "curve": "stepped"}, {"time": 1.3333, "x": 27.86, "y": 9.72}, {"time": 1.7333, "x": 15.77, "y": 1.47}, {"time": 2.0333, "x": 2.25, "y": 3.36, "curve": "stepped"}, {"time": 3.3667, "x": 2.25, "y": 3.36}, {"time": 3.6667, "x": 15.77, "y": 1.47}, {"time": 4.0667, "x": 27.86, "y": 9.72}]}, "ALL16": {"rotate": [{"angle": -58.28}], "translate": [{"x": 0.15, "y": -5.9}]}, "ALL13": {"rotate": [{"angle": 108.68}], "translate": [{"x": 7.41, "y": -21.68}]}, "ALL36": {"rotate": [{"angle": 12.37}, {"time": 0.4333}, {"time": 1.1, "angle": 17.26}, {"time": 1.3333, "angle": 12.37, "curve": "stepped"}, {"time": 2.0333, "angle": 12.37}, {"time": 2.4667}, {"time": 3.1333, "angle": 17.26}, {"time": 3.3667, "angle": 12.37, "curve": "stepped"}, {"time": 4.0667, "angle": 12.37}, {"time": 4.5}, {"time": 5.1667, "angle": 17.26}, {"time": 5.4, "angle": 12.37}]}, "ALL2": {"rotate": [{"angle": 4.03}], "translate": [{"x": -18.65, "y": -32.32, "curve": "stepped"}, {"time": 1.3333, "x": -18.65, "y": -32.32}, {"time": 1.7333, "x": -16.08, "y": -31.03}, {"time": 2.0333, "x": -18.65, "y": -32.32, "curve": "stepped"}, {"time": 3.3667, "x": -18.65, "y": -32.32}, {"time": 3.6667, "x": -16.08, "y": -31.03}, {"time": 4.0667, "x": -18.65, "y": -32.32}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL8": {"rotate": [{"angle": 81.02, "curve": "stepped"}, {"time": 1.7333, "angle": 81.02}, {"time": 2.0333, "angle": 63.62, "curve": "stepped"}, {"time": 3.3667, "angle": 63.62}, {"time": 3.6667, "angle": 81.02}], "translate": [{"x": -19.96, "y": -41.15}, {"time": 0.6667, "x": -16.88, "y": -40.49}, {"time": 1.3333, "x": -19.96, "y": -41.15}, {"time": 1.7333, "x": 0.36, "y": -74.67}, {"time": 2.0333, "x": -17.57, "y": -42.99}, {"time": 2.7, "x": -16.48, "y": -42.76}, {"time": 3.3667, "x": -17.57, "y": -42.99}, {"time": 3.6667, "x": 0.36, "y": -74.67}, {"time": 4.0667, "x": -19.96, "y": -41.15}, {"time": 4.7333, "x": -16.88, "y": -40.49}, {"time": 5.4, "x": -19.96, "y": -41.15}]}, "ALL9": {"rotate": [{"angle": 2.75}], "translate": [{"x": 11.56, "y": 0.97}]}, "ALL64": {"translate": [{"x": -103.97, "y": -1.45}, {"time": 0.1667, "x": -92.53, "y": -96.46}, {"time": 0.5, "x": -169.41, "y": 104.4}, {"time": 0.6667, "x": -103.97, "y": -1.45}, {"time": 0.8333, "x": -69.72, "y": -88.96}, {"time": 1.1667, "x": -168.83, "y": 101.11}, {"time": 1.3333, "x": -103.97, "y": -1.45}, {"time": 1.7333, "x": -176.42, "y": -73.84}, {"time": 2.0333, "x": 61.46, "y": -3.87}, {"time": 2.5667, "x": -51.63, "y": 41.15}, {"time": 3.2333, "x": 78.37, "y": -10.6}, {"time": 3.3667, "x": 61.46, "y": -3.87}, {"time": 3.6667, "x": -176.42, "y": -73.84}, {"time": 4.0667, "x": -103.97, "y": -1.45}, {"time": 4.2333, "x": -92.53, "y": -96.46}, {"time": 4.5667, "x": -169.41, "y": 104.4}, {"time": 4.7333, "x": -103.97, "y": -1.45}, {"time": 4.9, "x": -69.72, "y": -88.96}, {"time": 5.2333, "x": -168.83, "y": 101.11}, {"time": 5.4, "x": -103.97, "y": -1.45}]}, "ALL4": {"rotate": [{"angle": -16.82, "curve": "stepped"}, {"time": 1.3333, "angle": -16.82}, {"time": 1.7333, "angle": -16.57}, {"time": 2.0333, "angle": -16.82, "curve": "stepped"}, {"time": 3.3667, "angle": -16.82}, {"time": 3.6667, "angle": -16.57}, {"time": 4.0667, "angle": -16.82}]}, "ALL69": {"rotate": [{"angle": 30.61, "curve": "stepped"}, {"time": 1.7333, "angle": 30.61}, {"time": 2.0333, "angle": -1.97, "curve": "stepped"}, {"time": 3.3667, "angle": -1.97}, {"time": 3.6667, "angle": 30.61}], "translate": [{"x": 31.65, "y": -18.59}, {"time": 0.6667, "x": 34.73, "y": -17.93}, {"time": 1.3333, "x": 31.65, "y": -18.59}, {"time": 1.7333, "x": 18.06, "y": 12.75}, {"time": 2.0333, "x": 24.55, "y": -7.2}, {"time": 2.7, "x": 25.64, "y": -6.97}, {"time": 3.3667, "x": 24.55, "y": -7.2}, {"time": 3.6667, "x": 18.06, "y": 12.75}, {"time": 4.0667, "x": 31.65, "y": -18.59}, {"time": 4.7333, "x": 34.73, "y": -17.93}, {"time": 5.4, "x": 31.65, "y": -18.59}]}, "ALL66": {"translate": [{"x": -21.15, "y": 8.08}, {"time": 0.3, "x": -65.95, "y": -22.96}, {"time": 0.9667, "x": 32.31, "y": 45.12}, {"time": 1.3333, "x": -21.15, "y": 8.08, "curve": "stepped"}, {"time": 2.0333, "x": -21.15, "y": 8.08}, {"time": 2.3333, "x": -65.95, "y": -22.96}, {"time": 3, "x": 32.31, "y": 45.12}, {"time": 3.3667, "x": -21.15, "y": 8.08, "curve": "stepped"}, {"time": 4.0667, "x": -21.15, "y": 8.08}, {"time": 4.3667, "x": -65.95, "y": -22.96}, {"time": 5.0333, "x": 32.31, "y": 45.12}, {"time": 5.4, "x": -21.15, "y": 8.08}]}, "ALL3": {"rotate": [{"angle": -4.14, "curve": "stepped"}, {"time": 1.3333, "angle": -4.14}, {"time": 1.7333, "angle": -7.48}, {"time": 2.0333, "angle": -4.14, "curve": "stepped"}, {"time": 3.3667, "angle": -4.14}, {"time": 3.6667, "angle": -7.48}, {"time": 4.0667, "angle": -4.14}]}, "ALL6": {"rotate": [{"angle": -2.9}], "translate": [{"x": -12.2, "y": 3.42}]}, "ALL30": {"scale": [{}, {"time": 0.6667, "x": 1.202, "y": 1.202}, {"time": 1.3333, "curve": "stepped"}, {"time": 4.0667}, {"time": 4.7333, "x": 1.202, "y": 1.202}, {"time": 5.4}]}, "ALL37": {"rotate": [{"angle": 3.5}, {"time": 0.5, "angle": 17.26}, {"time": 1.1667}, {"time": 1.3333, "angle": 3.5, "curve": "stepped"}, {"time": 2.0333, "angle": 3.5}, {"time": 2.5333, "angle": 17.26}, {"time": 3.2}, {"time": 3.3667, "angle": 3.5, "curve": "stepped"}, {"time": 4.0667, "angle": 3.5}, {"time": 4.5667, "angle": 17.26}, {"time": 5.2333}, {"time": 5.4, "angle": 3.5}]}, "ALL5": {"rotate": [{"angle": 16.1, "curve": "stepped"}, {"time": 1.3333, "angle": 16.1}, {"time": 1.7333, "angle": 17.18}, {"time": 2.0333, "angle": 16.1, "curve": "stepped"}, {"time": 3.3667, "angle": 16.1}, {"time": 3.6667, "angle": 17.18}, {"time": 4.0667, "angle": 16.1}], "translate": [{"x": -6.61, "y": -11.37, "curve": "stepped"}, {"time": 1.3333, "x": -6.61, "y": -11.37}, {"time": 1.7333, "x": -10.47, "y": -14.82}, {"time": 2.0333, "x": -6.48, "y": -5.48}, {"time": 2.7, "x": -6.61, "y": -11.37, "curve": "stepped"}, {"time": 3.3667, "x": -6.61, "y": -11.37}, {"time": 3.6667, "x": -10.47, "y": -14.82}, {"time": 4.0667, "x": -6.61, "y": -11.37}], "scale": [{}, {"time": 0.6667, "x": 0.914}, {"time": 1.3333}, {"time": 1.5667, "x": 0.88}, {"time": 1.8, "x": 1.04}, {"time": 2.0333}, {"time": 2.7, "x": 0.914}, {"time": 3.3667}, {"time": 3.6, "x": 0.88}, {"time": 3.8333, "x": 1.04}, {"time": 4.0667}, {"time": 4.7333, "x": 0.914}, {"time": 5.4}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL15": {"rotate": [{"angle": -1.67}], "scale": [{"x": 0.858}]}, "ALL14": {"rotate": [{"angle": 0.86}], "scale": [{"x": 1.742}]}, "ALL12": {"rotate": [{"angle": 2.61}], "scale": [{"x": 1.569}]}, "ALL11": {"rotate": [{"angle": -1.41}], "scale": [{"x": 1.203}]}}, "ik": {"ALL13": [{}], "ALL16": [{"bendPositive": false}]}, "deform": {"default": {"barb": {"barb6_1": [{"time": 1.3333}, {"time": 1.7333, "offset": 4, "vertices": [87.26486, -25.28264, 87.26486, -25.28264], "curve": "stepped"}, {"time": 3.6667, "offset": 4, "vertices": [87.26486, -25.28264, 87.26486, -25.28264]}, {"time": 4.0667}]}}}, "drawOrder": [{"offsets": [{"slot": "barb", "offset": 7}]}]}, "run": {"bones": {"ALL6": {"rotate": [{"angle": -2.9}]}, "ALL69": {"rotate": [{"angle": -1.55}, {"time": 0.4333, "angle": 8.03}, {"time": 0.8333, "angle": -1.55}], "translate": [{"x": -5.76, "y": 40.55}, {"time": 0.4333, "x": 4.37, "y": -1.9}, {"time": 0.8333, "x": -5.76, "y": 40.55}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL8": {"rotate": [{"angle": 56.45}, {"time": 0.4333, "angle": 9.4}, {"time": 0.8333, "angle": 56.45}], "translate": [{"x": -4.4, "y": -58.05}, {"time": 0.4333, "x": -6.12, "y": -6.15}, {"time": 0.8333, "x": -4.4, "y": -58.05}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL64": {"translate": [{"x": 118.22, "y": -176.65}, {"time": 0.1333, "x": 139.68, "y": -235.61}, {"time": 0.5667, "x": 64.05, "y": -27.84}, {"time": 0.8333, "x": 118.22, "y": -176.65}]}, "ALL5": {"rotate": [{}, {"time": 0.4333, "angle": 7.5}, {"time": 0.8333}], "scale": [{}, {"time": 0.2, "x": 0.942, "y": 1.02}, {"time": 0.4333}, {"time": 0.6333, "x": 0.942, "y": 1.02}, {"time": 0.8333}]}, "ALL2": {"rotate": [{"angle": -4.58}], "translate": [{"x": 1.22, "y": -0.56}, {"time": 0.0667, "x": 1.22, "y": -3.42}, {"time": 0.2, "x": 1.22, "y": -8.93}, {"time": 0.3, "x": 1.22, "y": -1.89}, {"time": 0.4333, "x": 1.22, "y": -0.56}, {"time": 0.4667, "x": 1.22, "y": -3.42}, {"time": 0.6333, "x": 1.22, "y": -8.93}, {"time": 0.7333, "x": 1.22, "y": -1.89}, {"time": 0.8333, "x": 1.22, "y": -0.56}]}, "ALL13": {"rotate": [{"angle": -11.47}, {"time": 0.0667, "angle": -44.51}, {"time": 0.2, "angle": -15.24}, {"time": 0.3}, {"time": 0.4333, "angle": 31.13}, {"time": 0.4667, "angle": 43.73}, {"time": 0.6333, "angle": -3.73}, {"time": 0.7333}, {"time": 0.8333, "angle": -11.47}], "translate": [{"x": -15.91}, {"time": 0.0667, "x": -15.3, "y": 5.81}, {"time": 0.2, "x": 9.79, "y": 5.81}, {"time": 0.3, "x": 23.26, "y": 22.95}, {"time": 0.4333, "x": 36.11, "y": 11.02}, {"time": 0.4667, "x": 43.45, "y": -0.92}, {"time": 0.6333, "x": 6.25, "y": -1.97}, {"time": 0.7333, "x": -10.9, "y": -1.3}, {"time": 0.8333, "x": -15.91}]}, "ALL16": {"rotate": [{}, {"time": 0.0667, "angle": 30.67}, {"time": 0.2, "angle": -1.7}, {"time": 0.3}, {"time": 0.4333, "angle": -19.88}, {"time": 0.4667, "angle": -49.75}, {"time": 0.6333, "angle": -67.73}, {"time": 0.7333}], "translate": [{"x": 8.87, "y": 13.77}, {"time": 0.0667, "x": 15.91, "y": -0.92}, {"time": 0.2, "x": -20.2, "y": -0.92}, {"time": 0.3, "x": -41.62, "y": -1.53}, {"time": 0.4333, "x": -44.98, "y": 1.84}, {"time": 0.4667, "x": -40.39, "y": 5.51}, {"time": 0.6333, "x": -16.83, "y": 4.21}, {"time": 0.7333, "x": -4.27, "y": 18.65}, {"time": 0.8333, "x": 8.87, "y": 13.77}]}, "ALL11": {"rotate": [{"angle": -1.41}]}, "ALL37": {"rotate": [{"angle": 3.5}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL36": {"rotate": [{"angle": 12.37}]}, "ALL66": {"translate": [{"x": 220.04, "y": 48.71}, {"time": 0.4333, "x": 2.88, "y": 10.01}, {"time": 0.8333, "x": 220.04, "y": 48.71}]}}, "ik": {"ALL16": [{"bendPositive": false}]}}, "run_help": {"bones": {"ALL6": {"rotate": [{"angle": -2.9}], "translate": [{"x": -11.39, "y": 5.79}]}, "ALL69": {"rotate": [{"angle": 106.43}], "translate": [{"x": 88.63, "y": -4.04}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL8": {"rotate": [{"angle": -156.33}], "translate": [{"x": 91.15, "y": -13.8}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL64": {"translate": [{"x": 118.22, "y": -176.65}, {"time": 0.1333, "x": 139.68, "y": -235.61}, {"time": 0.5667, "x": 64.05, "y": -27.84}, {"time": 0.8333, "x": 118.22, "y": -176.65}]}, "ALL5": {"rotate": [{"angle": 17.84}], "translate": [{"x": -0.68, "y": -5.63}], "scale": [{}, {"time": 0.2, "x": 0.942, "y": 1.02}, {"time": 0.4333}, {"time": 0.6333, "x": 0.942, "y": 1.02}, {"time": 0.8333}]}, "ALL2": {"rotate": [{"angle": 2.08}], "translate": [{"x": 1.22, "y": -0.56}, {"time": 0.0667, "x": 1.22, "y": -3.42}, {"time": 0.2, "x": 1.22, "y": -8.93}, {"time": 0.3, "x": 1.22, "y": -1.89}, {"time": 0.4333, "x": 1.22, "y": -0.56}, {"time": 0.4667, "x": 1.22, "y": -3.42}, {"time": 0.6333, "x": 1.22, "y": -8.93}, {"time": 0.7333, "x": 1.22, "y": -1.89}, {"time": 0.8333, "x": 1.22, "y": -0.56}]}, "ALL13": {"rotate": [{"angle": -11.47}, {"time": 0.0667, "angle": -44.51}, {"time": 0.2, "angle": -15.24}, {"time": 0.3}, {"time": 0.4333, "angle": 31.13}, {"time": 0.4667, "angle": 43.73}, {"time": 0.6333, "angle": -3.73}, {"time": 0.7333}, {"time": 0.8333, "angle": -11.47}], "translate": [{"x": -15.91}, {"time": 0.0667, "x": -15.3, "y": 5.81}, {"time": 0.2, "x": 9.79, "y": 5.81}, {"time": 0.3, "x": 23.26, "y": 22.95}, {"time": 0.4333, "x": 36.11, "y": 11.02}, {"time": 0.4667, "x": 43.45, "y": -0.92}, {"time": 0.6333, "x": 6.25, "y": -1.97}, {"time": 0.7333, "x": -10.9, "y": -1.3}, {"time": 0.8333, "x": -15.91}]}, "ALL16": {"rotate": [{}, {"time": 0.0667, "angle": 30.67}, {"time": 0.2, "angle": -1.7}, {"time": 0.3}, {"time": 0.4333, "angle": -19.88}, {"time": 0.4667, "angle": -49.75}, {"time": 0.6333, "angle": -67.73}, {"time": 0.7333}], "translate": [{"x": 8.87, "y": 13.77}, {"time": 0.0667, "x": 15.91, "y": -0.92}, {"time": 0.2, "x": -20.2, "y": -0.92}, {"time": 0.3, "x": -41.62, "y": -1.53}, {"time": 0.4333, "x": -44.98, "y": 1.84}, {"time": 0.4667, "x": -40.39, "y": 5.51}, {"time": 0.6333, "x": -16.83, "y": 4.21}, {"time": 0.7333, "x": -4.27, "y": 18.65}, {"time": 0.8333, "x": 8.87, "y": 13.77}]}, "ALL11": {"rotate": [{"angle": -1.41}]}, "ALL37": {"rotate": [{"angle": 3.5}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL36": {"rotate": [{"angle": 12.37}]}, "ALL66": {"translate": [{"x": 220.04, "y": 48.71}, {"time": 0.4333, "x": 2.88, "y": 10.01}, {"time": 0.8333, "x": 220.04, "y": 48.71}]}, "ren": {"rotate": [{"angle": 5.11}], "translate": [{"x": -33, "y": 288.06}]}}, "ik": {"ALL16": [{"bendPositive": false}]}}, "sit_chair": {"bones": {"ALL64": {"translate": [{"x": 61.46, "y": -3.87}, {"time": 1.3333, "x": -60.94, "y": 524.05}, {"time": 2.6667, "x": 61.46, "y": -3.87}]}, "ALL4": {"rotate": [{"angle": -7.96}]}, "ALL69": {"rotate": [{"angle": -30.09}], "translate": [{"x": -9.12, "y": 11.84}]}, "ALL66": {"translate": [{"x": -21.15, "y": 8.08}, {"time": 0.3, "x": -65.95, "y": -22.96}, {"time": 0.9667, "x": 32.31, "y": 45.12}, {"time": 1.3333, "x": -21.15, "y": 8.08}, {"time": 1.6333, "x": -65.95, "y": -22.96}, {"time": 2.3, "x": 32.31, "y": 45.12}, {"time": 2.6667, "x": -21.15, "y": 8.08}]}, "ALL3": {"rotate": [{"angle": -3.6}]}, "ALL5": {"rotate": [{}, {"time": 0.7667, "angle": 7.42}, {"time": 1.8333, "angle": -5.03}, {"time": 2.6667}], "scale": [{}, {"time": 0.6667, "x": 0.942, "y": 1.02}, {"time": 1.3333}, {"time": 2, "x": 0.942, "y": 1.02}, {"time": 2.6667}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL6": {"rotate": [{"angle": -2.9}]}, "ALL31": {"translate": [{}, {"time": 1.3333, "x": 0.74, "y": -4.76}, {"time": 2.6667}]}, "ALL30": {"translate": [{}, {"time": 1.3333, "x": 1.77, "y": -11.33}, {"time": 2.6667}]}, "ALL2": {"rotate": [{"angle": 11.79}], "translate": [{"x": -18.65, "y": -32.32}]}, "ALL16": {"rotate": [{"angle": 18.02}, {"time": 0.3333, "angle": 44.06}, {"time": 0.6667, "angle": 18.02}, {"time": 1, "angle": -17.28}, {"time": 1.3333, "angle": 18.02}, {"time": 1.6667, "angle": 44.06}, {"time": 2, "angle": 18.02}, {"time": 2.3333, "angle": -17.28}, {"time": 2.6667, "angle": 18.02}], "translate": [{"x": 6.53, "y": -13.52}, {"time": 0.3333, "x": 11.83, "y": -9.81}, {"time": 0.6667, "x": 6.53, "y": -13.52}, {"time": 1, "x": -7.78, "y": -17.76}, {"time": 1.3333, "x": 6.53, "y": -13.52}, {"time": 1.6667, "x": 11.83, "y": -9.81}, {"time": 2, "x": 6.53, "y": -13.52}, {"time": 2.3333, "x": -7.78, "y": -17.76}, {"time": 2.6667, "x": 6.53, "y": -13.52}]}, "ALL13": {"rotate": [{"angle": 13.1}, {"time": 0.3333, "angle": -0.75}, {"time": 0.6667, "angle": 13.1}, {"time": 1, "angle": 44.11}, {"time": 1.3333, "angle": 13.1}, {"time": 1.6667, "angle": -0.75}, {"time": 2, "angle": 13.1}, {"time": 2.3333, "angle": 44.11}, {"time": 2.6667, "angle": 13.1}], "translate": [{"x": 5.45, "y": -25.16}, {"time": 0.3333, "x": -6.21, "y": -26.22}, {"time": 0.6667, "x": 5.45, "y": -25.16}, {"time": 1, "x": 4.65, "y": -25.42}, {"time": 1.3333, "x": 5.45, "y": -25.16}, {"time": 1.6667, "x": -6.21, "y": -26.22}, {"time": 2, "x": 5.45, "y": -25.16}, {"time": 2.3333, "x": 4.65, "y": -25.42}, {"time": 2.6667, "x": 5.45, "y": -25.16}]}, "ALL8": {"rotate": [{"angle": 9.4}], "translate": [{"x": -7.93, "y": -17.56}, {"time": 0.6667, "x": -6.25, "y": -17.7}, {"time": 1.3333, "x": -7.93, "y": -17.56}, {"time": 2, "x": -6.25, "y": -17.7}, {"time": 2.6667, "x": -7.93, "y": -17.56}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL11": {"rotate": [{"angle": -1.41}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL37": {"rotate": [{"angle": 3.5}, {"time": 0.5, "angle": 17.26}, {"time": 1.1667}, {"time": 1.3333, "angle": 3.5}, {"time": 1.8333, "angle": 17.26}, {"time": 2.5}, {"time": 2.6667, "angle": 3.5}]}, "ALL36": {"rotate": [{"angle": 12.37}, {"time": 0.4333}, {"time": 1.1, "angle": 17.26}, {"time": 1.3333, "angle": 12.37}, {"time": 1.7667}, {"time": 2.4333, "angle": 17.26}, {"time": 2.6667, "angle": 12.37}]}}, "ik": {"ALL16": [{"bendPositive": false}]}, "deform": {"default": {"erduo": {"erduo_1": [{}, {"time": 1.3333, "vertices": [-1.16154, 7.45191, -1.16158, 7.45236, 0, 0, 0, 0, 0, 0, 0, 0, -1.16154, 7.45191, -1.16158, 7.45236]}, {"time": 2.6667}]}, "archer1": {"archer1_1": [{}, {"time": 1.3333, "offset": 12, "vertices": [-1.92506, 5.24105, -1.92519, 5.24146, -2.858, 5.53894, -2.85815, 5.53912, -2.54045, 4.92348, -2.54057, 4.92377, -0.34334, 2.38946, -0.34344, 2.3894, -3.32893, 5.99744, -3.32912, 5.99725, -1.85518, 8.22776, -1.85533, 8.2276, -0.85217, -1.45779, -0.85223, -1.45758, 1.61431, -7.91058, 1.61422, -7.91043, 2.79562, -11.01091, 2.79539, -11.01102, 2.13667, -5.27785, 2.13665, -5.27774, -0.44787, -0.10277, -0.44789, -0.10297, 0.33769, -0.88696, 0.33763, -0.88684, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.95578, 1.02624, -1.95584, 1.02637, -2.27158, 3.22366, -2.27164, 3.22357, -0.56952, -3.67744, -0.56951, -3.67755, 1.1995, -7.98222, 1.19934, -7.98221, 1.08043, -7.32881, 1.08035, -7.3287, -1.68948, 1.07146, -1.68954, 1.07156, -2.07491, 8.1962, -2.07512, 8.19635, -3.4552, 6.87642, -3.45537, 6.8764, -0.34334, 2.38946, -0.34344, 2.3894, -1.50634, 3.97646, -1.50644, 3.97662, -2.77659, 6.43818, -2.7767, 6.43842, -1.16068, 6.02509, -1.16086, 6.02527, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.76105, -1.04487, -0.76103, -1.04507]}, {"time": 2.6667}]}, "archer18": {"archer18_7": [{}, {"time": 1.3333, "offset": 20, "vertices": [0.1375, 4.66695, 0.13739, 4.66736, -1.07637, 1.86583, -1.0764, 1.86584, 0, 0, 0, 0, 0, 0, 0, 0, 0.44691, 6.73189, 0.44682, 6.73224, 0.44691, 6.73189, 0.44682, 6.73224, 0.44691, 6.73189, 0.44682, 6.73224, -1.72968, 5.00479, -1.72964, 5.00476, -1.36205, 5.25969, -1.36209, 5.25983, -0.3154, 5.00598, -0.31544, 5.0061]}, {"time": 2.6667}]}, "archer4": {"archer4": [{"offset": 22, "vertices": [-0.03033, -2.51537, 0.6277, -2.43597, -1.53028, -1.99656, 0.13129, -3.02048, 0.91564, -2.88134, -1.70325, -2.49789, 0.32663, -0.7307, 0.50614, -0.62002, -0.17586, -0.78082]}, {"time": 0.3333, "offset": 22, "vertices": [0.49741, -2.47151, 1.1545, -2.48992, -1.38089, -2.50463, 0.23479, -3.82178, 0.86998, -3.688, -2.46721, -2.76084, 2.16288, -4.37755, 1.64033, -4.54237, -3.37755, -3.31466, 0.56269, -3.06551, -0.01073, -3.1167, -2.88906, -1.16922, 0.2115, -0.31199, 0.15051, -0.34557, -0.26293, -0.27007]}, {"time": 0.6667, "offset": 22, "vertices": [-0.03033, -2.51537, 0.6277, -2.43597, -1.53028, -1.99656, 0.13129, -3.02048, 0.91564, -2.88134, -1.70325, -2.49789, 0.32663, -0.7307, 0.50614, -0.62002, -0.17586, -0.78082]}, {"time": 1}, {"time": 1.3333, "offset": 22, "vertices": [-0.03033, -2.51537, 0.6277, -2.43597, -1.53028, -1.99656, 0.13129, -3.02048, 0.91564, -2.88134, -1.70325, -2.49789, 0.32663, -0.7307, 0.50614, -0.62002, -0.17586, -0.78082]}, {"time": 1.6667, "offset": 22, "vertices": [0.49741, -2.47151, 1.1545, -2.48992, -1.38089, -2.50463, 0.23479, -3.82178, 0.86998, -3.688, -2.46721, -2.76084, 2.16288, -4.37755, 1.64033, -4.54237, -3.37755, -3.31466, 0.56269, -3.06551, -0.01073, -3.1167, -2.88906, -1.16922, 0.2115, -0.31199, 0.15051, -0.34557, -0.26293, -0.27007]}, {"time": 2, "offset": 22, "vertices": [-0.03033, -2.51537, 0.6277, -2.43597, -1.53028, -1.99656, 0.13129, -3.02048, 0.91564, -2.88134, -1.70325, -2.49789, 0.32663, -0.7307, 0.50614, -0.62002, -0.17586, -0.78082]}, {"time": 2.3333}, {"time": 2.6667, "offset": 22, "vertices": [-0.03033, -2.51537, 0.6277, -2.43597, -1.53028, -1.99656, 0.13129, -3.02048, 0.91564, -2.88134, -1.70325, -2.49789, 0.32663, -0.7307, 0.50614, -0.62002, -0.17586, -0.78082]}]}}}, "drawOrder": [{"offsets": [{"slot": "barb", "offset": 12}]}]}, "sit_land_1": {"bones": {"ALL64": {"translate": [{"x": 61.46, "y": -3.87}, {"time": 0.5333, "x": -51.63, "y": 41.15}, {"time": 1.2, "x": 78.37, "y": -10.6}, {"time": 1.3333, "x": 61.46, "y": -3.87}]}, "ALL4": {"rotate": [{"angle": -7.96}]}, "ALL69": {"rotate": [{"angle": -30.09}], "translate": [{"x": -9.12, "y": 11.84}]}, "ALL66": {"translate": [{"x": -21.15, "y": 8.08}, {"time": 0.3, "x": -65.95, "y": -22.96}, {"time": 0.9667, "x": 32.31, "y": 45.12}, {"time": 1.3333, "x": -21.15, "y": 8.08}]}, "ALL3": {"rotate": [{"angle": -3.6}]}, "ALL5": {"rotate": [{}, {"time": 0.6667, "angle": 0.14}, {"time": 1.3333}], "scale": [{}, {"time": 0.6667, "x": 0.942, "y": 1.02}, {"time": 1.3333}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL6": {"rotate": [{"angle": -2.9}]}, "ALL2": {"rotate": [{"angle": 11.79}], "translate": [{"x": -18.65, "y": -32.32}]}, "ALL16": {"rotate": [{"angle": 95.83}], "translate": [{"x": 23.75}]}, "ALL13": {"rotate": [{"angle": 51.77}], "translate": [{"x": 11.54, "y": -21.98}]}, "ALL8": {"rotate": [{"angle": 9.4}], "translate": [{"x": -7.93, "y": -17.56}, {"time": 0.6667, "x": -6.25, "y": -17.7}, {"time": 1.3333, "x": -7.93, "y": -17.56}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL11": {"rotate": [{"angle": -1.41}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL37": {"rotate": [{"angle": 3.5}, {"time": 0.5, "angle": 17.26}, {"time": 1.1667}, {"time": 1.3333, "angle": 3.5}]}, "ALL36": {"rotate": [{"angle": 12.37}, {"time": 0.4333}, {"time": 1.1, "angle": 17.26}, {"time": 1.3333, "angle": 12.37}]}}}, "sit_land_2": {"bones": {"ALL64": {"translate": [{"x": 61.46, "y": -3.87}, {"time": 0.5333, "x": -51.63, "y": 41.15}, {"time": 1.2, "x": 78.37, "y": -10.6}, {"time": 1.3333, "x": 61.46, "y": -3.87}]}, "ALL4": {"rotate": [{"angle": -16.82}]}, "ALL69": {"rotate": [{"angle": -30.09}], "translate": [{"x": 20.66, "y": -3.2}, {"time": 0.6667, "x": 14.56, "y": 2.73}, {"time": 1.3333, "x": 20.66, "y": -3.2}]}, "ALL66": {"translate": [{"x": -21.15, "y": 8.08}, {"time": 0.3, "x": -65.95, "y": -22.96}, {"time": 0.9667, "x": 32.31, "y": 45.12}, {"time": 1.3333, "x": -21.15, "y": 8.08}]}, "ALL3": {"rotate": [{"angle": -4.14}]}, "ALL5": {"rotate": [{"angle": 16.1}], "translate": [{"x": -6.61, "y": -11.37}]}, "ALL9": {"rotate": [{"angle": 2.75}], "translate": [{"x": 11.56, "y": 0.97}]}, "ALL6": {"rotate": [{"angle": -2.9}], "translate": [{"x": -12.2, "y": 3.42}]}, "ALL2": {"rotate": [{"angle": 4.03}], "translate": [{"x": -18.65, "y": -32.32}]}, "ALL16": {"rotate": [{"angle": -58.28}], "translate": [{"x": 0.15, "y": -5.9}]}, "ALL13": {"rotate": [{"angle": 108.68}], "translate": [{"x": 7.41, "y": -21.68}]}, "ALL8": {"rotate": [{"angle": 81.02}], "translate": [{"x": -14.09, "y": -53.57}, {"time": 0.6667, "x": -6.94, "y": -64.41}, {"time": 1.3333, "x": -14.09, "y": -53.57}]}, "ALL15": {"rotate": [{"angle": -1.67}], "scale": [{"x": 0.858}]}, "ALL14": {"rotate": [{"angle": 0.86}], "scale": [{"x": 1.742}]}, "ALL12": {"rotate": [{"angle": 2.61}], "scale": [{"x": 1.569}]}, "ALL11": {"rotate": [{"angle": -1.41}], "scale": [{"x": 1.203}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL37": {"rotate": [{"angle": 3.5}, {"time": 0.5, "angle": 17.26}, {"time": 1.1667}, {"time": 1.3333, "angle": 3.5}]}, "ALL36": {"rotate": [{"angle": 12.37}, {"time": 0.4333}, {"time": 1.1, "angle": 17.26}, {"time": 1.3333, "angle": 12.37}]}}, "ik": {"ALL13": [{}], "ALL16": [{"bendPositive": false}]}}, "sit_land_3": {"bones": {"ALL64": {"translate": [{"x": 61.46, "y": -3.87}, {"time": 0.5333, "x": -51.63, "y": 41.15}, {"time": 1.2, "x": 78.37, "y": -10.6}, {"time": 1.3333, "x": 61.46, "y": -3.87}]}, "ALL4": {"rotate": [{"angle": -29.28}]}, "ALL69": {"rotate": [{"angle": 13.9}], "translate": [{"x": 34.16, "y": -8.52}]}, "ALL66": {"translate": [{"x": -21.15, "y": 8.08}, {"time": 0.3, "x": -65.95, "y": -22.96}, {"time": 0.9667, "x": 32.31, "y": 45.12}, {"time": 1.3333, "x": -21.15, "y": 8.08}]}, "ALL3": {"rotate": [{"angle": -4.47}]}, "ALL5": {"rotate": [{"angle": -4.35}], "scale": [{}, {"time": 0.6667, "x": 0.942, "y": 1.02}, {"time": 1.3333}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL6": {"rotate": [{"angle": -2.9}]}, "ALL2": {"rotate": [{"angle": 32.77}], "translate": [{"x": -18.65, "y": -32.32}]}, "ALL16": {"rotate": [{"angle": 4.73}], "translate": [{"x": 16.35, "y": 2.22}]}, "ALL13": {"rotate": [{"angle": 51.77}], "translate": [{"x": 15.98, "y": -34.19}]}, "ALL8": {"rotate": [{"angle": -40.37}], "translate": [{"x": -24.9, "y": -6.07}]}, "ALL15": {"rotate": [{"angle": -1.67}], "scale": [{"x": 1.492, "y": 0.866}]}, "ALL14": {"rotate": [{"angle": 0.86}], "scale": [{"x": 1.484}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL11": {"rotate": [{"angle": -1.41}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL37": {"rotate": [{"angle": 3.5}, {"time": 0.5, "angle": 17.26}, {"time": 1.1667}, {"time": 1.3333, "angle": 3.5}]}, "ALL36": {"rotate": [{"angle": 12.37}, {"time": 0.4333}, {"time": 1.1, "angle": 17.26}, {"time": 1.3333, "angle": 12.37}]}}, "ik": {"ALL16": [{"bendPositive": false}]}, "deform": {"default": {"archer5": {"archer5": [{"offset": 60, "vertices": [21.24974, 6.13288, -4.76821, 31.76449, -0.35516, 2.4605, 3.79202, -0.01583, -0.21284, 4.36215, -2.46107, 5.05966, 0.25835, 5.62053, 0.5487, 9.50326, -6.37831, -0.3335, -8.8923, -3.44977, -9.4593, 1.22297, 0, 0, 0, 0, 0, 0, 0, 0, 7.57019, -4.59811, 2.62447, 9.17182, -0.81232, 12.10983, 5.078, 11.0237, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.08583, 1.14102, -1.14544, -0.07805, 2.90277, 3.32154, -3.13278, 4.44616]}]}}}}, "village_carry": {"bones": {"ALL6": {"rotate": [{"angle": -2.9}]}, "ALL69": {"rotate": [{"angle": 24.8}], "translate": [{"x": 17.62, "y": 2.88}, {"time": 0.4333, "x": 24.53, "y": 1.46}, {"time": 0.8333, "x": 17.62, "y": 2.88}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL8": {"rotate": [{"angle": 96.85}], "translate": [{"x": 5.89, "y": -58.84}, {"time": 0.4333, "x": 12.8, "y": -60.26}, {"time": 0.8333, "x": 5.89, "y": -58.84}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL64": {"translate": [{"x": 118.22, "y": -176.65}, {"time": 0.1333, "x": 139.68, "y": -235.61}, {"time": 0.5667, "x": 64.05, "y": -27.84}, {"time": 0.8333, "x": 118.22, "y": -176.65}]}, "ALL5": {"rotate": [{"angle": 9.58}, {"time": 0.4333, "angle": 9.84}, {"time": 0.8333, "angle": 9.58}], "scale": [{}, {"time": 0.2, "x": 0.942, "y": 1.02}, {"time": 0.4333}, {"time": 0.6333, "x": 0.942, "y": 1.02}, {"time": 0.8333}]}, "ALL2": {"rotate": [{"angle": 6.81}], "translate": [{"x": 1.22, "y": -0.56}, {"time": 0.0667, "x": 1.22, "y": -3.42}, {"time": 0.2, "x": 1.22, "y": -8.93}, {"time": 0.3, "x": 1.22, "y": -1.89}, {"time": 0.4333, "x": 1.22, "y": -0.56}, {"time": 0.4667, "x": 1.22, "y": -3.42}, {"time": 0.6333, "x": 1.22, "y": -8.93}, {"time": 0.7333, "x": 1.22, "y": -1.89}, {"time": 0.8333, "x": 1.22, "y": -0.56}]}, "ALL13": {"rotate": [{"angle": -11.47}, {"time": 0.0667, "angle": -44.51}, {"time": 0.2, "angle": -15.24}, {"time": 0.3}, {"time": 0.4333, "angle": 31.13}, {"time": 0.4667, "angle": 43.73}, {"time": 0.6333, "angle": -3.73}, {"time": 0.7333}, {"time": 0.8333, "angle": -11.47}], "translate": [{"x": -15.91}, {"time": 0.0667, "x": -15.3, "y": 5.81}, {"time": 0.2, "x": 9.79, "y": 5.81}, {"time": 0.3, "x": 23.26, "y": 22.95}, {"time": 0.4333, "x": 36.11, "y": 11.02}, {"time": 0.4667, "x": 43.45, "y": -0.92}, {"time": 0.6333, "x": 6.25, "y": -1.97}, {"time": 0.7333, "x": -10.9, "y": -1.3}, {"time": 0.8333, "x": -15.91}]}, "ALL16": {"rotate": [{}, {"time": 0.0667, "angle": 30.67}, {"time": 0.2, "angle": -1.7}, {"time": 0.3}, {"time": 0.4333, "angle": -19.88}, {"time": 0.4667, "angle": -49.75}, {"time": 0.6333, "angle": -67.73}, {"time": 0.7333}], "translate": [{"x": 8.87, "y": 13.77}, {"time": 0.0667, "x": 15.91, "y": -0.92}, {"time": 0.2, "x": -20.2, "y": -0.92}, {"time": 0.3, "x": -41.62, "y": -1.53}, {"time": 0.4333, "x": -44.98, "y": 1.84}, {"time": 0.4667, "x": -40.39, "y": 5.51}, {"time": 0.6333, "x": -16.83, "y": 4.21}, {"time": 0.7333, "x": -4.27, "y": 18.65}, {"time": 0.8333, "x": 8.87, "y": 13.77}]}, "ALL11": {"rotate": [{"angle": -1.41}]}, "ALL37": {"rotate": [{"angle": 3.5}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL36": {"rotate": [{"angle": 12.37}]}, "ALL66": {"translate": [{"x": 220.04, "y": 48.71}, {"time": 0.4333, "x": 2.88, "y": 10.01}, {"time": 0.8333, "x": 220.04, "y": 48.71}]}, "wuti": {"rotate": [{"angle": -10.99}]}}, "ik": {"ALL16": [{"bendPositive": false}]}}, "village_sleep_1": {"slots": {"Z3": {"color": [{"color": "fbffd8aa"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1.1667, "color": "f9ffc5ff"}, {"time": 1.3333, "color": "fbffd8aa"}], "attachment": [{"name": "Z"}]}, "img_v2_3b71fc62-e05e-495d-9d1b-f4ce67b9a12g": {"attachment": [{"name": "img_v2_3b71fc62-e05e-495d-9d1b-f4ce67b9a12g"}]}, "Z2": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 0.8333, "color": "f9ffc5ff"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "Z"}]}, "archer17": {"attachment": [{"name": "archer17_7"}]}, "archer16": {"attachment": [{"name": "Dead"}]}, "Z": {"color": [{"color": "ffffff00"}, {"time": 0.5, "color": "f9ffc5ff"}, {"time": 1, "color": "ffffff00"}], "attachment": [{"name": "Z"}]}}, "bones": {"ALL30": {"scale": [{"x": 0.613, "y": 0.883}, {"time": 0.5667, "x": 0.98, "y": 1.526}, {"time": 1.3333, "x": 0.613, "y": 0.883}]}, "ALL17": {"translate": [{"x": -311.75, "y": -97.11}]}, "ALL38": {"translate": [{"x": -132.66, "y": 12.21}, {"time": 1, "x": -98.34, "y": 87.45}], "scale": [{"x": 0.753, "y": 0.753}, {"time": 1, "x": 1.424, "y": 1.424}]}, "ALL39": {"translate": [{"time": 0.3333, "x": -132.66, "y": 12.21}, {"time": 1.3333, "x": -98.34, "y": 87.45}], "scale": [{"time": 0.3333, "x": 0.753, "y": 0.753}, {"time": 1.3333, "x": 1.424, "y": 1.424}]}, "ALL40": {"translate": [{"x": -109.78, "y": 62.37}, {"time": 0.3333, "x": -98.34, "y": 87.45, "curve": "stepped"}, {"time": 0.6667, "x": -132.66, "y": 12.21}, {"time": 1.3333, "x": -109.78, "y": 62.37}], "scale": [{"x": 1.2, "y": 1.2}, {"time": 0.3333, "x": 1.424, "y": 1.424, "curve": "stepped"}, {"time": 0.6667, "x": 0.753, "y": 0.753}, {"time": 1.3333, "x": 1.2, "y": 1.2}]}, "ALL2": {"rotate": [{"angle": 39.29}], "translate": [{"x": -6.19, "y": -27.21}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL16": {"rotate": [{"angle": 52.21}], "translate": [{"x": 26.11, "y": -2.7}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL69": {"rotate": [{"angle": -10.17}], "translate": [{"x": -7.71, "y": 14.37}]}, "ALL8": {"rotate": [{"angle": 9.4}], "translate": [{"x": -6.13, "y": -14.84}]}, "ALL36": {"rotate": [{"angle": 12.37}]}, "ALL13": {"rotate": [{"angle": 56.47}], "translate": [{"x": 29.47, "y": -30.21}]}, "ALL64": {"translate": [{"x": 61.46, "y": -3.87}, {"time": 0.6667, "x": 163.16, "y": -43.34}, {"time": 1.3333, "x": 61.46, "y": -3.87}]}, "ALL6": {"rotate": [{"angle": -2.9}]}, "ALL37": {"rotate": [{"angle": 3.5}]}, "ALL5": {"rotate": [{"angle": -6.83}, {"time": 0.6667, "angle": -8.75}, {"time": 1.3333, "angle": -6.83}], "scale": [{"x": 0.97, "y": 1.013}]}, "ALL66": {"translate": [{"x": -21.15, "y": 8.08}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL3": {"rotate": [{"angle": 4.31}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL11": {"rotate": [{"angle": -1.41}]}, "ALL26": {"translate": [{}, {"time": 0.6667, "x": 0.66, "y": -0.66}, {"time": 1.3333}]}, "ALL25": {"translate": [{}, {"time": 0.6667, "x": 3.62, "y": 3.62}, {"time": 1.3333}]}, "ALL23": {"translate": [{}, {"time": 0.6667, "x": 5.26, "y": 1.97}, {"time": 1.3333}]}, "ALL22": {"translate": [{}, {"time": 0.6667, "x": 5.26, "y": 6.58}, {"time": 1.3333}]}, "ALL21": {"translate": [{}, {"time": 0.6667, "y": 11}, {"time": 1.3333}]}, "ALL20": {"translate": [{}, {"time": 0.6667, "x": 1.32, "y": 0.99}, {"time": 1.3333}]}, "ALL19": {"translate": [{}, {"time": 0.6667, "x": 1.64, "y": 2.3}, {"time": 1.3333}]}, "ALL28": {"translate": [{}, {"time": 0.6667, "y": 9.43}, {"time": 1.3333}]}}, "ik": {"ALL16": [{"bendPositive": false}]}}, "village_sleep_2": {"slots": {"Z3": {"color": [{"color": "fbffd8aa"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1.1667, "color": "f9ffc5ff"}, {"time": 1.3333, "color": "fbffd8aa"}], "attachment": [{"name": "Z"}]}, "img_v2_3b71fc62-e05e-495d-9d1b-f4ce67b9a12g": {"attachment": [{"name": "img_v2_3b71fc62-e05e-495d-9d1b-f4ce67b9a12g"}]}, "Z2": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 0.8333, "color": "f9ffc5ff"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "Z"}]}, "archer17": {"attachment": [{"name": "archer17_7"}]}, "archer16": {"attachment": [{"name": "Dead"}]}, "Z": {"color": [{"color": "ffffff00"}, {"time": 0.5, "color": "f9ffc5ff"}, {"time": 1, "color": "ffffff00"}], "attachment": [{"name": "Z"}]}}, "bones": {"ALL30": {"scale": [{"x": 0.613, "y": 0.883}, {"time": 0.5667, "x": 0.98, "y": 1.526}, {"time": 1.3333, "x": 0.613, "y": 0.883}]}, "ALL17": {"translate": [{"x": -311.75, "y": -97.11}]}, "ALL38": {"translate": [{"x": -132.66, "y": 12.21}, {"time": 1, "x": -98.34, "y": 87.45}], "scale": [{"x": 0.753, "y": 0.753}, {"time": 1, "x": 1.424, "y": 1.424}]}, "ALL39": {"translate": [{"time": 0.3333, "x": -132.66, "y": 12.21}, {"time": 1.3333, "x": -98.34, "y": 87.45}], "scale": [{"time": 0.3333, "x": 0.753, "y": 0.753}, {"time": 1.3333, "x": 1.424, "y": 1.424}]}, "ALL40": {"translate": [{"x": -109.78, "y": 62.37}, {"time": 0.3333, "x": -98.34, "y": 87.45, "curve": "stepped"}, {"time": 0.6667, "x": -132.66, "y": 12.21}, {"time": 1.3333, "x": -109.78, "y": 62.37}], "scale": [{"x": 1.2, "y": 1.2}, {"time": 0.3333, "x": 1.424, "y": 1.424, "curve": "stepped"}, {"time": 0.6667, "x": 0.753, "y": 0.753}, {"time": 1.3333, "x": 1.2, "y": 1.2}]}, "ALL2": {"rotate": [{"angle": 39.29}], "translate": [{"x": -6.19, "y": -27.21}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL16": {"rotate": [{"angle": 52.21}], "translate": [{"x": 26.11, "y": -2.7}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL69": {"rotate": [{"angle": -10.17}], "translate": [{"x": -7.71, "y": 14.37}]}, "ALL8": {"rotate": [{"angle": -164.23}], "translate": [{"x": 77.73, "y": -13.44}, {"time": 0.6667, "x": 76.39, "y": -15.02}, {"time": 1.3333, "x": 77.73, "y": -13.44}]}, "ALL36": {"rotate": [{"angle": 12.37}]}, "ALL13": {"rotate": [{"angle": 56.47}], "translate": [{"x": 29.47, "y": -30.21}]}, "ALL64": {"translate": [{"x": 61.46, "y": -3.87}, {"time": 0.6667, "x": 41.82, "y": -111.86}, {"time": 1.3333, "x": 61.46, "y": -3.87}]}, "ALL6": {"rotate": [{"angle": -2.9}]}, "ALL37": {"rotate": [{"angle": 3.5}]}, "ALL5": {"rotate": [{"angle": -169.25}, {"time": 0.6667, "angle": -170.34}, {"time": 1.3333, "angle": -169.25}], "translate": [{"x": -10.82, "y": -12.76}], "scale": [{"x": -0.97, "y": 1.013}]}, "ALL66": {"translate": [{"x": -21.15, "y": 8.08}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL3": {"rotate": [{"angle": 4.31}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL11": {"rotate": [{"angle": -1.41}]}, "ALL26": {"translate": [{}, {"time": 0.6667, "x": 0.66, "y": -0.66}, {"time": 1.3333}]}, "ALL25": {"translate": [{}, {"time": 0.6667, "x": 3.62, "y": 3.62}, {"time": 1.3333}]}, "ALL23": {"translate": [{}, {"time": 0.6667, "x": 5.26, "y": 1.97}, {"time": 1.3333}]}, "ALL22": {"translate": [{}, {"time": 0.6667, "x": 5.26, "y": 6.58}, {"time": 1.3333}]}, "ALL21": {"translate": [{}, {"time": 0.6667, "y": 11}, {"time": 1.3333}]}, "ALL20": {"translate": [{}, {"time": 0.6667, "x": 1.32, "y": 0.99}, {"time": 1.3333}]}, "ALL19": {"translate": [{}, {"time": 0.6667, "x": 1.64, "y": 2.3}, {"time": 1.3333}]}, "ALL28": {"translate": [{}, {"time": 0.6667, "y": 9.43}, {"time": 1.3333}]}}, "ik": {"ALL8": [{"bendPositive": false}], "ALL16": [{"bendPositive": false}]}}, "village_sleep_3": {"slots": {"img_v2_3b71fc62-e05e-495d-9d1b-f4ce67b9a12g": {"color": [{"time": 1.1333, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}], "attachment": [{"name": "img_v2_3b71fc62-e05e-495d-9d1b-f4ce67b9a12g"}]}, "archer17": {"attachment": [{"name": "archer17_7"}]}, "archer16": {"attachment": [{"name": "Dead"}]}}, "bones": {"ALL30": {"scale": [{"x": 0.613, "y": 0.883}, {"time": 0.2, "x": 0.93, "y": 1.301}, {"time": 0.5, "x": 0.613, "y": 0.883, "curve": "stepped"}, {"time": 0.8, "x": 0.613, "y": 0.883}, {"time": 1, "x": 0.93, "y": 1.301}, {"time": 1.3, "x": 0.613, "y": 0.883}]}, "ALL17": {"translate": [{"x": -311.75, "y": -97.11}]}, "ALL2": {"rotate": [{"angle": 39.29}, {"time": 0.2333, "angle": 35.2}], "translate": [{"x": -6.19, "y": -27.21}, {"time": 0.2333, "x": -14.35, "y": -18.03}, {"time": 0.5, "x": -6.41, "y": -24.53, "curve": "stepped"}, {"time": 0.7333, "x": -6.41, "y": -24.53}, {"time": 1.0667, "x": -14.71, "y": -18.4}, {"time": 1.3333, "x": -9.29, "y": -25.62}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL16": {"rotate": [{"angle": 52.21}, {"time": 0.2333, "angle": 60.45, "curve": "stepped"}, {"time": 0.9, "angle": 60.45}, {"time": 1.0333, "angle": 120.72}, {"time": 1.2667, "angle": 50.36}, {"time": 1.5, "angle": 60.45}], "translate": [{"x": 26.11, "y": -2.7}, {"time": 0.2333, "x": 18.24, "y": 20.26}, {"time": 0.5, "x": 28.35, "y": 14.12, "curve": "stepped"}, {"time": 0.7333, "x": 28.35, "y": 14.12}, {"time": 0.9, "x": 13.54, "y": 31.81}, {"time": 1.0333, "x": 28.95, "y": 33.73}, {"time": 1.2667, "x": 25.95, "y": 12.46}, {"time": 1.5, "x": 25.95, "y": 14.04}]}, "ALL7": {"rotate": [{"angle": 10.4}, {"time": 1.5, "angle": -35.73}]}, "ALL69": {"rotate": [{"angle": -10.17}, {"time": 0.3333, "angle": 68.92}, {"time": 0.6667, "angle": 75.88}, {"time": 1.0667, "angle": 62.26}, {"time": 1.5, "angle": 66.96}], "translate": [{"x": -7.71, "y": 14.37}, {"time": 0.3333, "x": 19.8, "y": -0.59}, {"time": 0.6667, "x": 11.39, "y": 3.15}, {"time": 1.0667, "x": 25.21, "y": -28.87}, {"time": 1.5, "x": 36.23, "y": -12.36}]}, "ALL8": {"rotate": [{"angle": -122.61}, {"time": 0.3333, "angle": -96.49}, {"time": 0.6667, "angle": -83.43}, {"time": 1.1, "angle": -76.16}], "translate": [{"x": 42.02, "y": 3.48}, {"time": 0.3333, "x": 69.52, "y": 12.63}, {"time": 0.6667, "x": 45.67, "y": 21.68}, {"time": 1.1, "x": 41.19, "y": 18.83}, {"time": 1.5, "x": 39.15, "y": 20.6}]}, "ALL36": {"rotate": [{"angle": 12.37}]}, "ALL13": {"rotate": [{"angle": 56.47}, {"time": 0.2667, "angle": 129.64}, {"time": 0.5, "angle": 47.86, "curve": "stepped"}, {"time": 0.7333, "angle": 47.86}, {"time": 1.0667, "angle": 74.54}, {"time": 1.3333, "angle": 33.13}], "translate": [{"x": 29.47, "y": -30.21}, {"time": 0.1667, "x": 15.58, "y": 1.29}, {"time": 0.2667, "x": 30.32, "y": 11.6}, {"time": 0.5, "x": 26.65, "y": -19.46, "curve": "stepped"}, {"time": 0.7333, "x": 26.65, "y": -19.46}, {"time": 1.0667, "x": 36.4, "y": -15.85}, {"time": 1.3333, "x": 18.63, "y": -31.07}]}, "ALL64": {"translate": [{"x": 61.46, "y": -3.87}, {"time": 0.3, "x": 195.28, "y": 225.16}, {"time": 0.6333, "x": -15.55, "y": 259.01}, {"time": 0.9667, "x": 156.28, "y": 42.14}, {"time": 1.5, "x": 78.16, "y": -66.59}]}, "ALL4": {"rotate": [{}, {"time": 0.2333, "angle": 0.76}, {"time": 0.5, "angle": -2.6, "curve": "stepped"}, {"time": 0.7333, "angle": -2.6}, {"time": 1.0667, "angle": -8.35}, {"time": 1.3333, "angle": -8.15}]}, "ALL6": {"rotate": [{"angle": -2.9}, {"time": 1.5, "angle": -28.98}]}, "ALL37": {"rotate": [{"angle": 3.5}]}, "ALL5": {"rotate": [{"angle": -6.83}, {"time": 0.2333, "angle": -3.13}, {"time": 0.5, "angle": 6.16, "curve": "stepped"}, {"time": 0.7333, "angle": 6.16}, {"time": 1.0667, "angle": -6.86}, {"time": 1.3333, "angle": 1.68}], "scale": [{"x": 0.97, "y": 1.013}]}, "ALL66": {"translate": [{"x": -21.15, "y": 8.08}, {"time": 0.3333, "x": -370.81, "y": 46.17}, {"time": 0.6667, "x": -49.72, "y": -104.12, "curve": "stepped"}, {"time": 0.8, "x": -49.72, "y": -104.12}, {"time": 1.0667, "x": 319.57, "y": -7.92}, {"time": 1.3333, "x": -18.9, "y": -34.5}]}, "ALL9": {"rotate": [{"angle": 2.75}, {"time": 1.5, "angle": 58.87}]}, "ALL3": {"rotate": [{"angle": 4.31}, {"time": 0.2333, "angle": 8.76}, {"time": 0.5, "angle": 10.16, "curve": "stepped"}, {"time": 0.7333, "angle": 10.16}, {"time": 1.0667, "angle": 9.2}, {"time": 1.3333, "angle": 9.15}]}, "ALL15": {"rotate": [{"angle": -1.67}, {"time": 1.5, "angle": -32.58}]}, "ALL14": {"rotate": [{"angle": 0.86}, {"time": 1.5, "angle": 59.15}]}, "ALL12": {"rotate": [{"angle": 2.61}, {"time": 1.5, "angle": -7.44}]}, "ALL11": {"rotate": [{"angle": -1.41}, {"time": 1.5, "angle": 2.48}]}, "ALL26": {"translate": [{"time": 0.7667}, {"time": 1.1, "x": 12.46, "y": -7.12}, {"time": 1.3, "x": 16.33, "y": -10.2}]}, "ALL25": {"translate": [{}, {"time": 0.2333, "x": 24.35, "y": -4.93}, {"time": 0.4333, "x": 32.74, "y": -19.49, "curve": "stepped"}, {"time": 0.7667, "x": 32.74, "y": -19.49}, {"time": 1.1, "x": 57.63, "y": -26.15}, {"time": 1.3, "x": 56.87, "y": -36.67}]}, "ALL24": {"translate": [{}, {"time": 0.2333, "x": 52.1, "y": 5.59}, {"time": 0.4333, "x": 49.86, "y": -31.64, "curve": "stepped"}, {"time": 0.7667, "x": 49.86, "y": -31.64}, {"time": 1.1, "x": 78.5, "y": -36.03}, {"time": 1.3, "x": 77.22, "y": -56.52}]}, "ALL23": {"translate": [{"time": 0.7667}, {"time": 1.1, "x": 19.94, "y": -10.32}, {"time": 1.3, "x": 16.12, "y": -16.17}]}, "ALL22": {"translate": [{}, {"time": 0.2333, "x": 43.01, "y": -2.26}, {"time": 0.4333, "x": 33.8, "y": -26.33, "curve": "stepped"}, {"time": 0.7667, "x": 33.8, "y": -26.33}, {"time": 1.1, "x": 61.2, "y": -33.53}, {"time": 1.3, "x": 52.78, "y": -43.68}]}, "ALL21": {"translate": [{}, {"time": 0.2333, "x": 57.54, "y": 20.98}, {"time": 0.4333, "x": 48.24, "y": -34.22, "curve": "stepped"}, {"time": 0.7667, "x": 48.24, "y": -34.22}, {"time": 1.1, "x": 82.18, "y": -46.54}, {"time": 1.3, "x": 70.25, "y": -61.7}]}, "ALL20": {"translate": [{"time": 0.7667}, {"time": 1.1, "x": 2.14, "y": -6.76}, {"time": 1.3, "x": 6.19, "y": -8.09}]}, "ALL19": {"translate": [{}, {"time": 0.2333, "x": 24.57, "y": -22.76}, {"time": 0.4333, "x": 27.46, "y": -26.55, "curve": "stepped"}, {"time": 0.7667, "x": 27.46, "y": -26.55}, {"time": 1.1, "x": 51.44, "y": -30.41}, {"time": 1.3, "x": 38.96, "y": -42.74}]}, "ALL18": {"translate": [{}, {"time": 0.2333, "x": 32.3, "y": -25.01}, {"time": 0.4333, "x": 45.03, "y": -39.21, "curve": "stepped"}, {"time": 0.7667, "x": 45.03, "y": -39.21}, {"time": 1.1, "x": 84.18, "y": -47.29}, {"time": 1.3, "x": 63.72, "y": -61.83}]}, "ALL28": {"translate": [{}, {"time": 0.2333, "x": 82.71, "y": 17.67}, {"time": 0.4333, "x": 70.99, "y": -41.31, "curve": "stepped"}, {"time": 0.7667, "x": 70.99, "y": -41.31}, {"time": 1.1, "x": 112.69, "y": -60.02}, {"time": 1.3, "x": 99, "y": -85.19}]}, "ALL29": {"translate": [{}, {"time": 0.2333, "x": 63, "y": 6.17}, {"time": 0.4333, "x": 64.12, "y": -42.37, "curve": "stepped"}, {"time": 0.7667, "x": 64.12, "y": -42.37}, {"time": 1.1, "x": 115.78, "y": -54.33}, {"time": 1.3, "x": 100.93, "y": -80.78}]}, "ALL27": {"translate": [{}, {"time": 0.2333, "x": 54.23, "y": -29.15}, {"time": 0.4333, "x": 61.13, "y": -47.34, "curve": "stepped"}, {"time": 0.7667, "x": 61.13, "y": -47.34}, {"time": 1.1, "x": 115.97, "y": -55.43}, {"time": 1.3, "x": 103.3, "y": -76.94}]}}, "ik": {"ALL8": [{"bendPositive": false}], "ALL16": [{"bendPositive": false}]}}, "village_sleep_4": {"slots": {"Z3": {"color": [{"color": "fbffd8aa"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1.1667, "color": "f9ffc5ff"}, {"time": 1.3333, "color": "fbffd8aa"}], "attachment": [{"name": "Z"}]}, "Z2": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 0.8333, "color": "f9ffc5ff"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "Z"}]}, "archer17": {"attachment": [{"name": "archer17_7"}]}, "archer16": {"attachment": [{"name": "Dead"}]}, "Z": {"color": [{"color": "ffffff00"}, {"time": 0.5, "color": "f9ffc5ff"}, {"time": 1, "color": "ffffff00"}], "attachment": [{"name": "Z"}]}}, "bones": {"ALL30": {"scale": [{"x": 0.613, "y": 0.883}, {"time": 0.5667, "x": 0.98, "y": 1.526}, {"time": 1.3333, "x": 0.613, "y": 0.883}]}, "ALL38": {"translate": [{"x": -132.66, "y": 12.21}, {"time": 1, "x": -98.34, "y": 87.45}], "scale": [{"x": 0.753, "y": 0.753}, {"time": 1, "x": 1.424, "y": 1.424}]}, "ALL39": {"translate": [{}, {"time": 0.3333, "x": -132.66, "y": 12.21}, {"time": 1.3333, "x": -98.34, "y": 87.45}], "scale": [{}, {"time": 0.3333, "x": 0.753, "y": 0.753}, {"time": 1.3333, "x": 1.424, "y": 1.424}]}, "ALL40": {"translate": [{"x": -109.78, "y": 62.37}, {"time": 0.3333, "x": -98.34, "y": 87.45, "curve": "stepped"}, {"time": 0.6667, "x": -132.66, "y": 12.21}, {"time": 1.3333, "x": -109.78, "y": 62.37}], "scale": [{"x": 1.2, "y": 1.2}, {"time": 0.3333, "x": 1.424, "y": 1.424, "curve": "stepped"}, {"time": 0.6667, "x": 0.753, "y": 0.753}, {"time": 1.3333, "x": 1.2, "y": 1.2}]}, "ALL2": {"rotate": [{"angle": 35.2}], "translate": [{"x": -9.29, "y": -25.62}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL16": {"rotate": [{"angle": 60.45}], "translate": [{"x": 25.95, "y": 14.04}]}, "ALL7": {"rotate": [{"angle": -35.73}]}, "ALL69": {"rotate": [{"angle": 66.96}], "translate": [{"x": 36.23, "y": -12.36}]}, "ALL8": {"rotate": [{"angle": -76.16}], "translate": [{"x": 39.15, "y": 20.6}]}, "ALL36": {"rotate": [{"angle": 12.37}]}, "ALL13": {"rotate": [{"angle": 33.13}], "translate": [{"x": 18.63, "y": -31.07}]}, "ALL64": {"translate": [{"x": 61.46, "y": -3.87}, {"time": 0.6667, "x": 163.16, "y": -43.34}, {"time": 1.3333, "x": 61.46, "y": -3.87}]}, "ALL4": {"rotate": [{"angle": -8.15}]}, "ALL6": {"rotate": [{"angle": -28.98}]}, "ALL37": {"rotate": [{"angle": 3.5}]}, "ALL5": {"rotate": [{"angle": 1.68}, {"time": 0.6667, "angle": 1.72}, {"time": 1.3333, "angle": 1.68}], "scale": [{"x": 0.97, "y": 1.013}]}, "ALL66": {"translate": [{"x": -18.9, "y": -34.5}, {"time": 0.6667, "x": -7.16, "y": 110.14}, {"time": 1.3333, "x": -18.9, "y": -34.5}]}, "ALL9": {"rotate": [{"angle": 58.87}]}, "ALL3": {"rotate": [{"angle": 9.15}]}, "ALL15": {"rotate": [{"angle": -32.58}]}, "ALL14": {"rotate": [{"angle": 59.15}]}, "ALL12": {"rotate": [{"angle": -7.44}]}, "ALL11": {"rotate": [{"angle": 2.48}]}, "ALL17": {"translate": [{"x": -311.75, "y": -97.11}]}, "ALL26": {"translate": [{"x": 16.33, "y": -10.2}]}, "ALL25": {"translate": [{"x": 56.87, "y": -36.67}]}, "ALL24": {"translate": [{"x": 77.22, "y": -56.52}]}, "ALL23": {"translate": [{"x": 16.12, "y": -16.17}]}, "ALL22": {"translate": [{"x": 52.78, "y": -43.68}]}, "ALL21": {"translate": [{"x": 70.25, "y": -61.7}]}, "ALL20": {"translate": [{"x": 6.19, "y": -8.09}]}, "ALL19": {"translate": [{"x": 38.96, "y": -42.74}]}, "ALL18": {"translate": [{"x": 63.72, "y": -61.83}]}, "ALL28": {"translate": [{"x": 99, "y": -85.19}]}, "ALL29": {"translate": [{"x": 100.93, "y": -80.78}]}, "ALL27": {"translate": [{"x": 103.3, "y": -76.94}]}}, "ik": {"ALL8": [{"bendPositive": false}], "ALL16": [{"bendPositive": false}]}}, "water": {"slots": {"tong": {"attachment": [{"name": "tong1"}]}, "barb": {"attachment": [{"name": "barb5"}]}, "penye0001": {"color": [{"time": 1.2, "color": "ffffffff"}, {"time": 1.4, "color": "ffffff00", "curve": "stepped"}, {"time": 2.7, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8667, "color": "ffffffff"}, {"time": 3.0667, "color": "ffffff00"}], "attachment": [{"time": 1.0333, "name": "penye0001"}, {"time": 1.0667, "name": "penye0002"}, {"time": 1.1, "name": "penye0003"}, {"time": 1.1333, "name": "penye0004"}, {"time": 1.1667, "name": "penye0005"}, {"time": 1.2, "name": "penye0006"}, {"time": 1.2667, "name": "penye0007"}, {"time": 1.3333, "name": "penye0008"}, {"time": 1.4, "name": null}, {"time": 2.7, "name": "penye0001"}, {"time": 2.7333, "name": "penye0002"}, {"time": 2.7667, "name": "penye0003"}, {"time": 2.8, "name": "penye0004"}, {"time": 2.8333, "name": "penye0005"}, {"time": 2.8667, "name": "penye0006"}, {"time": 2.9333, "name": "penye0007"}, {"time": 3, "name": "penye0008"}, {"time": 3.0667, "name": null}]}, "archer16": {"attachment": [{"name": "Dead"}, {"time": 0.5333, "name": "archer16_1"}, {"time": 1.6667, "name": "Dead"}, {"time": 2.2, "name": "archer16_1"}, {"time": 6.3333, "name": "Dead"}]}, "Elements - Liquid 009 Splash Right noCT noRSZ_1": {"attachment": [{"time": 0.3333, "name": "Elements - Liquid 009 Splash Right noCT noRSZ_00001"}, {"time": 1.0667, "name": null}, {"time": 2, "name": "Elements - Liquid 009 Splash Right noCT noRSZ_00001"}, {"time": 2.7333, "name": null}]}, "penye2": {"color": [{"time": 1.2333, "color": "ffffffff"}, {"time": 1.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.9, "color": "ffffffff"}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 1.0667, "name": "penye0001"}, {"time": 1.1, "name": "penye0002"}, {"time": 1.1333, "name": "penye0003"}, {"time": 1.1667, "name": "penye0004"}, {"time": 1.2, "name": "penye0005"}, {"time": 1.2333, "name": "penye0006"}, {"time": 1.3, "name": "penye0007"}, {"time": 1.3667, "name": "penye0008"}, {"time": 1.4333, "name": null}, {"time": 2.7333, "name": "penye0001"}, {"time": 2.7667, "name": "penye0002"}, {"time": 2.8, "name": "penye0003"}, {"time": 2.8333, "name": "penye0004"}, {"time": 2.8667, "name": "penye0005"}, {"time": 2.9, "name": "penye0006"}, {"time": 2.9667, "name": "penye0007"}, {"time": 3.0333, "name": "penye0008"}, {"time": 3.1, "name": null}]}, "penye1": {"color": [{"time": 1.2667, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.9333, "color": "ffffffff"}, {"time": 3.1333, "color": "ffffff00"}], "attachment": [{"time": 1.1, "name": "penye0001"}, {"time": 1.1333, "name": "penye0002"}, {"time": 1.1667, "name": "penye0003"}, {"time": 1.2, "name": "penye0004"}, {"time": 1.2333, "name": "penye0005"}, {"time": 1.2667, "name": "penye0006"}, {"time": 1.3333, "name": "penye0007"}, {"time": 1.4, "name": "penye0008"}, {"time": 1.4667, "name": null}, {"time": 2.7667, "name": "penye0001"}, {"time": 2.8, "name": "penye0002"}, {"time": 2.8333, "name": "penye0003"}, {"time": 2.8667, "name": "penye0004"}, {"time": 2.9, "name": "penye0005"}, {"time": 2.9333, "name": "penye0006"}, {"time": 3, "name": "penye0007"}, {"time": 3.0667, "name": "penye0008"}, {"time": 3.1333, "name": null}]}, "tong2": {"attachment": [{"name": "tong"}]}}, "bones": {"ALL58": {"rotate": [{"angle": -30.88, "curve": "stepped"}, {"time": 3.0667, "angle": -30.88}, {"time": 3.3333, "curve": "stepped"}, {"time": 6}, {"time": 6.3333, "angle": -30.88}], "translate": [{"x": 5.51, "y": 5.48, "curve": "stepped"}, {"time": 3.0667, "x": 5.51, "y": 5.48}, {"time": 3.3333, "curve": "stepped"}, {"time": 6}, {"time": 6.3333, "x": 5.51, "y": 5.48}]}, "ALL49": {"rotate": [{"time": 0.3333, "angle": -28.4}], "translate": [{"time": 0.3333, "x": -133.84, "y": 63.02}], "scale": [{"time": 0.3333, "x": 0.813, "y": 1.45}], "shear": [{"time": 0.3333, "x": 1.01}]}, "ALL50": {"rotate": [{"time": 1.1, "angle": -37.14}], "translate": [{"time": 1.1, "x": 60.03, "y": 29.22}], "scale": [{"time": 1.1, "x": 0.235, "y": 0.235}]}, "ALL48": {"rotate": [{"time": 1.0333, "angle": -37.14}], "translate": [{"time": 1.0333, "x": 18.32, "y": 15.95}], "scale": [{"time": 1.0333, "x": 0.235, "y": 0.235}]}, "ALL51": {"rotate": [{"time": 1.0667, "angle": -37.14}], "translate": [{"time": 1.0667, "x": 34.95, "y": 21.43}], "scale": [{"time": 1.0667, "x": 0.274, "y": 0.274}]}, "ALL2": {"rotate": [{"angle": -1.41}, {"time": 0.2667, "angle": -0.95, "curve": "stepped"}, {"time": 0.3667, "angle": -0.95}, {"time": 0.5333, "angle": 4.49}, {"time": 0.9333, "angle": 7.31}, {"time": 1.4, "angle": -4.2}, {"time": 1.6667, "angle": -1.41}, {"time": 1.9333, "angle": -0.95, "curve": "stepped"}, {"time": 2.0333, "angle": -0.95}, {"time": 2.2, "angle": 4.49}, {"time": 2.6, "angle": 7.31}, {"time": 3.0667, "angle": -4.2}, {"time": 3.3333, "curve": "stepped"}, {"time": 6}, {"time": 6.3333, "angle": -1.41}], "translate": [{"x": 1.17, "y": -10.79}, {"time": 0.2667, "x": 4.88, "y": -17.72}, {"time": 0.3667, "x": -3.4, "y": -10.27}, {"time": 0.5333, "x": -12.66, "y": -2.4}, {"time": 0.9333, "x": -16.71, "y": -1.79}, {"time": 1.4, "x": -7.92, "y": -2.08}, {"time": 1.6667, "x": 1.17, "y": -10.79}, {"time": 1.9333, "x": 4.88, "y": -17.72}, {"time": 2.0333, "x": -3.4, "y": -10.27}, {"time": 2.2, "x": -12.66, "y": -2.4}, {"time": 2.6, "x": -16.71, "y": -1.79}, {"time": 3.0667, "x": -7.92, "y": -2.08}, {"time": 3.3333, "y": 2.09}, {"time": 3.7}, {"time": 4.3667, "y": 3.84}, {"time": 4.6667, "y": 2.09}, {"time": 5.0333}, {"time": 5.7, "y": 3.84}, {"time": 6, "y": 2.09}, {"time": 6.3333, "x": 1.17, "y": -10.79}]}, "ALL13": {"translate": [{"time": 0.3667}, {"time": 0.5333, "x": -18.06, "curve": "stepped"}, {"time": 1.4, "x": -18.06}, {"time": 1.6667, "curve": "stepped"}, {"time": 2.0333}, {"time": 2.2, "x": -18.06, "curve": "stepped"}, {"time": 3.0667, "x": -18.06}, {"time": 3.3333}]}, "ALL36": {"rotate": [{"angle": 12.37, "curve": "stepped"}, {"time": 3.3333, "angle": 12.37}, {"time": 3.7667}, {"time": 4.4333, "angle": 17.26}, {"time": 4.6667, "angle": 12.37}, {"time": 5.1}, {"time": 5.7667, "angle": 17.26}, {"time": 6, "angle": 12.37}]}, "ALL8": {"rotate": [{"angle": 9.4}, {"time": 0.6667, "angle": 21.06}, {"time": 1.2, "angle": -10.55}, {"time": 1.6667, "angle": 9.4}, {"time": 2.3333, "angle": 21.06}, {"time": 2.8667, "angle": -10.55}, {"time": 3.3333, "angle": 9.4}], "translate": [{"x": -2.77, "y": -11.55}, {"time": 0.3333, "x": 1.38, "y": -2.2}, {"time": 0.6667, "x": -6.58, "y": -14.99}, {"time": 1.2, "x": 2.8, "y": -1.25}, {"time": 1.6667, "x": -2.77, "y": -11.55}, {"time": 2, "x": 1.38, "y": -2.2}, {"time": 2.3333, "x": -6.58, "y": -14.99}, {"time": 2.8667, "x": 2.8, "y": -1.25}, {"time": 3.3333, "x": -6.12, "y": -6.15}, {"time": 4, "x": -5.17, "y": -6.23}, {"time": 4.6667, "x": -6.12, "y": -6.15}, {"time": 5.3333, "x": -5.17, "y": -6.23}, {"time": 6, "x": -6.12, "y": -6.15}, {"time": 6.3333, "x": -2.77, "y": -11.55}]}, "ALL64": {"translate": [{"x": -52.79, "y": -76.69}, {"time": 0.3667, "x": -84.12, "y": -179.09}, {"time": 0.5333, "x": 28.81, "y": -0.37}, {"time": 0.9333, "x": -113.2, "y": 144.18}, {"time": 1.4, "x": -49.02, "y": -46.98}, {"time": 1.6667, "x": -52.79, "y": -76.69}, {"time": 2.0333, "x": -84.12, "y": -179.09}, {"time": 2.2, "x": 28.81, "y": -0.37}, {"time": 2.6, "x": -113.2, "y": 144.18}, {"time": 3.0667, "x": -49.02, "y": -46.98}, {"time": 3.3333, "x": 61.46, "y": -3.87}, {"time": 3.8667, "x": -51.63, "y": 41.15}, {"time": 4.5333, "x": 78.37, "y": -10.6}, {"time": 4.6667, "x": 61.46, "y": -3.87}, {"time": 5.2, "x": -51.63, "y": 41.15}, {"time": 5.8667, "x": 78.37, "y": -10.6}, {"time": 6, "x": 61.46, "y": -3.87}, {"time": 6.3333, "x": -52.79, "y": -76.69}]}, "ALL37": {"rotate": [{"angle": 3.5, "curve": "stepped"}, {"time": 3.3333, "angle": 3.5}, {"time": 3.8333, "angle": 17.26}, {"time": 4.5}, {"time": 4.6667, "angle": 3.5}, {"time": 5.1667, "angle": 17.26}, {"time": 5.8333}, {"time": 6, "angle": 3.5}]}, "ALL4": {"rotate": [{"angle": -8.6}, {"time": 0.2667, "angle": -11.63}, {"time": 0.3667, "angle": -15.49}, {"time": 0.5333, "angle": -12.66}, {"time": 0.9333, "angle": -11.05}, {"time": 1.4, "angle": -3.75}, {"time": 1.6667, "angle": -8.6}, {"time": 1.9333, "angle": -11.63}, {"time": 2.0333, "angle": -15.49}, {"time": 2.2, "angle": -12.66}, {"time": 2.6, "angle": -11.05}, {"time": 3.0667, "angle": -3.75}, {"time": 3.3333, "curve": "stepped"}, {"time": 6}, {"time": 6.3333, "angle": -8.6}]}, "ALL69": {"rotate": [{"angle": -43.47}, {"time": 0.2667, "angle": -49.9}, {"time": 0.3667, "angle": -40.6}, {"time": 0.5333, "angle": -49.05}, {"time": 0.9333, "angle": -108.96}, {"time": 1.4, "angle": -41.49}, {"time": 1.5333, "angle": -13.96}, {"time": 1.6667, "angle": -43.47}, {"time": 1.9333, "angle": -49.9}, {"time": 2.0333, "angle": -40.6}, {"time": 2.2, "angle": -49.05}, {"time": 2.6, "angle": -108.96}, {"time": 3.0667, "angle": -41.49}, {"time": 3.3333, "angle": -30.09, "curve": "stepped"}, {"time": 6, "angle": -30.09}, {"time": 6.3333, "angle": -43.47}], "translate": [{"x": 23.96, "y": -6.75}, {"time": 0.2667, "x": 30.53, "y": -6.42}, {"time": 0.3667, "x": 41.08, "y": -4.5}, {"time": 0.5333, "x": 48.55, "y": -12.98}, {"time": 0.9333, "x": -5.96, "y": 14.48}, {"time": 1.4, "x": 4.88, "y": -5.22}, {"time": 1.5333, "x": 32.21, "y": 8.83}, {"time": 1.6667, "x": 23.96, "y": -6.75}, {"time": 1.9333, "x": 30.53, "y": -6.42}, {"time": 2.0333, "x": 41.08, "y": -4.5}, {"time": 2.2, "x": 48.55, "y": -12.98}, {"time": 2.6, "x": -5.96, "y": 14.48}, {"time": 3.0667, "x": 4.88, "y": -5.22}, {"time": 3.3333, "x": -9.12, "y": 11.84, "curve": "stepped"}, {"time": 6, "x": -9.12, "y": 11.84}, {"time": 6.3333, "x": 23.96, "y": -6.75}]}, "ALL66": {"translate": [{"x": -21.15, "y": 8.08}, {"time": 0.2667, "x": 92.92, "y": -79.05, "curve": "stepped"}, {"time": 0.5333, "x": 92.92, "y": -79.05}, {"time": 0.9333, "x": -309.71, "y": -163.94}, {"time": 1.4, "x": 15.07, "y": -52.78}, {"time": 1.6667, "x": -21.15, "y": 8.08}, {"time": 1.9333, "x": 92.92, "y": -79.05, "curve": "stepped"}, {"time": 2.2, "x": 92.92, "y": -79.05}, {"time": 2.6, "x": -309.71, "y": -163.94}, {"time": 3.0667, "x": 15.07, "y": -52.78}, {"time": 3.3333, "x": -21.15, "y": 8.08}, {"time": 3.6333, "x": -65.95, "y": -22.96}, {"time": 4.3, "x": 32.31, "y": 45.12}, {"time": 4.6667, "x": -21.15, "y": 8.08}, {"time": 4.9667, "x": -65.95, "y": -22.96}, {"time": 5.6333, "x": 32.31, "y": 45.12}, {"time": 6, "x": -21.15, "y": 8.08}]}, "ALL3": {"rotate": [{"angle": -9.94}, {"time": 0.2667, "angle": -12.82}, {"time": 0.3667, "angle": -7.21}, {"time": 0.5333, "angle": -3.04}, {"time": 0.9333, "angle": -4.18}, {"time": 1.4, "angle": -0.48}, {"time": 1.6667, "angle": -9.94}, {"time": 1.9333, "angle": -12.82}, {"time": 2.0333, "angle": -7.21}, {"time": 2.2, "angle": -3.04}, {"time": 2.6, "angle": -4.18}, {"time": 3.0667, "angle": -0.48}, {"time": 3.3333, "curve": "stepped"}, {"time": 6}, {"time": 6.3333, "angle": -9.94}]}, "ALL5": {"rotate": [{"angle": 8.91}, {"time": 0.2667, "angle": 13.57}, {"time": 0.5333, "angle": 7.02}, {"time": 0.9333, "angle": 5.72}, {"time": 1.4, "angle": 8.76}, {"time": 1.6667, "angle": 8.91}, {"time": 1.9333, "angle": 13.57}, {"time": 2.2, "angle": 7.02}, {"time": 2.6, "angle": 5.72}, {"time": 3.0667, "angle": 8.76}, {"time": 3.3333}, {"time": 4, "angle": 0.14}, {"time": 4.6667}, {"time": 5.3333, "angle": 0.14}, {"time": 6}, {"time": 6.3333, "angle": 8.91}], "translate": [{"x": -1.08, "y": -5.19, "curve": "stepped"}, {"time": 3.0667, "x": -1.08, "y": -5.19}, {"time": 3.3333, "curve": "stepped"}, {"time": 6}, {"time": 6.3333, "x": -1.08, "y": -5.19}], "scale": [{"time": 3.3333}, {"time": 4, "x": 0.942, "y": 1.02}, {"time": 4.6667}, {"time": 5.3333, "x": 0.942, "y": 1.02}, {"time": 6}]}, "ALL9": {"rotate": [{"angle": 2.75}]}, "ALL6": {"rotate": [{"angle": -2.9}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL11": {"rotate": [{"angle": -1.41}]}, "ALL10": {"rotate": [{"angle": -7.48}]}}, "ik": {"ALL16": [{"bendPositive": false}]}, "drawOrder": [{"time": 0.5667, "offsets": [{"slot": "barb", "offset": 18}, {"slot": "archer8", "offset": 18}, {"slot": "archer7", "offset": 18}, {"slot": "Elements - Liquid 009 Splash Right noCT noRSZ_1", "offset": 1}]}, {"time": 1.4, "offsets": [{"slot": "barb", "offset": 19}, {"slot": "archer8", "offset": 19}, {"slot": "archer7", "offset": 19}]}, {"time": 1.5333}, {"time": 2.2333, "offsets": [{"slot": "barb", "offset": 18}, {"slot": "archer8", "offset": 18}, {"slot": "archer7", "offset": 18}, {"slot": "Elements - Liquid 009 Splash Right noCT noRSZ_1", "offset": 1}]}, {"time": 3.0667, "offsets": [{"slot": "barb", "offset": 19}, {"slot": "archer8", "offset": 19}, {"slot": "archer7", "offset": 19}]}, {"time": 3.3333}]}, "work": {"bones": {"ALL9": {"rotate": [{"angle": 2.75}]}, "ALL69": {"rotate": [{"angle": 44}], "translate": [{"x": 38.38, "y": 15.92}, {"time": 0.2667, "x": 35.48, "y": 5.24}, {"time": 0.5333, "x": 38.38, "y": 15.92}]}, "ALL66": {"translate": [{"x": 318.88, "y": -30.7}, {"time": 0.2667, "x": 22.47, "y": -15.89}, {"time": 0.5333, "x": 318.88, "y": -30.7}]}, "ALL3": {"rotate": [{"angle": -8.86}, {"time": 0.2667, "angle": -2.27}, {"time": 0.5333, "angle": -8.86}], "translate": [{"x": 1.91, "y": -9.4}]}, "ALL64": {"translate": [{"x": 38.11, "y": -64.07}, {"time": 0.2667, "x": -6.46, "y": -182.61}, {"time": 0.5333, "x": 38.11, "y": -64.07}]}, "ALL4": {"rotate": [{"angle": -22.45}]}, "ALL36": {"rotate": [{"angle": 12.37}]}, "ALL5": {"rotate": [{"angle": 28.56}, {"time": 0.2667, "angle": 23.74}, {"time": 0.5333, "angle": 28.56}]}, "ALL6": {"rotate": [{"angle": -2.9}], "translate": [{"x": -18.75, "y": 4.19}]}, "ALL2": {"rotate": [{"angle": -1.96}, {"time": 0.2667, "angle": -1.48}, {"time": 0.5333, "angle": -1.96}], "translate": [{"x": 8.94, "y": -10.84}, {"time": 0.1, "x": 6.81, "y": -10.25}, {"time": 0.3667, "x": 13.16, "y": -12.02}, {"time": 0.5333, "x": 8.94, "y": -10.84}]}, "ALL16": {"translate": [{"x": 18.71}]}, "ALL8": {"rotate": [{"angle": 117.91}, {"time": 0.2667, "angle": 112.6}, {"time": 0.5333, "angle": 117.91}], "translate": [{"x": 15.6, "y": -91.12}, {"time": 0.2667, "x": 13.32, "y": -81.34}, {"time": 0.5333, "x": 15.6, "y": -91.12}]}, "ALL15": {"rotate": [{"angle": -1.67}]}, "ALL14": {"rotate": [{"angle": 0.86}]}, "ALL12": {"rotate": [{"angle": 2.61}]}, "ALL11": {"rotate": [{"angle": -1.41}]}, "ALL10": {"rotate": [{"angle": -7.48}]}, "ALL7": {"rotate": [{"angle": 10.4}]}, "ALL37": {"rotate": [{"angle": 3.5}]}}, "ik": {"ALL16": [{"bendPositive": false}]}}}}