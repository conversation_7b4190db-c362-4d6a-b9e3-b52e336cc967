import DataTexture from "../SimpleLightSystem/DataTexture";

/**
 * 测试32位浮点数编码解码的正确性
 */
export class FloatEncodingTest {
    
    /**
     * 测试各种浮点数值的编码解码
     */
    static testFloatEncoding() {
        console.log("=== 32位浮点数编码解码测试 ===");
        
        // 测试用例：包含各种类型的浮点数
        const testValues = [
            0.0,           // 零
            1.0,           // 正整数
            -1.0,          // 负整数
            3.14159,       // 正小数
            -3.14159,      // 负小数
            0.000001,      // 很小的正数
            -0.000001,     // 很小的负数
            1000000.0,     // 很大的正数
            -1000000.0,    // 很大的负数
            123.456789,    // 普通小数
            -123.456789,   // 负普通小数
            Number.MAX_VALUE,      // 最大值
            Number.MIN_VALUE,      // 最小正值
            -Number.MAX_VALUE,     // 最大负值
            Infinity,              // 正无穷
            -Infinity,             // 负无穷
            NaN                    // 非数字
        ];
        
        console.log("测试值数量:", testValues.length);
        console.log("原始值 -> 编码后的RGBA -> 验证是否正确");
        console.log("----------------------------------------");
        
        let passCount = 0;
        let failCount = 0;
        
        for (let i = 0; i < testValues.length; i++) {
            const originalValue = testValues[i];
            
            // 使用DataTexture的编码函数
            const encoded = DataTexture.EncodeFloatToRGBA(originalValue);
            
            // 模拟shader中的解码过程（JavaScript版本）
            const decoded = this.decodeFloatFromRGBA(encoded);
            
            // 检查是否相等（对于NaN需要特殊处理）
            const isEqual = this.floatEquals(originalValue, decoded);
            
            console.log(`${i + 1}. ${originalValue} -> [${encoded.join(', ')}] -> ${decoded} ${isEqual ? '✓' : '✗'}`);
            
            if (isEqual) {
                passCount++;
            } else {
                failCount++;
                console.error(`  错误: 期望 ${originalValue}, 得到 ${decoded}`);
            }
        }
        
        console.log("----------------------------------------");
        console.log(`测试结果: ${passCount} 通过, ${failCount} 失败`);
        console.log(`成功率: ${((passCount / testValues.length) * 100).toFixed(2)}%`);
        
        return failCount === 0;
    }

    /**
     * 测试兼容版本解码函数的正确性
     */
    static testCompatibleDecoding() {
        console.log("\n=== 兼容版本解码测试 ===");

        const testValues = [0.0, 1.0, -1.0, 3.14159, -3.14159, 123.456789];

        let passCount = 0;
        let failCount = 0;

        for (const originalValue of testValues) {
            const encoded = DataTexture.EncodeFloatToRGBA(originalValue);
            const fastDecoded = this.decodeFloatFromRGBA_Fast(encoded);
            const compatibleDecoded = this.decodeFloatFromRGBA_Compatible(encoded);

            const isEqual = this.floatEquals(fastDecoded, compatibleDecoded);

            console.log(`${originalValue} -> 快速版本: ${fastDecoded}, 兼容版本: ${compatibleDecoded} ${isEqual ? '✓' : '✗'}`);

            if (isEqual) {
                passCount++;
            } else {
                failCount++;
            }
        }

        console.log(`兼容版本测试结果: ${passCount} 通过, ${failCount} 失败`);
        return failCount === 0;
    }
    
    /**
     * JavaScript版本的解码函数，模拟shader中的逻辑
     * 提供两种实现：快速版本（使用TypedArray）和兼容版本（纯数学运算）
     */
    private static decodeFloatFromRGBA(rgba: [number, number, number, number]): number {
        // 快速版本：使用TypedArray（推荐）
        return this.decodeFloatFromRGBA_Fast(rgba);

        // 如果需要验证兼容版本的正确性，可以使用：
        // return this.decodeFloatFromRGBA_Compatible(rgba);
    }

    /**
     * 快速版本：使用TypedArray进行位级转换
     */
    private static decodeFloatFromRGBA_Fast(rgba: [number, number, number, number]): number {
        // 重构32位整数（小端序）
        const intValue = rgba[0] | (rgba[1] << 8) | (rgba[2] << 16) | (rgba[3] << 24);

        // 将整数位模式重新解释为浮点数
        const buffer = new ArrayBuffer(4);
        const intView = new Int32Array(buffer);
        const floatView = new Float32Array(buffer);

        intView[0] = intValue;
        return floatView[0];
    }

    /**
     * 兼容版本：使用纯数学运算，模拟shader中的IEEE 754解析
     */
    private static decodeFloatFromRGBA_Compatible(rgba: [number, number, number, number]): number {
        const [byte0, byte1, byte2, byte3] = rgba;

        // 提取符号位（第31位）
        const sign = (byte3 >= 128) ? -1 : 1;

        // 提取指数（第30-23位）
        const exp_high = byte3 % 128; // 去掉符号位
        const exp_low = Math.floor(byte2 / 128); // byte2的最高位
        const exponent = exp_high * 2 + exp_low;

        // 提取尾数（第22-0位）
        const mantissa_high = byte2 % 128; // byte2的低7位
        const mantissa_mid = byte1;
        const mantissa_low = byte0;

        // 重构尾数值（23位）
        const mantissa = (mantissa_high * 65536 + mantissa_mid * 256 + mantissa_low) / 8388608; // 2^23

        // 处理特殊情况
        if (exponent === 0) {
            // 非规格化数或零
            if (mantissa === 0) {
                return sign * 0; // 正零或负零
            } else {
                // 非规格化数：(-1)^S × 2^(-126) × (0.mantissa)
                return sign * Math.pow(2, -126) * mantissa;
            }
        } else if (exponent === 255) {
            // 无穷大或NaN
            if (mantissa === 0) {
                return sign * Infinity; // 无穷大
            } else {
                return NaN; // NaN
            }
        } else {
            // 规格化数：(-1)^S × 2^(E-127) × (1.mantissa)
            const realExponent = exponent - 127;
            return sign * Math.pow(2, realExponent) * (1 + mantissa);
        }
    }
    
    /**
     * 比较两个浮点数是否相等（处理NaN和精度问题）
     */
    private static floatEquals(a: number, b: number): boolean {
        // 处理NaN
        if (isNaN(a) && isNaN(b)) return true;
        if (isNaN(a) || isNaN(b)) return false;
        
        // 处理无穷大
        if (a === Infinity && b === Infinity) return true;
        if (a === -Infinity && b === -Infinity) return true;
        if ((a === Infinity || a === -Infinity) && (b === Infinity || b === -Infinity)) return false;
        
        // 处理零
        if (a === 0 && b === 0) return true;
        
        // 对于IEEE 754标准的位级编码，应该是完全相等的
        return a === b;
    }
    
    /**
     * 生成shader测试代码
     */
    static generateShaderTestCode(): string {
        return `
// 在shader中测试解码函数的示例代码
void testDecodeFloatFromRGBA() {
    // 测试一些已知的编码值
    vec4 testColor1 = vec4(0.0, 0.0, 128.0/255.0, 63.0/255.0); // 编码后的1.0
    float decoded1 = decodeFloatFromRGBA(testColor1);
    
    vec4 testColor2 = vec4(195.0/255.0, 245.0/255.0, 72.0/255.0, 64.0/255.0); // 编码后的3.14159
    float decoded2 = decodeFloatFromRGBA(testColor2);
    
    // 在实际使用中，您可以通过改变输出颜色来验证结果
    // 例如：如果decoded1接近1.0，输出绿色；否则输出红色
    if (abs(decoded1 - 1.0) < 0.001) {
        gl_FragColor = vec4(0.0, 1.0, 0.0, 1.0); // 绿色表示成功
    } else {
        gl_FragColor = vec4(1.0, 0.0, 0.0, 1.0); // 红色表示失败
    }
}
`;
    }
}
