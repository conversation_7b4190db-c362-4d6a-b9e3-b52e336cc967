import DataTexture from "../SimpleLightSystem/DataTexture";

/**
 * 测试32位浮点数编码解码的正确性
 */
export class FloatEncodingTest {
    
    /**
     * 测试各种浮点数值的编码解码
     */
    static testFloatEncoding() {
        console.log("=== 32位浮点数编码解码测试 ===");
        
        // 测试用例：包含各种类型的浮点数
        const testValues = [
            0.0,           // 零
            1.0,           // 正整数
            -1.0,          // 负整数
            3.14159,       // 正小数
            -3.14159,      // 负小数
            0.000001,      // 很小的正数
            -0.000001,     // 很小的负数
            1000000.0,     // 很大的正数
            -1000000.0,    // 很大的负数
            123.456789,    // 普通小数
            -123.456789,   // 负普通小数
            Number.MAX_VALUE,      // 最大值
            Number.MIN_VALUE,      // 最小正值
            -Number.MAX_VALUE,     // 最大负值
            Infinity,              // 正无穷
            -Infinity,             // 负无穷
            NaN                    // 非数字
        ];
        
        console.log("测试值数量:", testValues.length);
        console.log("原始值 -> 编码后的RGBA -> 验证是否正确");
        console.log("----------------------------------------");
        
        let passCount = 0;
        let failCount = 0;
        
        for (let i = 0; i < testValues.length; i++) {
            const originalValue = testValues[i];
            
            // 使用DataTexture的编码函数
            const encoded = DataTexture.EncodeFloatToRGBA(originalValue);
            
            // 模拟shader中的解码过程（JavaScript版本）
            const decoded = this.decodeFloatFromRGBA(encoded);
            
            // 检查是否相等（对于NaN需要特殊处理）
            const isEqual = this.floatEquals(originalValue, decoded);
            
            console.log(`${i + 1}. ${originalValue} -> [${encoded.join(', ')}] -> ${decoded} ${isEqual ? '✓' : '✗'}`);
            
            if (isEqual) {
                passCount++;
            } else {
                failCount++;
                console.error(`  错误: 期望 ${originalValue}, 得到 ${decoded}`);
            }
        }
        
        console.log("----------------------------------------");
        console.log(`测试结果: ${passCount} 通过, ${failCount} 失败`);
        console.log(`成功率: ${((passCount / testValues.length) * 100).toFixed(2)}%`);
        
        return failCount === 0;
    }
    
    /**
     * JavaScript版本的解码函数，模拟shader中的逻辑
     */
    private static decodeFloatFromRGBA(rgba: [number, number, number, number]): number {
        // 重构32位整数（小端序）
        const intValue = rgba[0] | (rgba[1] << 8) | (rgba[2] << 16) | (rgba[3] << 24);
        
        // 将整数位模式重新解释为浮点数
        const buffer = new ArrayBuffer(4);
        const intView = new Int32Array(buffer);
        const floatView = new Float32Array(buffer);
        
        intView[0] = intValue;
        return floatView[0];
    }
    
    /**
     * 比较两个浮点数是否相等（处理NaN和精度问题）
     */
    private static floatEquals(a: number, b: number): boolean {
        // 处理NaN
        if (isNaN(a) && isNaN(b)) return true;
        if (isNaN(a) || isNaN(b)) return false;
        
        // 处理无穷大
        if (a === Infinity && b === Infinity) return true;
        if (a === -Infinity && b === -Infinity) return true;
        if ((a === Infinity || a === -Infinity) && (b === Infinity || b === -Infinity)) return false;
        
        // 处理零
        if (a === 0 && b === 0) return true;
        
        // 对于IEEE 754标准的位级编码，应该是完全相等的
        return a === b;
    }
    
    /**
     * 生成shader测试代码
     */
    static generateShaderTestCode(): string {
        return `
// 在shader中测试解码函数的示例代码
void testDecodeFloatFromRGBA() {
    // 测试一些已知的编码值
    vec4 testColor1 = vec4(0.0, 0.0, 128.0/255.0, 63.0/255.0); // 编码后的1.0
    float decoded1 = decodeFloatFromRGBA(testColor1);
    
    vec4 testColor2 = vec4(195.0/255.0, 245.0/255.0, 72.0/255.0, 64.0/255.0); // 编码后的3.14159
    float decoded2 = decodeFloatFromRGBA(testColor2);
    
    // 在实际使用中，您可以通过改变输出颜色来验证结果
    // 例如：如果decoded1接近1.0，输出绿色；否则输出红色
    if (abs(decoded1 - 1.0) < 0.001) {
        gl_FragColor = vec4(0.0, 1.0, 0.0, 1.0); // 绿色表示成功
    } else {
        gl_FragColor = vec4(1.0, 0.0, 0.0, 1.0); // 红色表示失败
    }
}
`;
    }
}
