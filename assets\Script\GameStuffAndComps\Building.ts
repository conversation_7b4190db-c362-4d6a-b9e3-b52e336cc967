// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import GameManager from "../GameManager";
import GameUtils, { UnitInfo } from "../Game/GameUtils";
import LocalUtils, { TweenObject } from "../LocalUtils";
import AntiExtrusionManager from "../Game/AntiExtrusionManager";
import GameDirector from "../Game/GameDirector";
import GameStuffManager from "../Game/GameStuffManager";
import Hero from "./Hero";
import { ResourceInfo, ResourceType } from "./Resource";
import StatueUnlockManager from "../Game/StatueUnlockManager";
import StorageArea from "./StorageArea";
import Stuff from "./Stuff";
import Unit, { CanGetHurtBehavior } from "./Unit";
import ColorFlashSync from "./Comps/ColorFlashSync";
import CustomSpriteData from "../Utils/CustomSpriteData";

const {ccclass, property} = cc._decorator;

@ccclass
export default class Building extends Stuff implements CanGetHurtBehavior {

    @property(cc.Boolean)
    isStatue = false;
    @property(cc.Boolean)
    isTrigger = false;

    @property(cc.Integer)
    statueRef = 0;

    @property(cc.Float)
    unlockDistance = 250;


    get leftCost(): number {
        return this.unlockMaxProgress - this.unlockProgress;
    }

    set isCanUseCoinUnlock(value: boolean) {
        this._isCanUseCoinUnlock = value;
    }

    stockadeOpenDistance = 330;

    stockadeGroupIndex = 0;
    stockadeIndexInList = -1;
    isStockadeActive = false;

    img: cc.Sprite = null;
    shadowNode: cc.Node = null;
    baseNode: cc.Node = null;
    maskNode: cc.Node = null;
    stockadeNode: cc.Node = null;

    isCanUnlock = false;
    isCanGetHurt = false;

    isUnlockFinished = false;
    isStockadeUnlock = false;

    unlockProgress = 0;
    unlockMaxProgress = 1000;

    nowHP = 3000;
    maxHP = 3000;

    isBeDestroyed = false;
    isJustGetHurt = false;
    getHurtTime = 5;

    // 餐桌
    isTable = false;
    tableIndex = -1;

    protected _isFirstUnlock = false;
    protected _isFirstUnlocked = false;
    protected _isUnlocking = false;

    protected _nextCoinTime = 0;
    protected _isCanUseCoinUnlock = false;
    protected _isUseNextCoin = false;
    protected _isUsingNextCoin = false;

    protected _nextResourceTime = 0;
    protected _isUseNextResource = false;
    protected _isUsingNextResource = false;

    private _isMainHeroComing = false;

    // 栅栏
    isStockadeInWater = false;
    private _isStockadeOpenStateRefresh = true;
    private _isStockadeToOpen = true;
    private _stockadeOpenProgress = 1;
    private _stockadeOpenTweenObject: TweenObject<number> = null;

    // 大门
    private _isGateAnimPlaying = false;
    private _isGateToOpen = false;
    private _isGateOpen = false;
    
    // 烤架
    private _grillMeatNum = 0;
    // private _grillMeatNumLimit = 30;
    // private _grillMeatViewNum = 0;
    private _grillMeatTime = 0;
    private _grillMeatInterval = 0.7;

    // private _workbenchTime = 0;
    // private _workbenchCDTime = 1;
    // private _workbenchRunCostTime = 0.6;
    // private _isWorkbenchRun = false;
    // private _isWorkbenchRunning = false;
    // private _workbenchRunningTime = 0;

    // private _isWorkbenchResourceFlyIn = false;
    // private _workbenchInAreaResource: ResourceInfo = null;
    // private _isWorkbenchResourceTransform = false;
    // private _isWorkbenchResourceFlyOut = false;

    // private _laserTime = 0;
    // private _laserInterval = 0.1;
    // private _laserEffNodes: cc.Node[] = [];
    // private _lasetTrgEnemys: UnitInfo[] = [];
    // private _laserTrgPoss: cc.Vec2[] = [];
    // private _isLasersHiding: boolean[] = [];

    private _isCookerCooking = false;


    protected override InitOnLoad() {
        super.InitOnLoad();
        let img = this.node.getChildByName('img');
        if(img) {
            this.img = img.getComponent(cc.Sprite);
        }
        let center = this.node.getChildByName('center');
        if(center) {
            this.shadowNode = center.getChildByName('shadow_1');
            this.baseNode = center.getChildByName('baseNode');
            this.maskNode = center.getChildByName('mask');
            if(this.maskNode) {
                let stockadeNode = this.maskNode.getChildByName('stockadeNode');
                if(stockadeNode) {
                    this.stockadeNode = stockadeNode;
                }
            }
        } else {
            let stockadeNode = this.node.getChildByName('stockadeNode');
            if(stockadeNode) {
                this.stockadeNode = stockadeNode;
            }
        }
        this._rootPosition = cc.v2(this.node.position);
        let rbNode = this.node;
        if(rbNode) {
            let polygonCollider = rbNode.getComponent(cc.PhysicsPolygonCollider);
            if(polygonCollider && this.statueRef != 100) {
                GameManager.instance.LateFrameCall(()=>{
                    AntiExtrusionManager.instance.CreateAPolygonFromCollider(polygonCollider, true);
                });
            }
        }
        if(this.statueRef == 2) {
            this.hitboxRadius = 280;
        } else if(this.statueRef == 100) {
            this.hitboxRadius = 30;
            this.maxHP = 20;
        } else if(this.statueRef == 101) {
            this.hitboxRadius = 150;
            this.maxHP = 1500;
            this.nowHP = 1500;
        } else if(this.statueRef == 102 || this.statueRef == 110) {
            this.hitboxRadius = 90;
            this.maxHP = 2500;
            this.nowHP = 2500;
        }
        if((this.statueRef >= 2 && this.statueRef <= 5) || this.statueRef == 100 || this.statueRef == 101 || this.statueRef == 102 ||
              this.model_id >= 31 && this.model_id <= 40 || this.model_id == 11 || this.model_id == 12) {
            this.gameUpdateCallbackId = GameManager.instance.AddGameUpdate('Building', (dt: number)=>{
                this.gameUpdate(dt);
            }, false, 1100);
            // if(this.statueRef == 201 || this.statueRef == 202) {
            if(this.statueRef == 101 || this.statueRef == 102) {
                this.isStockadeUnlock = true;
                this.isCanUnlock = true;
                this.isUnlockFinished = true;
            } else if(this.statueRef == 100) {
                this.isCanUnlock = true;
                this.unlockMaxProgress = 1;
            }
            if(this.model_id >= 31 && this.model_id <= 40) {
                this.ShowHidePlate(0, true);
                this.ShowHidePlate(1, true);
            }
            this._stockadeOpenTweenObject = new TweenObject(1, (value: number)=>{
                this._stockadeOpenProgress = value;
                if(this.stockadeNode) {
                    this.stockadeNode.y = value * (-190);
                }
            });
        }
    }

    override Init(isInWater = false) {
        this._rootPosition = cc.v2(this.node.position);
        this.isStockadeInWater = isInWater;
        if(isInWater) {
            this.unlockDistance = 180;
        }
        let shadow1 = this.node.getChildByName('center').getChildByName('shadow_1');
        if(shadow1) {
            shadow1.active = !isInWater;
        }
        let shadow2 = this.node.getChildByName('center').getChildByName('shadow_2');
        if(shadow2) {
            shadow2.active = isInWater;
        }
        if(this.maskNode) {
            this.maskNode.active = false;
        }
    }

    protected override gameUpdate(dt: number) {
        if(this.isTrigger) {
            // if(this.isCanUnlock && !this.isUnlockFinished && !this._isUnlocking) {
                this.TryTrigger();
            // }
        } else if(this.isStatue) {
            let isAllow = true;
            if(this.statueRef == 100 && !this.isStockadeInWater) {
                isAllow = GameDirector.instance.isAllowUnlockStockade;
            }
            if(this.isCanUnlock && isAllow && !this.isUnlockFinished) {
                if(!this._isCanUseCoinUnlock && this.JudgDistance()) {
                    if(!this._isFirstUnlock) {
                        this._isFirstUnlock = true;
                        this._isCanUseCoinUnlock = true;
                    }
                }
                if(this._isCanUseCoinUnlock) {
                    let coinTime = this.GetCoinTime();
                    if(this._isUsingNextCoin) {
                        this._nextCoinTime += dt;
                        if(this._nextCoinTime >= coinTime) {
                            this._isUsingNextCoin = false;
                        }
                    }
                    this._isUseNextCoin = true;
                    while(this._isUseNextCoin && !this._isUsingNextCoin) {
                        this._isUseNextCoin = this.TryUseCoinUnlock();
                        if(this._isUseNextCoin) {
                            coinTime = this.GetCoinTime();
                            // let coinTime = 0.03;
                            this._nextCoinTime -= coinTime;
                            if(this._nextCoinTime >= coinTime) {
                                this._isUsingNextCoin = false;
                            } else {
                                this._isUsingNextCoin = true;
                            }
                        }
                    }
                }
            }
            if(this.statueRef == 2) { // 中心建筑：大锅

                let resourceTime = this.GetReourceTime();
                if(this._isUsingNextResource) {
                    this._nextResourceTime += dt;
                    if(this._nextResourceTime >= resourceTime) {
                        this._isUsingNextResource = false;
                    }
                }
                this._isUseNextResource = true;
                while(this._isUseNextResource && !this._isUsingNextResource) {
                    let isSucceed = false;
                    if(this.JudgDistance()) {
                        isSucceed = this.TryPutInMeat();
                    }
                    this._isUseNextResource = isSucceed;
                    if(this._isUseNextResource) {
                        resourceTime = this.GetReourceTime();
                        this._nextResourceTime -= resourceTime;
                        if(this._nextResourceTime >= resourceTime) {
                            this._isUsingNextResource = false;
                        } else {
                            this._isUsingNextResource = true;
                        }
                    }
                }

                // if(this._grillMeatNum > 0) {
                //     if(this._grillMeatTime < this._grillMeatInterval) {
                //         if(this._grillMeatTime == 0) {
                //             LocalUtils.PlaySoundDelay(0.4, 'bbq', false, 0.5);
                //         }
                //         this._grillMeatTime += dt;
                //     } else {
                //         this.GrillMeatDone();
                //         this._grillMeatTime = 0;
                //     }
                // }

                if(this._grillMeatNum >= 10) {
                    if(!this._isCookerCooking) {
                        this._isCookerCooking = true;
                        this._grillMeatTime = -1.5;
                        GameManager.instance.LateTimeCallOnce(()=>{
                            let cooker = this.node.getChildByName('cooker');
                            if(cooker) {
                                let spine = cooker.getComponent(sp.Skeleton);
                                if(spine) spine.setAnimation(0, 'cook', true);
                                cooker.scaleX = Math.abs(cooker.scaleX);
                            }
                            let anim = GameUtils.rootGameWorldUI.demandBubble_meat.getComponent(cc.Animation);
                            if(anim) {
                                anim.play('demandBubble_disappear');
                            }
                            LocalUtils.PlaySound('buy_3');
                            GameManager.instance.scheduleOnce(()=>{
                                GameUtils.rootGameWorldUI.demandBubble_meat.active = false;
                            }, 3);
                            
                        }, 0.3);
                    }
                    let storageFoodNum = GameUtils.rootGameWorld.foodStorageArea.GetNumOfResource().resourceNums[ResourceType.food];
                    if(storageFoodNum < 4) {
                        if(this._grillMeatTime < this._grillMeatInterval) {
                            // if(this._grillMeatTime == 0) {
                            //     LocalUtils.PlaySoundDelay(0.4, 'bbq', false, 0.5);
                            // }
                            this._grillMeatTime += dt;
                        } else {
                            this.GrillMeatDone();
                            this._grillMeatTime = 0;
                        }
                    }
                }
            }
        }
        if(this.model_id >= 31 && this.model_id <= 40) { // 是桌子
            let tableIndex = this.model_id - 31;
            let seat1Index = tableIndex * 2;
            let seat2Index = tableIndex * 2 + 1;
            if(this.JudgDistance()) {
                GameStuffManager.instance.TryTakeSeatPlate(seat1Index);
                GameStuffManager.instance.TryTakeSeatPlate(seat2Index);
            }
        } else if(this.model_id == 11) { // 洗碗池
            if(this.JudgDistance()) {
                this.UseResourceUpdate(dt, ()=>{
                    return this.TryUseResrouce(ResourceType.plate, GameUtils.mainHeroBackpack, ()=>{
                        // console.log('洗碗！');
                        this.PlayWashPlate();
                    }, cc.v2(40, 35));
                });
            }
        }
    }

    UseResourceUpdate(dt: number, isSucceedCallback: (...args: any[])=>boolean) {
        let resourceTime = this.GetReourceTime();
        if(this._isUsingNextResource) {
            this._nextResourceTime += dt;
            if(this._nextResourceTime >= resourceTime) {
                this._isUsingNextResource = false;
            }
        }
        this._isUseNextResource = true;
        let loopTimes = 0;
        while(loopTimes < 100 && (this._isUseNextResource && !this._isUsingNextResource)) {
            loopTimes += 1;
            let isSucceed = isSucceedCallback();
            this._isUseNextResource = isSucceed;
            if(this._isUseNextResource) {
                resourceTime = this.GetReourceTime();
                this._nextResourceTime -= resourceTime;
                if(this._nextResourceTime >= resourceTime) {
                    this._isUsingNextResource = false;
                } else {
                    this._isUsingNextResource = true;
                }
            }
        }
        if(loopTimes >= 100) console.warn(`UseResourceUpdate(): loopTimes >= 100 ! [${this.node.name}]`);
    }

    ReadyToDestroy() {
        // if(this.img) {
        //     cc.tween(new TweenObject<number>(0, (value: number)=>{
        //         this.img.node.color = cc.color(255, 255 - value * 200, 255 - value * 200);
        //     })).to(0.5, {value: 1}).start();
        //     cc.tween(this.node).set({scale: 1.05}).to(0.4, {scale: 1.2}).to(0.1, {scale: 1.65, opacity: 0}).start();
        // }
    }
    
    protected override onDestroy(): void {
        super.onDestroy();
    }

    TryTrigger() {
        let distance = GameUtils.Fake3dDistanceExpand(GameUtils.mainHero.script.rootPosition, this.rootPosition);
        if(distance < this.unlockDistance) {
            // if(this.unlockProgress < this.unlockMaxProgress) {
            //     this.unlockProgress += 1;
                this.OnTrigger();
            // }
        }
        if(this.statueRef == 100) {
            if(distance < this.stockadeOpenDistance) {
                this._isMainHeroComing = true;
            } else if(distance > this.stockadeOpenDistance + 40) {
                this._isMainHeroComing = false;
            }
            
        }
        if(this.statueRef != 100 || !this.isStockadeInWater) {
            this.RefreshGateState();
        }
        this.TryPlayGateAnim();
    }

    OnTrigger() {
        // if(this.statueRef == 5) {
        //     this._isUnlocking = true;
        //     let node = this.node.getChildByName('anim').getChildByName('eff_jian');
        //     let anim = node.getComponent(cc.Animation);
        //     anim.play();
        //     this.scheduleOnce(()=>{
        //         this.isUnlockFinished = true;
        //         StatueUnlockManager.instance.StatueUnlock(this.statueRef);

        //         let statue6 = GameUtils.rootGuideToStatue.statues[5];
        //         statue6.PlayFirstUnlock(()=>{
        //             statue6.isCanUseCoinUnlock = true;
        //         });
        //     }, 1);
        //     this.HideOutsideLight();
        if(this.statueRef == 100) {
        } else if(this.statueRef == 101) {
        } else if(this.statueRef == 102) {
        } else {
            this.isUnlockFinished = true;
            StatueUnlockManager.instance.StatueUnlock(this.statueRef);
        }
    }

    RefreshGateState() {
        if(this._isMainHeroComing || (this.node.name == 'gate_1' && GameDirector.instance.isNPCComing)) {
            if(this.statueRef == 100) {
                if(!this._isStockadeToOpen || this._isStockadeOpenStateRefresh) {
                    this._isStockadeOpenStateRefresh = false;
                    this._isStockadeToOpen = true;
                    this.OpenStockade();
                }
            } else if(this.statueRef == 110) {
                if(!this._isGateToOpen) {
                    this._isGateToOpen = true;
                }
            }
        } else {
            if(this.statueRef == 100) {
                if(this._isStockadeToOpen || this._isStockadeOpenStateRefresh) {
                    this._isStockadeOpenStateRefresh = false;
                    this._isStockadeToOpen = false;
                    this.CloseStockade();
                }
            } else if(this.statueRef == 110) {
                if(this._isGateToOpen) {
                    this._isGateToOpen = false;
                }
            }
        }
    }

    OnUnlockProgressFull() {
    }

    TryUseCoinUnlock() {
        if(this.JudgDistance()) {
            // if(GameUtils.coinNum - 1 >= 0 && this.unlockProgress < this.unlockMaxProgress) {
            if(this.unlockProgress < this.unlockMaxProgress ) {
                // console.log(`progress1: ${this.unlockProgress} / ${this.unlockMaxProgress}`);
                
                let pos = this.rootPosition;
                let isSucceed = false;
                if(this.statueRef == 100) {
                    isSucceed = GameUtils.mainHeroBackpack.ResourceCost(ResourceType.wood, pos, 0, ()=>{
                        LocalUtils.PlaySound('wood');
                        // cc.Tween.stopAllByTarget(this.node);
                        // cc.tween(this.node).set({scale: 1.05}).to(0.08, {scale: 1.1}).to(0.1, {scale: 1}).start();
                        // StatueUnlockManager.instance.AddStatueUnlockProgress(this.statueRef, 1);
                        this.OnUnlock();
                    }, false);
                } else if(this.nowHP > 0) {
                    isSucceed = GameUtils.mainHeroBackpack.ResourceCost(ResourceType.coin, pos, 50, ()=>{
                        LocalUtils.PlaySound('coin');
                        // cc.Tween.stopAllByTarget(this.node);
                        // cc.tween(this.node).set({scale: 1.05}).to(0.08, {scale: 1.1}).to(0.1, {scale: 1}).start();
                        StatueUnlockManager.instance.AddStatueUnlockProgress(this.statueRef, 1);
                    });
                }
                if(isSucceed) {
                    this.unlockProgress += 1;
                    if(this.unlockProgress >= this.unlockMaxProgress) {
                        this.OnUnlockProgressFull();
                    }
                }
                // if(!this._isFirstUnlock) {
                //     this._isFirstUnlock = true;
                // }
                return true;
            }
        }
        return false;
    }

    TryPutInMeat(srcStorageArea?: StorageArea) {
        if(!srcStorageArea) {
            srcStorageArea = GameUtils.mainHeroBackpack;
        }
        let isSucceed = this.TryUseResrouce(ResourceType.rawMeat, srcStorageArea, ()=>{}, cc.v2(-70, 160));
        if(isSucceed) {
            this._grillMeatNum += 1;
        }
        return isSucceed;
    }

    TryUseResrouce(type: ResourceType, srcStorageArea: StorageArea, onCompliteCallback?: Function, costPosOffset: cc.Vec2 = cc.Vec2.ZERO) {
        let pos = this.rootPosition.add(costPosOffset);
        let isSucceed = false;
        isSucceed = srcStorageArea.ResourceCost(type, pos, 50, ()=>{
            if(type == ResourceType.coin) {
                LocalUtils.PlaySound('coin');
            } else if(type == ResourceType.wood) {
                LocalUtils.PlaySound('wood');
            } else if(type == ResourceType.meat || type == ResourceType.rawMeat || type == ResourceType.food) {
                LocalUtils.PlaySound('meat');
            }
            onCompliteCallback && onCompliteCallback();
        }, false);
        return isSucceed;
    }

    JudgDistance() {
        let distance = GameUtils.mainHero.script.rootPosition.sub(this.rootPosition).len();
        return distance < this.unlockDistance;
    }

    UseCoinUnlock() {
        GameUtils.rootGameWorldUI.PlayCoinUnlock(GameUtils.mainHero.script.centerPosition, this.rootPosition.add(cc.v2(0, -40)));
        GameManager.instance.LateTimeCallOnce(()=>{
            LocalUtils.PlaySound('coin');
            // cc.Tween.stopAllByTarget(this.node);
            // cc.tween(this.node).set({scale: 1.05}).to(0.08, {scale: 1.2}).to(0.1, {scale: 1}).start();
            StatueUnlockManager.instance.AddStatueUnlockProgress(this.statueRef, 1);
        }, 0.5);
    }


    // TryWorkbenchWork() {
    //     // let bowNum = GameUtils.rootGameWorld.bowStorageArea.GetNumOfResource().resourceNums[ResourceType.bow];
    //     // if(bowNum < 3 || GameDirector.instance.isConveyerUnlock) {
    //         this.WorkbenchResourceTransform();
    //         this._isWorkbenchResourceTransform = true;
    //     // }
    // }

    // WorkbenchResourceTransform() {
    //     // console.log('生产弓！');
    //     // GameStuffManager.instance.CreateBow(this.rootPosition, true, this.statueRef - 2);
    // }

    // WorkbenchResourceFly() {
    // }

    // ShootLasers() {
    //     // let targetPos = cc.v2(GameUtils.rootGuideToStatue.node.position);
    //     for(let i = 0; i < 4; i++) {
    //         let targetEnemy = this.FindLaserNewTargetEnemy(i);
    //         if(targetEnemy) {
    //             let targetPos = targetEnemy.script.rootPosition;
    //             let laserEffNode = GameUtils.rootGameWorldUI.PlayLaserLine(this.rootPosition, 750, targetPos, 50);
    //             this._lasetTrgEnemys.push(targetEnemy);
    //             this._laserTrgPoss.push(targetPos);
    //             this._laserEffNodes.push(laserEffNode);
    //             this._isLasersHiding[i] = false;
    //             targetEnemy.script.GetHurt(null, 20, 6, targetPos.sub(this.rootPosition).normalize());
    //         }
    //     }
    // }

    // UpdateLasers() {
    //     if(this._laserEffNodes.length > 0) {
    //         this._lasetTrgEnemys = [];
    //         this._laserTrgPoss = [];
    //         this._laserEffNodes.forEach((e, i)=>{
    //             let targetEnemy = this.FindLaserNewTargetEnemy(i);
    //             if(targetEnemy) {
    //                 let targetPos = targetEnemy.script.rootPosition;
    //                 GameUtils.rootGameWorldUI.PlayLaserLineUpdate(e, this.rootPosition, 750, targetPos, 50);
    //                 this._lasetTrgEnemys.push(targetEnemy);
    //                 this._laserTrgPoss.push(targetPos);
    //                 targetEnemy.script.GetHurt(null, 20, 6, targetPos.sub(this.rootPosition).normalize());
    //                 this._isLasersHiding[i] = false;
    //             } else {
    //                 if(!this._isLasersHiding[i]) {
    //                     this._isLasersHiding[i] = true;
    //                     GameUtils.rootGameWorldUI.PlayLaserLineHide(e);
    //                 }
    //             }
    //         });
    //     }
    // }

    // FindLaserNewTargetEnemy(laserIndex: number) {
    //     let trgEnemy: UnitInfo = null;
    //     if(this._laserTrgPoss.length <= 0) {
    //         trgEnemy = GameStuffManager.instance.FindAEnemy(this.rootPosition);
    //     } else {
    //         let enemys: UnitInfo[] = [];
    //         let lastPos = this._laserTrgPoss[laserIndex];
    //         if(lastPos) {
    //             enemys = GameStuffManager.instance.TryGetEnemysInArea(lastPos, 100);
    //             if(enemys.length <= 0) {
    //                 enemys = GameStuffManager.instance.TryGetEnemysInArea(lastPos, 300);
    //             }
    //             if(enemys.length > 0) {
    //                 let minAngleDiff = 180;
    //                 enemys.forEach((e)=>{
    //                     let angleDiff = this.rootPosition.sub(lastPos).angle(e.script.rootPosition.sub(lastPos));
    //                     if(angleDiff < minAngleDiff) {
    //                         minAngleDiff = angleDiff;
    //                         let isRepeat = false;
    //                         this._lasetTrgEnemys.forEach((other, index)=>{
    //                             if(index != laserIndex) {
    //                                 if(e == other) {
    //                                     isRepeat = true;
    //                                 }
    //                             }
    //                         });
    //                         if(!isRepeat) {
    //                             trgEnemy = e;
    //                         }
    //                     }
    //                 });
    //             } else {
    //             }
    //         }
    //         if(!trgEnemy) {
    //             enemys = GameStuffManager.instance.TryGetEnemysInArea(this.rootPosition, 1200);
    //             if(enemys.length <= 0) {
    //                 enemys = GameStuffManager.instance.TryGetEnemysInArea(this.rootPosition, 2200);
    //             }
    //             if(enemys.length > 0) {
    //                 trgEnemy = LocalUtils.RandomAMember(enemys);
    //             }
    //         }
    //     }
    //     return trgEnemy;
    // }

    GrillMeatDone() {
        // this._grillMeatNum -= 1;
        // this._grillMeatViewNum -= 1;
        // this.RefreshGrillMeatImg();
        GameStuffManager.instance.CreateDropResource(this.rootPosition.add(cc.v2(-60, 200)), cc.v2(), ResourceType.food, GameUtils.rootGameWorld.foodStorageArea, 0, false);
    }

    // RefreshGrillMeatImg() {
    //     let img_meat_1 = this.node.getChildByName('img_meat_1');
    //     let img_meat_2 = this.node.getChildByName('img_meat_2');
    //     let img_meat_3 = this.node.getChildByName('img_meat_3');
    //     let meatNum = this._grillMeatViewNum;
    //     if(img_meat_1) img_meat_1.active = meatNum >= 1;
    //     if(img_meat_2) img_meat_2.active = meatNum >= 2;
    //     if(img_meat_3) img_meat_3.active = meatNum >= 3;
    // }
    
    OpenStockade(isFast = false) {
        let mask: cc.Mask;
        cc.Tween.stopAllByTarget(this._stockadeOpenTweenObject);
        if(this.maskNode) {
            this.maskNode.active = true;
            mask = this.maskNode.getComponent(cc.Mask);
            if(mask) {
                mask.enabled = true;
            }
        }
        let diff = Math.abs(this._stockadeOpenProgress - 1);
        if(isFast) {
            cc.tween(this._stockadeOpenTweenObject).to(diff * 0.15, {value: 1}).call(()=>{
                if(this.maskNode) {
                    this.maskNode.active = false;
                }
            }).start();
        } else {
            cc.tween(this._stockadeOpenTweenObject).to(diff * 0.25, {value: 1}).call(()=>{
                if(this.maskNode) {
                    this.maskNode.active = false;
                }
            }).start();
        }
    }

    CloseStockade() {
        let mask: cc.Mask;
        cc.Tween.stopAllByTarget(this._stockadeOpenTweenObject);
        if(this.maskNode) {
            this.maskNode.active = true;
            mask = this.maskNode.getComponent(cc.Mask);
            mask.enabled = true;
        }
        let diff = Math.abs(this._stockadeOpenProgress);
        cc.tween(this._stockadeOpenTweenObject).to(diff * 0.25, {value: 0}).call(()=>{
            if(mask) {
                mask.enabled = false;
            }
        }).start();
    }

    TryPlayGateAnim() {
        if(!this._isGateAnimPlaying) {
            if(this._isGateOpen && !this._isGateToOpen) {
                this.CloseGate();
            } else if(!this._isGateOpen && this._isGateToOpen) {
                this.OpenGate();
            }
        }
    }

    OpenGate() {
        // console.log('OpenGate!');
        let spineNode = this.node.getChildByName('spine');
        if(spineNode) {
            let spine = spineNode.getComponent(sp.Skeleton);
            spine.timeScale = 2;
            // if(this.node.name == 'gate_1' || this.node.name == 'gate_3' || this.node.name == 'gate_4') {
            if(this.node.name == 'gate_1') {
                spine.setAnimation(0, '01_kai', false);
            // } else if(this.node.name == 'gate_2' || this.node.name == 'gate_5') {
            //     spine.setAnimation(0, '02_kai', false);
            }
            this._isGateAnimPlaying = true;
            GameManager.instance.scheduleOnce(()=>{
                this._isGateAnimPlaying = false;
                this._isGateOpen = true;
            }, 0.3);
        }
        // if(this.node.name == 'gate_1') {
        //     let spine_l = GameUtils.rootGameWorld.gate_1_l_Spine;
        //     let spine_r = GameUtils.rootGameWorld.gate_1_r_Spine;
        //     spine_l.timeScale = 2;
        //     spine_r.timeScale = 2;
        //     spine_l.setAnimation(0, 'animation', false);
        //     spine_r.setAnimation(0, 'animation', false);
        //     this._isGateAnimPlaying = true;
        //     GameManager.instance.scheduleOnce(()=>{
        //         this._isGateAnimPlaying = false;
        //         this._isGateOpen = true;
        //     }, 0.3);
        // }
    }

    CloseGate() {
        // console.log('CloseGate!');
        let spineNode = this.node.getChildByName('spine');
        if(spineNode) {
            let spine = spineNode.getComponent(sp.Skeleton);
            spine.timeScale = 2;
            // if(this.node.name == 'gate_1' || this.node.name == 'gate_3' || this.node.name == 'gate_4') {
            if(this.node.name == 'gate_1') {
                spine.setAnimation(0, '01_guan', false);
            // } else if(this.node.name == 'gate_2' || this.node.name == 'gate_5') {
            //     spine.setAnimation(0, '02_guan', false);
            }
            this._isGateAnimPlaying = true;
            GameManager.instance.scheduleOnce(()=>{
                this._isGateAnimPlaying = false;
                this._isGateOpen = false;
            }, 0.3);
        }
        // if(this.node.name == 'gate_1') {
        //     let spine_l = GameUtils.rootGameWorld.gate_1_l_Spine;
        //     let spine_r = GameUtils.rootGameWorld.gate_1_r_Spine;
        //     spine_l.getCurrent(0).timeScale = -1;
        //     spine_r.getCurrent(0).timeScale = -1;
        //     spine_l.getCurrent(0).trackTime = spine_l.getCurrent(0).animationEnd;
        //     spine_r.getCurrent(0).trackTime = spine_r.getCurrent(0).animationEnd;
        //     this._isGateAnimPlaying = true;
        //     GameManager.instance.scheduleOnce(()=>{
        //         this._isGateAnimPlaying = false;
        //         this._isGateOpen = false;
        //     }, 0.3);
        // }
    }

    PlayFirstUnlock(callback?: Function) {
        this._isFirstUnlocked = true;
        // if(this.statueRef == 2) {
        //     let node = this.node.getChildByName('mask').getChildByName('node');
        //     let mask = this.node.getChildByName('mask').getComponent(cc.Mask);
        //     this.scheduleOnce(()=>{
        //         cc.tween(node).to(1.5, {y: -200}).call(()=>{
        //             mask.enabled = false;
        //             callback && callback();
        //         }).start();
        //         LocalUtils.PlaySound('door');
        //         this.scheduleOnce(()=>{
        //             GameUtils.rootGameWorldUI.PlayEffSmoke(this.rootPosition);
        //         }, 0.1);
        //     }, 0.2);
        //     GameUtils.CameraShake(1.5);
        // } else if(this.statueRef == 3) {
        //     let node = this.node.getChildByName('anim').getChildByName('plant');
        //     let anim = node.getComponent(cc.Animation);
        //     anim.play();
        // } else if(this.statueRef == 6) {
        //     let mask = this.node.getChildByName('mask').getComponent(cc.Mask);
        //     let node = this.node.getChildByName('mask').getChildByName('img');
        //     cc.tween(node).to(1.9, {y: -270}).call(()=>{
        //         mask.enabled = false;
        //         callback && callback();
        //     }).start();
        //     LocalUtils.PlaySound('door');
        //     this.scheduleOnce(()=>{
        //         GameUtils.rootGameWorldUI.PlayEffSmoke(this.rootPosition);
        //     }, 0.1);
        //     GameUtils.CameraShake(1.9);
        // } else {
            callback && callback();
        // }
    }

    OnUnlock() {
        if(this.statueRef == 100) {
            this.nowHP = this.maxHP;
            this.isStockadeUnlock = true;
            this.CloseStockade();
            cc.tween(this.stockadeNode).delay(0.2).to(0.15, {scale: 1.35}, {easing: 'quadIn'}).to(0.25, {scale: 1}, {easing: 'quadOut'}).start();
            GameManager.instance.LateTimeCallOnce(()=>{
                this.isTrigger = true;
                this.isCanGetHurt = true;
                this._isStockadeOpenStateRefresh = true;
                if(this.isStockadeInWater) {
                    let collider = this.node.getComponent(cc.PhysicsPolygonCollider);
                    if(collider) {
                        collider.enabled = true;
                        // this.node.group = 'GameWorldDriftingWall';
                        // console.log('开启碰撞！');
                    }
                }
            }, 0.7);
            GameDirector.instance.OnStockadeUnlock();
        }
        if(this.statueRef == 100) {
        } else {
            this.isUnlockFinished = true;
            this.PlayUnlock();
        }
    }

    PlayUnlock() {
        if(this.statueRef >= 2 && this.statueRef <= 5) {
            // let node = this.node.getChildByName('anim').getChildByName('eff_statue_broken');
            // let anim = node.getComponent(cc.Animation);
            // anim.play();
            this.node.active = true;
            this.node.scale = 0.3;
            cc.tween(this.node).to(0.3, {scale: 1}).start();
            GameManager.instance.LateTimeCallOnce(()=>{
                // this.isCanUpgrade = true;
            }, 1);
        } else {
            this.PlayColorFlash(cc.Color.WHITE, 0.4);
        }
    }

    PlayBeAttackByLaser() {
        if(this.statueRef == 2) {
            let spNode = this.node.getChildByName('spine');
            if(spNode) {
                let skeleton = spNode.getComponent(sp.Skeleton);
                skeleton.timeScale = 0.5;
                skeleton.setAnimation(0, 'animation4', false);
            }
        } else if(this.statueRef == 3 || this.statueRef == 4 || this.statueRef == 5 || this.statueRef == 6) {
            let spNode = this.node.getChildByName('spine');
            if(spNode) {
                let skeleton = spNode.getComponent(sp.Skeleton);
                skeleton.timeScale = 0.5;
                skeleton.setAnimation(0, 'animation1', false);
            }
        }
    }

    ShowHidePlate(plateIndex: number, isHide: boolean = false): cc.Node {
        let plates = this.node.getChildByName('plates');
        if(plates) {
            let plate = plates.getChildByName(`plate_${plateIndex + 1}`);
            plate.active = !isHide;
            return plate;
        }
    }

    PlayWashPlate() {
        let node = this.node.getChildByName('smoke_make');
        cc.Tween.stopAllByTarget(node);
        node.opacity = 255;
        cc.tween(node).delay(4).to(0.8, {opacity: 0}).start();
    }

    GetHurt(unit: Unit, value: number) {
        this.nowHP -= value;
        if(this.statueRef == 100) {
            let anim = this.node.getChildByName('center').getComponent(cc.Animation);
            let isDirLeft = unit.attribute.attackDir.x < 0;
            if(isDirLeft) {
                anim.play('tree_gathering_left');
            } else {
                anim.play('tree_gathering_right');
            }
            this.PlayColorFlash(cc.Color.WHITE);
        } else if(this.statueRef == 101 || this.statueRef == 102) {
            this.PlayColorFlash(cc.Color.WHITE);
        } else if(this.statueRef == 110) {
            this.PlayColorFlash(cc.Color.WHITE);
            // if(this.node.name == 'gate_1') {
            //     let gate_1_l = GameUtils.rootGameWorld.gate_1_l;
            //     let gate_1_r = GameUtils.rootGameWorld.gate_1_r;
            //     if(gate_1_l) { gate_1_l.PlayColorFlash(cc.Color.WHITE); }
            //     if(gate_1_r) { gate_1_r.PlayColorFlash(cc.Color.WHITE); }
            // }
        } else if(this.statueRef == 2) {
            this.PlayColorFlash(cc.Color.WHITE, 0.08);
        }
        if(this.nowHP <= 0) {
            if(this.statueRef == 100) {
                this.OpenStockade(true);
                this.isCanGetHurt = false;
                this.isTrigger = false;
                this.isStockadeUnlock = false;
                this.isUnlockFinished = false;
                this.unlockProgress = 0;
            } else if(this.statueRef == 2) {
                if(!this.isBeDestroyed) {
                    this.isBeDestroyed = true;
                    this.PlayBeAttackByLaser();
                    // GameDirector.instance.OnCenterBuildingBeDestroyed();
                }
            }
        }
        this.isJustGetHurt = true;
        this.getHurtTime = 2;
    }

    PlayGrow(callback?: Function) {
        let srcScaleX = this.node.scaleX;
        let srcScaleY = this.node.scaleY;
        this.node.scaleX = srcScaleX * 0.3;
        this.node.scaleY = srcScaleY * 0.3;
        cc.tween(this.node).to(0.3, {scaleX: srcScaleX, scaleY: srcScaleY}).call(()=>{
            callback && callback();
        }).start();
    }

    PlayColorFlash(color: cc.Color, time = 0.1) {
        let img = this.img;
        if(this.stockadeNode) {
            img = this.stockadeNode.getChildByName('img').getComponent(cc.Sprite);
        }
        if(img) {
            let imgNode = img.node;
            this.PlayImgOrSpineColorFlash(imgNode, color, time);
        }
        if(img) {
            let colorFlashSync = img.getComponent(ColorFlashSync);
            if(colorFlashSync) {
                colorFlashSync.targetSprites.forEach((e)=>{
                    this.PlayImgOrSpineColorFlash(e.node, color, time);
                });
                colorFlashSync.targetSpines.forEach((e)=>{
                    this.PlayImgOrSpineColorFlash(e.node, color, time);
                });
            }
        }
        if(this.baseNode) {
            this.PlayImgOrSpineColorFlash(this.baseNode, color, time);
        }
        let spineNode = this.node.getChildByName('spine');
        if(!img && spineNode) {
            this.PlayImgOrSpineColorFlash(spineNode, color, time);
        }
    }

    PlayImgOrSpineColorFlash(imgOrSpineNode: cc.Node, color: cc.Color, time: number) {
        if(!imgOrSpineNode) {
            return;
        }
        let colorMixerComp = imgOrSpineNode.getComponent(CustomSpriteData);
        if(!colorMixerComp) {
            colorMixerComp = imgOrSpineNode.addComponent(CustomSpriteData);
            colorMixerComp.Init(GameUtils.rootGameWorld.customSpriteMaterial, color, 0.5);
            colorMixerComp.LoadNode();
        }
        colorMixerComp.PlayFlashColor(color, 0.7, time);
    }

    // ShowOutsideLight() {
    //     // console.log('111');
    //     if(this.statueRef == 2) {
    //         let light = this.node.getChildByName('mask').getChildByName('node').getChildByName('outsideLight');
    //         light.active = true;
    //     } else if(this.statueRef == 3) {
    //         let light = this.node.getChildByName('outsideLight');
    //         light.active = true;
    //     } else if(this.statueRef == 4) {
    //         let light = this.node.getChildByName('outsideLight');
    //         light.active = true;
    //     } else if(this.statueRef == 5) {
    //         let light = this.node.getChildByName('outsideLight');
    //         light.active = true;
    //     }
    // }

    // HideOutsideLight() {
    //     // console.log('222');
    //     if(this.statueRef == 2) {
    //         let light = this.node.getChildByName('mask').getChildByName('node').getChildByName('outsideLight');
    //         light.active = false;
    //     } else if(this.statueRef == 3) {
    //         let light = this.node.getChildByName('outsideLight');
    //         light.active = false;
    //     } else if(this.statueRef == 4) {
    //         let light = this.node.getChildByName('outsideLight');
    //         light.active = false;
    //     } else if(this.statueRef == 5) {
    //         let light = this.node.getChildByName('outsideLight');
    //         light.active = false;
    //     }
    // }

    GetCoinTime() {
        let coinTime = 0.01 / (this.unlockMaxProgress / 100);
        
        // let coinTime = 0.03 - (this.unlockProgress / this.unlockMaxProgress) * 0.06;
        // if(coinTime < 0.005) {
        //     coinTime = 0.005;
        // }
        // coinTime *= 3;

        // if(this.unlockMaxProgress - this.unlockProgress <= 2) {
        //     coinTime = 0.3;
        // } else if(this.unlockMaxProgress - this.unlockProgress <= 6) {
        //     coinTime = 0.4 - (this.unlockMaxProgress - this.unlockProgress) * 0.05;
        //     // 2 ==> 0.3, 6 ==> 0.1, 10 ==> 0.03
        // } else if(this.unlockMaxProgress - this.unlockProgress <= 10) {
        //     coinTime = 0.22 - (this.unlockMaxProgress - this.unlockProgress) * 0.02;
        // }
        return coinTime;
    }

    GetReourceTime() {
        // return 0.01 / (this.unlockMaxProgress / 100);
        return 0.15;
    }

    GetCenterPosition(): cc.Vec2 {
        if(this.statueRef == 2) {
            return this.rootPosition.add(cc.v2(0, 0));
        } else if(this.statueRef == 100) {
            return this.rootPosition.add(cc.v2(0, 0));
        } else if(this.statueRef == 101 || this.statueRef == 102) {
            return this.rootPosition.add(cc.v2(0, -20));
        } else if(this.statueRef == 110) {
            return this.rootPosition.add(cc.v2(0, -20));
        } else {
            return super.GetCenterPosition();
        }
    }
}
