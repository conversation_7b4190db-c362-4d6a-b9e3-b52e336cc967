// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import GameManager from "../GameManager";
import GameUtils from "../Game/GameUtils";
import LocalUtils from "../LocalUtils";
import UnitComponent from "./UnitComponent";

export default class Attribute extends UnitComponent {

    moveSpeed: number;
    moveSpeedUp: number = 0;

    get moveSpeedValue() {
        return this.moveSpeed * (1 + this.moveSpeedUp / 100);
    }
    get fake3dMoveSpeed() {
        return GameUtils.Fake3dSpeed(this.moveSpeedValue, this.detourMoveDir);
    }
    get finalMoveSpeed() {
        let speed = this.finalAntiExtrusionForceMoveVec2.len();
        if(speed > this.fake3dMoveSpeed) {
            speed = this.fake3dMoveSpeed;
        }
        return speed;
    }

    get maxHp(): number {
        return this._maxHp;
    }
    set maxHp(value: number) {
        this._maxHp = value;
        if(this._maxHp < this.nowHp) {
            this.nowHp = this._maxHp;
        }
    }
    nowHp: number;

    get moveDir() {
        return this._moveDir;
    }
    set moveDir(value: cc.Vec2) {
        // if(this._moveDir.x >= 0 && value.x < 0) {
            this._onDirChengeCallback(this.finalMoveDir.x < 0);
        // } else if(this._moveDir.x < 0 && value.x >= 0) {
        //     this._onDirChengeCallback(false);
        // }
        this._moveDir = value;
    }
    get moveDirAngleOffset(): number {
        return this._moveDirAngleOffset;
    };
    set moveDirAngleOffset(value: number) {
        this._moveDirAngleOffset = value;
        this._onDirChengeCallback(this.finalMoveDir.x < 0);
    }

    get detourMoveDir(): cc.Vec2 {
        return this.moveDir.rotate(this.moveDirAngleOffset * Math.PI / 180);
    }
    get finalMoveDir(): cc.Vec2 {
        return this.finalAntiExtrusionForceMoveVec2.normalize();
    }

    get finalAntiExtrusionForceMoveVec2(): cc.Vec2 {
        let forceScale = 0.5;
        if(this.antiExtrusionForce.len() < 1) {
            return this.detourMoveDir.normalize().mul(this.fake3dMoveSpeed);
        }
        let angle = LocalUtils.Vec2ToAngle(this.antiExtrusionForce, this.detourMoveDir);
        if(Math.abs(angle) <= 90) {
            return this.detourMoveDir.normalize().mul(this.fake3dMoveSpeed).add(this.antiExtrusionForce.mul(forceScale));
        } else {
            let force = this.detourMoveDir.normalize().mul(this.fake3dMoveSpeed).add(this.antiExtrusionForce.mul(forceScale));
            let forceValue = force.len() > 500 ? 500 : force.len();
            if(forceValue < 20) {
                forceValue = 20;
            }
            let newForceDir = cc.v2();
            if(angle > 0) {
                newForceDir = LocalUtils.AngleToVec2(-80, this.antiExtrusionForce).normalize();
            } else {
                newForceDir = LocalUtils.AngleToVec2(80, this.antiExtrusionForce).normalize();
            }
            let newForce = newForceDir.mul(forceValue);
            return newForce;
        }
    }
    
    attackDir: cc.Vec2 = cc.v2(1, 0);
    gatherDir: cc.Vec2 = cc.v2(1, 0);
    antiExtrusionForce: cc.Vec2 = cc.v2(0, 0);

    private _moveDirAngleOffset: number = 0;
    private _maxHp: number;
    private _moveDir: cc.Vec2 = cc.v2(1, 0);

    private _onDirChengeCallback: Function = (isLeft: boolean)=>{};
    private _onHpToZeroCallback: Function = ()=>{};

    private _slowingEffectList: {value: number, timeLeft: number}[] = [];

    // isChangeDirCallWaitTime = false;
    // private _changeDirCallTimeInterval = 0.3;
    // private _changeDirCallTime = 0;
    // private _isChangeDirCall = false;

    baseAttribute: BaseAttribute = {
        baseMoveSpeed: 100,
        baseHp: 100,
    };
    
    Init() {
        this.moveSpeed = this.baseAttribute.baseMoveSpeed;
        this.maxHp = this.baseAttribute.baseHp;
        this.nowHp = this.maxHp;

        this.updateCallbackId = GameManager.instance.AddGameUpdate('Attribute', (dt: number)=>{
            this.gameUpdate(dt);
        });
    }

    Reset() {
        this.moveSpeedUp = 0;
        this.moveDirAngleOffset = 0;
        this.attackDir = cc.v2(1, 0);
        this.gatherDir = cc.v2(1, 0);
        this._moveDir = cc.v2(1, 0);
    }

    gameUpdate(dt: number): void {
        this.RefreshSpeedUp(dt);
    }

    RefreshSpeedUp(dt: number) {
        let toDeleteIndexs: number[] = [];
        let minValue = 0;
        this._slowingEffectList.forEach((e, index)=>{
            if(e.value < minValue) {
                minValue = e.value;
            }
            e.timeLeft -= dt;
            if(e.timeLeft <= 0) {
                toDeleteIndexs.push(index);
            }
        });
        this.moveSpeedUp = minValue;
        if(this.moveSpeedUp < -100) {
            this.moveSpeedUp = -100;
        }
        toDeleteIndexs.forEach((e)=>{
            this._slowingEffectList.splice(e, 1);
        });
    }

    AddASlowingEffect(value: number, durationTime: number) {
        if(value > 0) { console.warn(`减速数值为正数！${value}`); }
        this._slowingEffectList.push({value: value, timeLeft: durationTime});
    }

    SetOnDirChengeCallback(callback: Function) {
        this._onDirChengeCallback = callback;
    }

    SetOnHpToZeroCallback(callback: Function) {
        this._onHpToZeroCallback = callback;
    }

    HpSub(value: number) {
        this.nowHp -= value;
        if(this.nowHp <= 0) {
            this.nowHp = 0;
            this._onHpToZeroCallback();
        }
    }
}

export type BaseAttribute = {
    baseMoveSpeed: number;
    baseHp: number;
}