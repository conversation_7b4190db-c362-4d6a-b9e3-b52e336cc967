// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import Util from "../Base/Util";
import GameManager from "../GameManager";
import GameUtils, { UnitInfo } from "../Game/GameUtils";
import LocalUtils, { TweenObject } from "../LocalUtils";
import Attribute from "../UnitComponent/Attribute";
import Backpack from "../UnitComponent/Backpack";
import SkillReleaser from "../UnitComponent/SkillReleaser";
import UnitStateMachine from "../UnitComponent/UnitStateMachine";
import { WeaponInfo } from "../UnitComponent/Weapon";
import Bullet from "./Bullet";
import Enemy from "./Enemy";
import Hero from "./Hero";
import Stuff from "./Stuff";
import GameStuffManager, { DigBlockInfo } from "../Game/GameStuffManager";
import { ResourceInfo } from "./Resource";
import SinglePrefabCreator from "../Utils/SinglePrefabCreator";
import CustomSpriteData from "../Utils/CustomSpriteData";
import MyPhysicsExtension from "../Utils/MyPhysics/MyPhysicsExtension";
import UnitBehavior from "../UnitComponent/UnitBehavior";

const {ccclass, property} = cc._decorator;

export enum UnitGatherType {
    default,
    cut,
    mine,
    dig,
}

@ccclass
export default class Unit extends Stuff implements CanGetHurtBehavior, MoveOnTrackBehavior {

    // 装载武器
    // 装载技能发射器
    // 装载角色属性

    private static _curId = 0;


    @property(SinglePrefabCreator)
    singlePrefabCreator: SinglePrefabCreator = null;

    get unit_id() : number{
        return this._unit_id;
    }

    get isAlive() {
        return (!(this.isReadyToDead || this.isDead || this.isUnborn)) && this && this.isValid;
    }

    get isFaceLeft() {
        return this._isFaceLeft;
    }

    info: UnitInfo = null;

    unitNode: cc.Node = null;

    body: cc.Node = null;
    backpackNode: cc.Node = null;
    spine: sp.Skeleton = null;
    FANode: cc.Node = null;
    collider: cc.PhysicsBoxCollider = null;
    rbNode: cc.Node = null;
    shadowNode: cc.Node = null;
    guideCircleNode: cc.Node = null;
    cover: cc.Node = null;
    spDieNode: cc.Node = null;
    spDie: sp.Skeleton = null;

    skillLightParent: cc.Node = null;
    skillLight: cc.Node = null;

    attribute: Attribute = null;
    unitStateMachine: UnitStateMachine = null;
    skillReleaser: SkillReleaser = null;
    backpack: Backpack = null;
    unitBehavior: UnitBehavior = null;
    
    weaponList: WeaponInfo[] = [];
    mainWeaponIndex: number = 0;

    focusUnit: Unit = null;
    // focusCanGetHurtBehavior: CanGetHurtBehavior = null;
    focusStuff: Stuff = null;
    focusMoveTarget: Stuff = null;
    focusAttackTarget: Stuff = null;
    focusAttackTargetDigBlock: DigBlockInfo = null;

    minDistance = 100;
    
    isUpdatePhysicsWorld = false;

    isCanNotGetHurt = false;
    isCanMove = true;
    isAllowFocusUnit = true;
    isAllowAttack = false;

    // 状态
    isAttacking = false;
    isMoving = false;
    isSkilling = false;
    isGathering = false;
    unitGatherType = UnitGatherType.default;

    isWaitingFood = false;
    isGotFood = false;
    isEating = false;
    isSleeping = false;

    get isCarrying(): boolean {
        return this._isCarrying;
    }
    set isCarrying(value: boolean) {
        if(this._isCarrying != value) {
            this._isCarrying = value;
            this.unitStateMachine.ReplayNowState();
        }
    }
    private _isCarrying: boolean = false;

    isAllowPatrol = false;
    isOnPatrol = false;

    isAutoMoving = false;
    autoMoveAllTime = 0;
    autoMoveLeftTime = 0;
    autoMoveDir: cc.Vec2;
    autoMoveSpeed: number;
    autoMoveHeight: number;

    noMovingTime: number = 0;

    /** 走线路径 */
    trackRef: number = -1;
    isMoveOnTrack = false;
    isMoveToPathPoint = false;
    pathPointRef = 0;
    pathPointEndRef = 8;
    movingPathPointPos: cc.Vec2;
    onArriveEndCallback: Function;
    
    trackPosList: cc.Vec2[] = [];
    queueGap = 100;

    spDieMark: number = 0;

    patrolPos: cc.Vec2 = null;
    setPatrolPosTime: number = 0;

    isReadyToDead = false;
    isDead = false;
    isUnborn = true;

    isCurFrameRbNodeActive = false;
    isCurFrameRbNodeActive2 = false;
    // isIgnoreCollision = false;
    isIgnoreCollision = true;

    lastFramePos: cc.Vec2 = null;
    curFrametargetPos: cc.Vec2 = null;

    isTurnFaceWaitTime = false;
    isDrop = true;
    
    /** 搬运方块中 */
    isCarryingBlock = false;
    carringBlocks: ResourceInfo[] = [];
    // carringBlockNumLimit: number = 1;
    carringBlock: ResourceInfo = null;
    nextPickBlockTime: number = 0;

    private _unit_id: number = -1;

    private _isFaceLeft = false;
    private _isMoveUpdateAdded = false;
    private _moveUpdateCallbackId = -1;
    private _gameUpdateCallbackId = -1;
    private _isLateStop = false;
    private _lateStopTime = 0;
    private _isLateStoped = false;

    private _isCheckPhysicsBoxNecessary = true;
    private _checkPhysicsBoxNecessaryTime = 0;

    private _turnFaceTimeInterval = 0.3;
    private _turnFaceTime = 0;
    private _isTurnFaced = false;

    private _colorTweenObject: TweenObject<number>;

    
    // 排队
    protected _queueGap = 100;
    protected _queueStartPathPointIndex = -1;
    protected _queueHeadPathPointIndex = -1;
    protected _queueMiddlePathPointRefs: number[] = [0, 1];
    protected _queueLineIndex = -1;
    protected _queueLocationIndex = -1;
    protected _queueCurMiddleIndex = -1;
    protected _queueTargetMiddleIndex = -1;
    // protected _isQueueToMiddlePoint = false;
    protected _isQueueArrived = false;
    private _onArriveQueueHead = null;

    // LIFE-CYCLE CALLBACKS:

    protected override InitOnLoad() {
        // console.log(`Unit InitOnLoad ${this.node.name}`);
        this._unit_id = Unit.GetUnitId();

        this.LoadNodes();
        super.InitOnLoad();

        this.LoadAttribute();
        if(this.spine || this.FANode) {
            this.LoadUnitStateMachine();
        }
        this.LoadUnitBehavior();

        this.ReInit();
        GameManager.instance.LateFrameCall(()=>{
            this.LoadColorMixerComp(this.unitNode);
        });

        this._gameUpdateCallbackId = GameManager.instance.AddGameUpdate('Unit', (dt: number) =>{
            this.unitGameUpdate(dt);
        }, false, 2060);
    }

    ReInit() {
        this.isCanNotGetHurt = false;
        this.isCanMove = true;
        this.isAllowFocusUnit = true;
        this.isAllowAttack = false;

        this.isAttacking = false;
        this.isMoving = false;
        this.isSkilling = false;
        this.isGathering = false;
        this.isAllowPatrol = false;
        this.isOnPatrol = false;
        this.isReadyToDead = false;
        this.isDead = false;
        
        this.isWaitingFood = false;
        this.isGotFood = false;
        this.isEating = false;
        this.isSleeping = false;
        this.isCarrying = false;

        this.isAutoMoving = false;
        this.autoMoveAllTime = 0;
        this.autoMoveLeftTime = 0;

        this.noMovingTime = 0;

        this.spDieMark = 0;

        this.isDrop = true;

        this._isLateStop = false;
        this._lateStopTime = 0;
        this._isLateStoped = false;

        this._isCheckPhysicsBoxNecessary = true;
        // this.LoadColorMixerComp();
        // this.SetCharacterColor(cc.Color.WHITE);

        // if(this.tag == 'npc') {
        //     this.isIgnoreCollision = false;
        // }
    }

    LoadNodes() {
        let body = this.unitNode.getChildByName('view').getChildByName('body');
        this.body = body;

        let skillLightParent = body.getChildByName('skill_light_parent');
        if(skillLightParent) {
            this.skillLightParent = skillLightParent;
            let skillLight = skillLightParent.getChildByName('skill_light');
            if(skillLight) {
                this.skillLight = skillLight;
            }
        }

        this.backpackNode = this.unitNode.getChildByName('backpack');
        let spineNode = body.getChildByName('spine');
        if(spineNode) {
            this.spine = spineNode.getComponent(sp.Skeleton);
        } else {
            // console.error('spine 不存在！');
        }
        this.FANode = body.getChildByName('FA');

        this.rbNode = this.unitNode.getChildByName('rb');
        this.collider = this.rbNode.getComponents(cc.PhysicsBoxCollider).find((e)=>{ return e.tag == 0});
        this.shadowNode = this.unitNode.getChildByName('view').getChildByName('shadow');
        this.guideCircleNode = this.unitNode.getChildByName('view').getChildByName('guideCircle');

        this.cover = this.unitNode.getChildByName('view').getChildByName('cover');
        if(this.cover) {
            this.spDieNode = this.cover.getChildByName('sp_die');
            if(this.spDieNode) {
                this.spDie = this.spDieNode.getChildByName('spine').getComponent(sp.Skeleton);
            }
        }
    }

    ResetUnitNode(newUnitNode: cc.Node) {
        this.unitNode = newUnitNode;
        this.LoadNodes();
        if(this.spine || this.FANode) {
            this.LoadUnitStateMachine();
        }
        GameManager.instance.LateFrameCall(()=>{
            this.LoadColorMixerComp(this.unitNode);
        });
    }

    Reset(pos: cc.Vec2) {
        this._unit_id = Unit.GetUnitId();

        this.unitNode.opacity = 0;
        this.ReInit();
        this.isDead = true;
        this.unitStateMachine.Reset();
        GameManager.instance.LateFrameCall(()=>{
            this.unitNode.opacity = 255;
            this.rootPosition = pos;
            this.isDead = false;
        });
        cc.Tween.stopAllByTarget(this.shadowNode);
        this.shadowNode.position = cc.v3();
        this.shadowNode.opacity = 255;
        this.spDieNode && (this.spDieNode.active = false);
    }

    // SetCharacterColor(color: cc.Color) {
    //     if(this.spine) {
    //         this.spine.node.color = color;
    //         let colorMixerComp = this.spine.node.getComponent(ColorMixer);
    //         if(colorMixerComp) {
    //             colorMixerComp.color = color;
    //         }
    //     }
    //     if(this.FANode) {
    //         this.FANode.children.forEach((e)=>{
    //             e.color = color;
    //             let colorMixerComp = e.getComponent(ColorMixer);
    //             if(colorMixerComp) {
    //                 colorMixerComp.color = color;
    //             }
    //         });
    //     }
    // }

    PlayCharacterColorFlash(color: cc.Color, colorMixRatio = 0.7, colorMixTime = 0.1) {
        if(this.spine) {
            let colorMixerComp = this.spine.node.getComponent(CustomSpriteData);
            if(!colorMixerComp) {
                colorMixerComp = this.spine.node.addComponent(CustomSpriteData);
                colorMixerComp.Init(GameUtils.rootGameWorld.colorMixerMaterial, color, 0.5);
                colorMixerComp.LoadNode();
                console.warn('no colorMixerComp!');
            }
            colorMixerComp.PlayFlashColor(color, colorMixRatio, colorMixTime);
        }
        if(this.FANode) {
            this.FANode.children.forEach((e, index)=>{
                let colorMixerComp = e.getComponent(CustomSpriteData);
                if(!colorMixerComp) {
                    colorMixerComp = e.addComponent(CustomSpriteData);
                    colorMixerComp.Init(GameUtils.rootGameWorld.customSpriteMaterial, color, 0.5);
                    colorMixerComp.LoadNode();
                    console.warn('no colorMixerComp!');
                }
                colorMixerComp.PlayFlashColor(color, colorMixRatio, colorMixTime);
            });
        }
    }

    // RemoveColorMixerComp() {
    //     if(this.spine) {
    //         let colorMixerComp = this.spine.node.getComponent(ColorMixer);
    //         if(colorMixerComp) {
    //             this.spine.node.removeComponent(colorMixerComp);
    //         }
    //     }
    //     if(this.FANode) {
    //         this.FANode.children.forEach((e, index)=>{
    //             let colorMixerComp = e.getComponent(ColorMixer);
    //             if(colorMixerComp) {
    //                 e.removeComponent(colorMixerComp);
    //             }
    //         });
    //     }
    // }

    // update (dt) {}

    // updateCallback(dt: number) {
    // }

    // lateUpdateCallback(dt: number) {
    // }

    unitGameUpdate(dt: number) {
        if(this.isTurnFaceWaitTime) {
            this._turnFaceTime += dt;
            if(this._turnFaceTime > this._turnFaceTimeInterval) {
                this._isTurnFaced = false;
            }
        }
        if(!this._isLateStoped && this._lateStopTime > 0.04) {
            this.isMoving = false;
            this._isLateStoped = true;
            this.unitStateMachine.TryStopRun();
            // GameManager.instance.RemoveUpdateCallbackByID(this._moveUpdateCallbackId);
        }
        if(this._isLateStop) {
            this._lateStopTime += dt;
        }
        if(this.isMoving || this.isAutoMoving) {
            this.noMovingTime = 0;
        } else {
            this.noMovingTime += dt;
        }
        // console.log(`this._lateStopTime: ${this._lateStopTime}`);

        let physicsManager = cc.director.getPhysicsManager();
        let rbNode = this.rbNode;
        let rb: cc.RigidBody = rbNode.getComponent(cc.RigidBody);

        if(this.tag == 'hero' && (this.selfScript as Hero).isMainHero) {
        // if(this.tag == 'hero') {
            rbNode.active = true;
        } else if(this.tag == 'driftingMeat') {
            rbNode.active = true;
        } else if(this.isAlive) {
            if(this.tag == 'hero') {
                this._isCheckPhysicsBoxNecessary = false;
                return;
            }
            if(!rb) return;
            rb.type = cc.RigidBodyType.Static;
            // this.isCurFrameRbNodeActive = true;
            let box = rb.node.getComponents(cc.PhysicsBoxCollider)[1];
            if(!box) box = rb.node.getComponents(cc.PhysicsBoxCollider)[0];
            if(box && !this.isIgnoreCollision) {
                let rectXY = rbNode.convertToWorldSpaceAR(cc.v2(box.offset.x - box.size.width / 2, box.offset.y - box.size.height / 2));
                let rectWH = rbNode.convertToWorldSpaceAR(cc.v2(box.size.width, box.size.height)).sub(rbNode.convertToWorldSpaceAR(cc.v2()));
                let rect = cc.rect(rectXY.x, rectXY.y, rectWH.x, rectWH.y);
                let result = physicsManager.testAABB(rect);
                let isAllowActive = false;
                result.forEach((e)=>{
                    if(!isAllowActive) {
                        if(e.node.group == 'GameWorldBullet') {
                            let bullet = e.node.getComponent(Bullet);
                            if(!bullet) {
                                bullet = e.node.parent.getComponent(Bullet);
                            }
                            if(bullet && bullet.weaponProps.damageTarget == 0 && this.tag == 'hero') {
                                isAllowActive = true;
                            }
                            if(bullet && bullet.weaponProps.damageTarget == 1 && this.tag == 'enemy') {
                                isAllowActive = true;
                            }
                            // if(bullet) {
                            //     console.log(`get Bullet!!  bullet: ${bullet},  weaponProps.damageTarget: ${bullet.weaponProps.damageTarget}`);
                            // }
                        }
                        // isAllowUpdate = true;
                    }
                });
                if(isAllowActive) {
                    this.isCurFrameRbNodeActive = true;
                    // rbNode.active = true;
                } else {
                    // this.isCurFrameRbNodeActive = false;
                    // rbNode.active = false;
                }
            }
            this._checkPhysicsBoxNecessaryTime += dt;
            if(this._checkPhysicsBoxNecessaryTime > 1) {
                this._checkPhysicsBoxNecessaryTime = 0;
                if(!this.isIgnoreCollision) {
                let rectXY = rbNode.convertToWorldSpaceAR(cc.v2(box.offset.x - (box.size.width + 160) / 2, box.offset.y - (box.size.height + 120) / 2));
                let rectWH = rbNode.convertToWorldSpaceAR(cc.v2(box.size.width, box.size.height)).sub(rbNode.convertToWorldSpaceAR(cc.v2())).add(cc.v2(160, 120));
                let rect = cc.rect(rectXY.x, rectXY.y, rectWH.x, rectWH.y);
                let result: cc.PhysicsCollider[] = MyPhysicsExtension.Instance.MyTestAABB(rect);
                let isAllowUpdate = false;
                result.forEach((e)=>{
                    if(!isAllowUpdate) {
                        if(e.node.group == 'GameWorldStuff') {
                            isAllowUpdate = true;
                        }
                    }
                });
                if(isAllowUpdate) {
                    this._isCheckPhysicsBoxNecessary = true;
                } else if(rbNode.active) {
                    this._isCheckPhysicsBoxNecessary = false;
                }
                }
            }
        }
    }

    
    /** 此函数主要执行自动的 AI 行为
     * - 包括索敌、攻击和移动行为 
     * - 非自动的复杂行为应该借助 unitBehavior 来实现
     * - 如果是独立单位，不通过 ref 等标识符区分行为模式，可在此函数内实现 AI 行为
    */
    // AI(dt: number) {}

    MoveToTargetPos(dt: number, pos: cc.Vec2, isKeepDistance: boolean = false) {
        let moveDir = pos.sub(this.rootPosition).normalize();
        let distance = GameUtils.Fake3dDistanceExpand(pos, this.rootPosition);
        if(moveDir == cc.v2(0, 0)) {
            moveDir = this.attribute.moveDir;
        }
        this.attribute.moveDir = moveDir;

        let moveDistance = this.attribute.finalMoveSpeed * dt;
        
        if(isKeepDistance) {
            let minDistance = 0;
            minDistance = this.minDistance + (this.focusStuff ? this.focusStuff.hitboxRadius : 0);
            if(distance - moveDistance < minDistance) {
                moveDistance = distance - minDistance;
                if(moveDistance < 0) {
                    moveDistance = 0;
                }
            }
        } else {
            if(distance < moveDistance) {
                moveDistance = distance;
            }
        }
        let newPos = this.rootPosition.add(this.attribute.finalMoveDir.mul(moveDistance));
        if(!isKeepDistance && distance < moveDistance) {
            newPos = pos;
            // console.log(`到达：dir: ${moveDir.toString()}`);
        }
        if(this.isAlive && moveDistance > 0.002) {
            this.Move(newPos);
        } else {
            // console.warn('moveDistance <= 0.002!');
        }
        // console.log(`target pos: ${pos}`);
        // console.log(`moveDir: ${moveDir.normalize()}this.rootPosition: ${this.rootPosition}`);
    }

    TryDigBlock(pos: cc.Vec2, isPosChanging = false) {
        let digBlock = GameStuffManager.instance.FindADigBlock(this.unit_id, pos);
        if(digBlock) {
            // console.log('开始挖掘！！');
            if(digBlock.TryLock(this.unit_id)) {
                if(digBlock.lockedUnitId != this.unit_id) {
                    console.error('id 不一致！');
                }
                this.unitGatherType = UnitGatherType.dig;
                if(this.attribute) {
                    // this.attribute.AddASlowingEffect(-80, 0.5); // 加减速效果
                    this.attribute.gatherDir = cc.v2(this.isFaceLeft ? -1 : 1, 0);
                }
                this.OnGather();
                GameManager.instance.LateTimeCallOnce(()=>{
                    if(isPosChanging) {
                        let newPos = this.rootPosition.add(cc.v2(this.isFaceLeft ? -70 : 70, -30));
                        let newDigBlock = GameStuffManager.instance.FindADigBlock(this.unit_id, newPos);
                        if(newDigBlock && newDigBlock.TryLock(this.unit_id)) {
                            if(digBlock.index != newDigBlock.index) {
                                digBlock.RemoveLock();
                            }
                            newDigBlock.TryDig(this.unit_id);
                        } else {
                            digBlock.TryDig(this.unit_id);
                        }
                    } else {
                        digBlock.TryDig(this.unit_id);
                    }
                }, 0.3);
                return true;
            }
        }
        return false;
    }
        

    Patrol(dt: number) {
        if(this.patrolPos) {
            this.MoveToTargetPos(dt, this.patrolPos);
            let distance = GameUtils.Fake3dDistanceExpand(this.patrolPos, this.rootPosition);
            if(distance < 2) {
                this.patrolPos = null;
            }
        }
    }

    SetNextPatrolPos(pos?: cc.Vec2) {
        if(!pos) {
            let randomDir = LocalUtils.AngleToVec2(Math.random() * 360);
            pos = this.rootPosition.add(randomDir.mul(Math.random() * 100 + 50).scale(cc.v2(1, 0.8)));
            let loopTimes = 0;
            while(loopTimes < 20 && GameUtils.CheckPosIsOnCollision(pos)) {
                randomDir = LocalUtils.AngleToVec2(Math.random() * 360);
                pos = this.rootPosition.add(randomDir.mul(Math.random() * 100 + 50 + loopTimes * 2).scale(cc.v2(1, 0.8)));
                loopTimes += 1;
            }
            if(loopTimes >= 20) {
                // console.error(`${this.node.name} patrol pos is on collision!`);
            }
        }
        if(!GameUtils.CheckPosIsOnCollision(pos)) {
            this.patrolPos = pos;
        }
    }

    OnBorn() {
        this.unitNode.opacity = 0;
        GameManager.instance.LateFrameCall(()=>{
            this.isUnborn = false;
            this.unitNode.opacity = 255;
        });
    }

    OnAttack() {
        this.isAttacking = true;
        this.unitStateMachine.Attack({onStateEndCallback: ()=>{
            if(this.isMoving) {
                this.TurnFaceDir(this.attribute.finalMoveDir.x < 0);
            }
            this.isAttacking = false;
        }}, ()=>{
            this.isAttacking = false;
        });
        if(this.isMoving) {
            this.TurnFaceDir(this.attribute.finalMoveDir.x < 0);
        } else {
            this.TurnFaceDir(this.attribute.attackDir.x < 0);
        }
    }

    OnGather() {
        this.isGathering = true;
        // console.log('伐木！');
        this.unitStateMachine.Gather({onStateEndCallback: ()=>{
            this.OnGatherComplete();
        }}, ()=>{
            this.OnGatherComplete();
        });
        this.TurnFaceDir(this.attribute.gatherDir.x < 0);
    }

    OnGatherComplete() {
        // console.log('伐木结束！');
        if(this.isMoving) {
            this.TurnFaceDir(this.attribute.finalMoveDir.x < 0);
        }
        this.isGathering = false;
    }

    OnShoot() {
        if(this.isAlive && !this.isSkilling) {
            this.TurnFaceDir(this.attribute.attackDir.x < 0);
        }
    }

    // private _f() {
    //     this.isMoving = false;
    //     this.unitStateMachine.TryStopRun();
    // }

    Move(pos: cc.Vec2) {
        if(!this.isAlive) {
            return;
        }
        this.isMoving = true;
        this.rootPosition = pos;
        this.OnMove();
    }

    OnMove() {
        this.unitStateMachine.Run();

        this._isLateStop = true;
        this._lateStopTime = 0;
        this._isLateStoped = false;

        if(!this._isMoveUpdateAdded) {
            this._isMoveUpdateAdded = true;
            // GameManager.instance.AddGameUpdate('Unit', this.unitGameUpdate);
        } else {
            // GameManager.instance.RemoveUpdateCallbackByID(this._moveUpdateCallbackId);
        }
    }

    GetHurt(sourceUnit: Unit, damage: number, bulletRef = 0, direction = cc.v2(1, 0), hitRepelDistance = 0, hitRepelHeight = 0) {
        // console.log(`${this.node.name} 受伤！-${damage}, 来自 ${sourceUnit.node.name}`);
        if(this.isAlive && !this.isCanNotGetHurt) {
            let randmoResult = Util.RandomRange(0, 1);
            if(randmoResult < 0.5) {
                LocalUtils.PlaySound('hit');
            } else {
                LocalUtils.PlaySound('hit2');
            }
            if(this.tag == 'enemy' && this.attribute) {
                // this.attribute.moveSpeedUp = -80;
                this.attribute.AddASlowingEffect(-80, 0.1);
            }
            this.OnGetHurt();
            if(hitRepelDistance > 0 || hitRepelHeight > 0) {
                let hitRepelDistanceAdd = Math.random() * 0.5;
                let hitRepelHeightAdd = Math.random() * 0.5;
                this.BeHitRepel(sourceUnit, direction, hitRepelDistance * (1 + hitRepelDistanceAdd), hitRepelHeight * (1 + hitRepelHeightAdd));
            }
            this.spDieMark = GameUtils.GetSpDieMarkByBulletRef(bulletRef);
            this.attribute.HpSub(damage);
            // this.SetCharacterColor(cc.color(255, 90, 90));
            // if(this._colorTweenObject) {
            //     cc.Tween.stopAllByTarget(this._colorTweenObject);
            // } else {
            //     this._colorTweenObject = new TweenObject<number>(90, (value: number)=>{
            //         this.SetCharacterColor(cc.color(255, value, value));
            //     });
            // }
            // cc.tween(this._colorTweenObject).set({value: 90}).delay(0.1).call(()=>{
            //     if(this && this.isValid && this.tag == 'enemy' && this.attribute) {
            //         this.attribute.moveSpeedUp = 0;
            //     }
            // }).to(0.3, {value: 255}).call(()=>{
            // }).start();
            this.PlayCharacterColorFlash(cc.color(225, 0, 0));
            GameUtils.rootGameWorldUI.PlayEffHit(this.centerPosition, bulletRef, direction);
        }
    }

    OnGetHurt() {
        
    }

    BeHitRepel(sourceUnit: Unit, dir: cc.Vec2, hitRepelDistance: number, hitRepelHeight: number) {
        this.isAutoMoving = true;
        this.autoMoveDir = dir;
        if(hitRepelHeight > 0) {
            this.autoMoveAllTime = 0.4;
            this.autoMoveLeftTime = 0.4;
        } else {
            this.autoMoveAllTime = 0.15;
            this.autoMoveLeftTime = 0.15;
        }
        this.autoMoveSpeed = hitRepelDistance / this.autoMoveAllTime;
        this.autoMoveSpeed = GameUtils.Fake3dSpeed(this.autoMoveSpeed, dir);
        this.autoMoveHeight = hitRepelHeight;

    }

    KillSelf(isDrop = false) {
        // this.FANode.scale = 10;
        this.attribute.nowHp = 0;
        this.isDrop = isDrop;
        this.ReadyToDead();
    }

    ReadyToDead() {
        this.isReadyToDead = true;
        this.unitStateMachine.Die({onStateEndCallback: ()=>{
            this.isDead = true;
            if(this.tag == 'enemy' && ((this.selfScript as Enemy).enemyRef == 1 || (this.selfScript as Enemy).enemyRef == 3)) {
            } else if(this.tag == 'hero' && (this.selfScript as Hero).isMainHero) {
            } else {
                cc.tween(this.unitNode).to(0.2, {opacity: 0}).call(()=>{
                }).start();
            }
            GameManager.instance.LateTimeCallOnce(()=>{
                this.OnDead();
            }, 0.22);
        }});
        GameManager.instance.scheduleOnce(()=>{
            if(this.tag == 'enemy' && ((this.selfScript as Enemy).enemyRef == 1 || (this.selfScript as Enemy).enemyRef == 3)) {
                cc.tween(this.unitNode).delay(1).to(0.2, {opacity: 0}).call(()=>{
                }).start();
            }
            if(this.isDrop) {
                if(this.spDieMark != 0) {
                    let delay = (this.spDieMark == 1 || this.spDieMark == 2) ? 0.5 : 0;
                    GameUtils.rootGameWorldUI.PlayEffDie(this.centerPosition, delay);
                } else {
                    GameUtils.rootGameWorldUI.PlayEffDie(this.centerPosition);
                }
            }
        }, 0.1);
    }

    OnDead() {
        this.UnEquipWeapon();
        this.DestroyUnitStateMachine();
        // this.RemoveColorMixerComp();
        this.node.destroy();
    }

    UnEquipWeapon() {
        this.weaponList = [];
    }

    LoadAttribute() {
        this.attribute = new Attribute(this);
        this.attribute.Init();
        this.attribute.SetOnDirChengeCallback((isLeft: boolean)=>{
            if(this.isAlive && !this.isAttacking && !this.isSkilling && !this.isGathering && this.isMoving) {
                if(this.isFaceLeft != isLeft) {
                    if(this.isTurnFaceWaitTime) {
                        if(!this._isTurnFaced) {
                            this._isTurnFaced = true;
                            this._turnFaceTime = 0;
                            this.TurnFaceDir(isLeft);
                        }
                    } else {
                        this.TurnFaceDir(isLeft);
                    }
                }
            } else {
                // 主角攻击时可以转头
                // if(this.tag == 'hero' && (this.selfScript as Hero).isMainHero) {
                //     // console.log(`Turn Face Fail!  isAlive: ${this.isAlive}, isAttacking: ${this.isAttacking}, isMoving: ${this.isMoving}
                //     //     , finalMoveDir: ${this.attribute.finalMoveDir}, moveDirAngleOffset: ${this.attribute.moveDirAngleOffset}`);
                //     if(this.isAttacking) {
                //         this.TurnFaceDir(isLeft);
                //     }
                // }
            }
        });
        this.attribute.SetOnHpToZeroCallback(()=>{
            this.ReadyToDead();
        });
        if(this.tag == 'enemy' || this.tag == 'npc') {
            this.isTurnFaceWaitTime = true;
        }
    }

    LoadUnitStateMachine() {
        this.unitStateMachine = new UnitStateMachine(this);
        this.unitStateMachine.InitSpine(this.spine);
        this.unitStateMachine.Stop();
    }

    LoadBackpack() {
        if(this.backpackNode) {
            this.backpack = new Backpack(this);
            this.backpack.Init();
        } else {
            console.warn(`backpackNode 不存在！ ${this.node.name}`);
        }
    }
    
    LoadUnitBehavior() {
    }


    DestroyUnitStateMachine() {
        this.unitStateMachine.DestroySelf();
    }

    TurnFaceDir(isLeft: boolean) {
        if((this.spine || this.FANode) && this.isAlive) {
            if(isLeft) {
                this._isFaceLeft = true;
                this.spine && (this.spine.node.scaleX = -Math.abs(this.spine.node.scaleX));
                this.FANode && (this.FANode.scaleX = -Math.abs(this.FANode.scaleX));
                this.skillLightParent && (this.skillLightParent.scaleX = -Math.abs(this.skillLightParent.scaleX));
                // this.backpackNode && (this.backpackNode.scaleX = -Math.abs(this.backpackNode.scaleX));
                this.spDieNode && (this.spDieNode.scaleX = -Math.abs(this.spDieNode.scaleX));
            } else {
                this._isFaceLeft = false;
                this.spine && (this.spine.node.scaleX = Math.abs(this.spine.node.scaleX));
                this.FANode && (this.FANode.scaleX = Math.abs(this.FANode.scaleX));
                this.skillLightParent && (this.skillLightParent.scaleX = Math.abs(this.skillLightParent.scaleX));
                // this.backpackNode && (this.backpackNode.scaleX = Math.abs(this.backpackNode.scaleX));
                this.spDieNode && (this.spDieNode.scaleX = Math.abs(this.spDieNode.scaleX));
            }
        } 
    }

    override SetHeight(height: number) {
        this.height = height;
        this.body.y = height;
        if(this.shadowNode) {
            this.shadowNode.y = height;
        }
        if(this.guideCircleNode) {
            this.guideCircleNode.y = height;
        }
    }

    protected override SetPos(pos: cc.Vec2): cc.Vec2 {

        // let node = this.unitNode;
        // let physicsManager = cc.director.getPhysicsManager();
        let rbNode = this.rbNode;
        let rb: cc.RigidBody = rbNode.getComponent(cc.RigidBody);
        let srcPos = cc.v2(rbNode.x, rbNode.y);
        if(!this.lastFramePos) {
            this.lastFramePos = srcPos.add(cc.v2());
        }
        // this.lastFramePos = srcPos.add(cc.v2());
        this.curFrametargetPos = pos.add(cc.v2());
        rbNode.x = pos.x; 
        rbNode.y = pos.y;

        if(this.isIgnoreCollision) {
            return pos;
        }
        // /*
        if(this.tag == 'hero' && (this.selfScript as Hero).isMainHero) {
        // if(this.tag == 'hero') {
            rbNode.active = true;
            cc.director.getPhysicsManager().update();
        } else if(this._isCheckPhysicsBoxNecessary) {
            if(!rb) return pos;
            let box = rb.node.getComponents(cc.PhysicsBoxCollider)[0];
            if(box) {
                let rectXY = rbNode.convertToWorldSpaceAR(cc.v2(box.offset.x - box.size.width / 2, box.offset.y - box.size.height / 2));
                let rectWH = rbNode.convertToWorldSpaceAR(cc.v2(box.size.width, box.size.height)).sub(rbNode.convertToWorldSpaceAR(cc.v2()));
                let rect = cc.rect(rectXY.x, rectXY.y, rectWH.x, rectWH.y);
                // console.log(rect);
                // let result: cc.PhysicsCollider[] = [];
                let result: cc.PhysicsCollider[] = MyPhysicsExtension.Instance.MyTestAABB(rect);
                // let result = GameUtils.RayCastGroup([cc.v2(rect.xMin, rect.yMin), cc.v2(rect.xMin, rect.yMax), cc.v2(rect.xMax, rect.yMax), cc.v2(rect.xMax, rect.yMin)]);
                
                let isAllowUpdate = false;
                result.forEach((e)=>{
                    if(!isAllowUpdate) {
                        if(e.node.group == 'GameWorldStuff') {
                            isAllowUpdate = true;
                        }
                        // isAllowUpdate = true;
                    }
                });
                if(isAllowUpdate) {
                    this.isCurFrameRbNodeActive2 = true;
                    // rbNode.active = true;
                    // cc.director.getPhysicsManager().update();
                    // rbNode.active = false;
                } else if(rbNode.active) {
                    // rbNode.active = false;
                }
            } else {
                // console.log('box not exist !');
            }
        }
        // */
        // let finalPos = cc.v2(rbNode.x, rbNode.y);
        
        // GameUtils.rootGameWorld.TryReSortSelf(this);
        // GameUtils.rootGameWorld.ReSortStuffs();

        // this.TryChengeMoveDir(srcPos, pos, finalPos);
        // return finalPos;
        return pos;
    }

    SetFinalPos(dt: number) {
        let finalPos = cc.v2(this.rbNode.x, this.rbNode.y);
        if(this.tag != 'hero') {
            this.TryChengeMoveDir(dt, this.lastFramePos, this.curFrametargetPos, finalPos);
        }
        this.SetRootPosition(finalPos);
        this.lastFramePos = finalPos;
    }

    TP(pos: cc.Vec2) {
        this.rbNode.x = pos.x; 
        this.rbNode.y = pos.y;
        this._rootPosition = cc.v2(pos.x, pos.y);
    }

    TryChengeMoveDir(dt: number, srcPos: cc.Vec2, pos: cc.Vec2, finalPos: cc.Vec2) {
        let rbNode = this.rbNode;
        let viewNode = this.unitNode.getChildByName('view');
        if(this.attribute) {
            if(this.isAlive && pos.sub(srcPos).len() > 0.01 && finalPos.sub(srcPos).len() / pos.sub(srcPos).len() < 0.6) {
                // this.FANode && (this.FANode.scale = 1)
                if(Math.abs(this.attribute.moveDirAngleOffset) < 1) {
                    let startLoc = viewNode.convertToWorldSpaceAR(this.collider.offset);
                    let endLoc = viewNode.convertToWorldSpaceAR(this.collider.offset.add(this.attribute.moveDir.normalize().mul(200)));
                    let result: cc.PhysicsRayCastResult[] = [];
                    result = cc.director.getPhysicsManager().rayCast(startLoc, endLoc, cc.RayCastType.Closest);
    
                    if(result.length > 0) {
                        let normal = result[0].normal;
                        let dirOffset = LocalUtils.Vec2ToAngle(normal, this.attribute.moveDir);
                        // console.log(`发生阻挡！ (${this.node.name}) dirOffset: ${dirOffset}`);
    
                        if(Math.abs(dirOffset) >= 90) {
                            if(dirOffset < 0) {
                        // let random = Math.random();
                        //     if(random > 0.5) {
                                this.attribute.moveDirAngleOffset = -110;
                            } else {
                                this.attribute.moveDirAngleOffset = 110;
                            }
                        }
                    } else {
                        // console.log(`移动受阻，但未检测到物体！ (${this.node.name}) startLoc: ${startLoc}`);
                    }
                } else {
                    // console.log(`移动受阻，移动方向偏移！ (${this.node.name}) moveDirAngleOffset: ${this.attribute.moveDirAngleOffset}`);
                }
            // } else {
                // let AngleOffsetAbs = Math.abs(this.attribute.moveDirAngleOffset);
                if(this.attribute.moveDirAngleOffset > 0) {
                    this.attribute.moveDirAngleOffset += 40 * 60 * dt;
                    if(this.attribute.moveDirAngleOffset > 180){
                        this.attribute.moveDirAngleOffset = 180;
                    }
                } else {
                // } else if(this.attribute.moveDirAngleOffset < 0) {
                    this.attribute.moveDirAngleOffset -= 40 * 60 * dt;
                    if(this.attribute.moveDirAngleOffset < -180){
                        this.attribute.moveDirAngleOffset = -180;
                    }
                // } else {
                }
            // }
            } else {
                // this.FANode && (this.FANode.scale = 3)
                // this.attribute.moveDirAngleOffset = 0;
                if(this.attribute.moveDirAngleOffset > 0) {
                    this.attribute.moveDirAngleOffset -= 2 * 60 * dt;
                    if(this.attribute.moveDirAngleOffset < 1){
                        this.attribute.moveDirAngleOffset = 1;
                    }
                } else if(this.attribute.moveDirAngleOffset < 0) {
                    this.attribute.moveDirAngleOffset += 2 * 60 * dt;
                    if(this.attribute.moveDirAngleOffset > -1){
                        this.attribute.moveDirAngleOffset = -1;
                    }
                }
            }
        }
    }

    /** 设置走线路径 */
    MoveToTrack(trackRef: number, isReverse: boolean = false, beforeArriveFirstPathPoint?: Function): boolean {
        if(this.RefreshTrack(trackRef, isReverse)) {
            this.pathPointRef = 0;
            this.isMoveOnTrack = true;
            this.isMoveToPathPoint = true;
            beforeArriveFirstPathPoint && beforeArriveFirstPathPoint();
            this.ArrivePathPoint();
        } else {
            return false;
        }
        return true;
    }

    /** 到达路径点 */
    ArrivePathPoint() {
        this.OnArrivePathPoint(this.pathPointRef);
        if(this.pathPointRef <= this.pathPointEndRef) {
            let queueHeadPathPointIndex = this._queueHeadPathPointIndex; // 队首路径点坐标
            let queueStartPathPointIndex = this._queueStartPathPointIndex; // 开始排队的路径点坐标
            if(this.pathPointRef == queueStartPathPointIndex) { // 排队开始
                // console.log(`开始排队！`);
                this.QueueUp();
            } else if(this.pathPointRef == queueHeadPathPointIndex) { // 排队中途到达
                if(!this._isQueueArrived) {
                    this._isQueueArrived = true;
                    let curIndex = this._queueLocationIndex;
                    // 判断是否需要转到下一个排队点
                    if(this._queueCurMiddleIndex < this._queueTargetMiddleIndex) { // 目标为中途点
                        // console.log(`到达站点！ ${this._queueIndex}`);
                        this.GoToQueuePathPoint(queueHeadPathPointIndex, curIndex);
                        this._isQueueArrived = false;
                    } else if(this._queueCurMiddleIndex == this._queueTargetMiddleIndex) { // 目标为目标点
                        this.GoToQueuePathPoint(queueHeadPathPointIndex, curIndex);
                        // if(this._queueIndex == 0 || this._queueIndex == 1 || this._queueIndex == 2) {
                        if(curIndex == 0) {
                            // console.log(`[${this.unit_id}] 到达队首点！ ${curIndex}`);
                            this._onArriveQueueHead && this._onArriveQueueHead();
                            this._onArriveQueueHead = null;
                        } else {
                            // console.log(`士兵到达点..！ ${this._queueIndex}`);
                        }
                    } else if(this._queueCurMiddleIndex > this._queueTargetMiddleIndex) {
                        console.error(`[${this.unit_id}] 超出站点！ ${curIndex}`);
                    }
                } else {
                }
            } else {
                // 直接去下一个路径点
                this.GoToNextPathPoint();
            }
        } else {
            console.warn(`已到达终点，无效目标：${this.pathPointRef} (${this.unit_id})`);
            // this.OnArriveTrackEnd();
        }
    }

    OnArrivePathPoint(pointRef: number) {
        // console.log(`到达路径点：${pointRef}`);
    }

    /** 前往下一个路径点，如果前方已无下一个路径点，触发终止回调 */
    GoToNextPathPoint(isOffset = false, offsetNum = 0) {
        // console.log(`go to next path point! speed: ${this.attribute.finalMoveSpeed}`);
        this.pathPointRef += 1;
        if(this.pathPointRef <= this.pathPointEndRef) {
            // if(isOffset) {
            //     this.SetNewPathPoint(offsetNum, this.pathPointRef - 1, this.pathPointRef);
            // } else {
                this.movingPathPointPos = this.GetPathPointPosition(this.pathPointRef - 1);
            // }
            this.isMoveToPathPoint = true;
        } else {
            this.OnArriveTrackEnd();
        }
    }

    OnArriveTrackEnd() {
        // console.log(`到达终点！${this.unit_id}`);
        this.isMoveOnTrack = false;
        let onArriveEndCallback = this.onArriveEndCallback;
        this.onArriveEndCallback = null;
        onArriveEndCallback && onArriveEndCallback();
    }

    /** 预先设定排队点信息 */
    ReadyToQueueUp(queueLineIndex: number, head: number, start: number) {
        /** 1. 注册排队路径
         *  2. 设定排队点信息
         *  3. 将目标路径点设置到队伍队首
        */
        this._queueLineIndex = queueLineIndex;
        this._queueHeadPathPointIndex = this.pathPointEndRef - head - 1;
        this._queueStartPathPointIndex = this.pathPointEndRef - start - 1;
        this._queueMiddlePathPointRefs = [];
        for(let i = this._queueStartPathPointIndex; i < this._queueHeadPathPointIndex + 1; i++) {
            this._queueMiddlePathPointRefs.push(i);
        }
        this._queueLocationIndex = -1;
        this._queueCurMiddleIndex = -1;
    }

    /** 直接进入排队 */
    QueueUp() {
        let head = this._queueHeadPathPointIndex; // 队首路径点坐标
        let index = GameStuffManager.instance.UnitQueueIn(this._queueLineIndex, this);
        if(index >= 0) {
            this._queueLocationIndex = index;
            this._queueCurMiddleIndex = 0;
            this.GoToQueuePathPoint(head, index);
        } else {
            console.error(`异常！排队返回值为负！`);
            this.pathPointRef = head;
            this.GoToNextPathPoint();
        }
    }

    QueueOut() {
        GameStuffManager.instance.UnitQueueOut(this._queueLineIndex, this);
        this._queueHeadPathPointIndex = -1;
        this._queueStartPathPointIndex = -1;
        this._queueMiddlePathPointRefs = [];
        this._queueLineIndex = -1;
        this._queueLocationIndex = -1;
        this._queueCurMiddleIndex = -1;
        this._queueTargetMiddleIndex = -1;
        this._isQueueArrived = false;
    }

    GoToQueuePathPoint(pathPointIndex: number, offsetNum: number) {
        this.pathPointRef = pathPointIndex;
        this.SetNewPathPoint(offsetNum);
        this.isMoveToPathPoint = true;
    }

    /** 回调方式调用，重新设定单位的排队信息，向前走一步 */
    ResetNewQueueIndex(index: number) {
        this._queueLocationIndex = index;
        this.SetNewPathPoint(index);
        this.isMoveToPathPoint = true;
        this._isQueueArrived = false;
        if(index == 0) {
            // console.log(`[${this.unit_id}] 目标设定到队首了！ `);
            
            // this._onArrivedQueueHead && this._onArrivedQueueHead();
            // this._onArrivedQueueHead = null;
        }
    }
    
    /** 计算排队后要去的坐标 */
    // SetNewPathPoint(offsetNum: number, srcPathPointRef: number, trgPathPointRef: number) {
    //     let queueGap = this.queueGap;
    //     let srcPos = this.GetPathPointPosition(srcPathPointRef - 1);
    //     let trgPos = this.GetPathPointPosition(trgPathPointRef - 1);
    //     this.movingPathPointPos = trgPos.add(trgPos.sub(srcPos).normalize().mul(-offsetNum * queueGap));
    // }

    
    /**
     * 设置新排队的点位,改变 _movingPathPointPos 的值
     * - 支持多路径折线排队,请提前设置路径列表 _queueMiddlePathPointRefs
     *
     * @param {number} offsetNum 排队队列序号
     * @returns 
     */
    SetNewPathPoint(offsetNum: number) {
        // 排队的路径点排列
        let middlePointsIndexs = [...this._queueMiddlePathPointRefs].reverse();
        let middlePoints: cc.Vec2[] = [];
        middlePointsIndexs.forEach((e)=>{ middlePoints.push(this.GetPathPointPosition(e)); });
        
        let rtn = this.GetTargeOnPathPosAndTargetMiddleIndex(middlePoints, offsetNum);
        let pos = rtn.pos;
        let targetListMiddleIndex = rtn.targetIndex;

        // 折线路径下判断是前往下一个路径点还是前往目标点
        let isToMiddlePoint = this._queueCurMiddleIndex < targetListMiddleIndex;
        if(isToMiddlePoint) {
            // console.log(`中途点！${this._queueCurMiddleIndex}`);
            this._queueCurMiddleIndex += 1;
            this._queueTargetMiddleIndex = targetListMiddleIndex + 1;
            this.movingPathPointPos = middlePoints[middlePoints.length - 1 - this._queueCurMiddleIndex];
        } else {
            // console.log(`分配路径点 info:`, info);
            this.movingPathPointPos = pos;
            this._queueTargetMiddleIndex = targetListMiddleIndex;
        }
    }

    RefreshTrack(trackRef: number, isReverse: boolean = false): boolean {
        this.trackRef = trackRef;
        this.trackPosList = [];
        // 将走线点坐标加入路径点列表
        let pathPoints = GameUtils.rootPathPoints.GetTrackPathPoints(trackRef);
        if(isReverse) {
            pathPoints.reverse();
        }
        pathPoints.forEach((e)=>{
            this.trackPosList.push(cc.v2(e.getPosition()));
        });
        if(pathPoints.length == 0) {
            console.error(`路径点数量为 0 ！`);
            return false;
        }
        // console.log(`路径点已设置：trackRef: ${trackRef}, pathPoints.length: ${pathPoints.length}`);
        this.pathPointEndRef = pathPoints.length;
        return true;
    }

    SetOnArriveEndCallback(callback: Function) {
        this.onArriveEndCallback = callback;
    }

    SetOnArriveQueueHeadCallback(callback: Function) {
        this._onArriveQueueHead = callback;
    }

    GetPathPointPosition(pathPointIndex: number) {
        return this.trackPosList[pathPointIndex];
    }

    /** 给出中途点的索引，得到目标点和中途点索引 */
    GetTargeOnPathPosAndTargetMiddleIndex(middlePoints: cc.Vec2[], offsetNum: number = 0): {pos: cc.Vec2, targetIndex: number} {
        // 计算目标点在折线的哪个位置
        let info = Unit.GetPointInfoOnPath(middlePoints, 0, offsetNum * this._queueGap);
        let pos = Unit.GetTargeOnPathPos(middlePoints, 0, offsetNum * this._queueGap);
        let targetListMiddleIndex = middlePoints.length - 1 - info.endPosIndex;
        return {pos: pos, targetIndex: targetListMiddleIndex};
    }

    /** 计算折线所在坐标 */
    static GetTargeOnPathPos(middlePoints: cc.Vec2[], startDistance: number, distance: number) {
        let info = this.GetPointInfoOnPath(middlePoints, startDistance, distance);
        let startPos = middlePoints[info.startPosIndex];
        let endPos = middlePoints[info.endPosIndex];
        let pos = startPos.add(endPos.sub(startPos).normalize().mul(info.leftDistance));
        return pos;
    }

    /** 折线计算 */
    static GetPointInfoOnPath(points: cc.Vec2[], startDistance: number, distance: number):
          {startPosIndex: number, endPosIndex: number, leftDistance: number} {
        // console.log(`points:`, points, `startDistance: ${startDistance}, distance: ${distance}`);
        if(points.length < 2) return null;
        let leftDistance = startDistance + distance;
        let startPosIndex = 0;
        let endPosIndex = 1;
        for(let i = 0; i < points.length - 1; i++) {
            startPosIndex = i;
            endPosIndex = i + 1;
            if(i < points.length - 2) {
                let lineDistance = points[startPosIndex].sub(points[endPosIndex]).len();
                if(leftDistance <= lineDistance) {
                    break;
                }
                leftDistance -= lineDistance;
            }
        }
        return {startPosIndex, endPosIndex, leftDistance};
    }


    public static GetUnitId() {
        Unit._curId ++;
        return Unit._curId;
    }

    protected override onDestroy(): void {
        GameManager.instance.RemoveUpdateCallbackByID(this._gameUpdateCallbackId);
        // GameManager.instance.RemoveUpdateCallbackByID(this._gameUpdateCallbackId2);
        // GameManager.instance.RemoveUpdateCallbackByID(this._gameUpdateCallbackId3);
        // GameManager.instance.RemoveUpdateCallbackByID(this._moveUpdateCallbackId);
        this.DestroyUnitStateMachine();
        this.unitStateMachine.onDestroy();
        this.attribute.onDestroy();
        super.onDestroy();
    }
}

export interface CanGetHurtBehavior {
    selfScript: Stuff;

    rootPosition: cc.Vec2;
    centerPosition: cc.Vec2;
    GetHurt(getHurtBehavior: CanGetHurtBehavior, damage: number): void;
}

export interface MoveOnTrackBehavior {
    trackRef: number;
    trackPosList: cc.Vec2[];
    isMoveOnTrack: boolean;
    isMoveToPathPoint: boolean;
    pathPointRef: number;
    pathPointEndRef: number;
    movingPathPointPos: cc.Vec2;
    onArriveEndCallback: Function;

    MoveToTrack(trackRef: number): boolean;
    ArrivePathPoint(): void;
    OnArrivePathPoint(pointRef: number): void;
    GoToNextPathPoint(isOffset?: boolean, offsetNum?: number): void;
    OnArriveTrackEnd(): void;
    SetOnArriveEndCallback(callback: Function): void;
}