// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import GameManager from "../GameManager";
import GameUtils, { UnitInfo } from "../Game/GameUtils";
import LocalUtils from "../LocalUtils";
import AntiExtrusionManager from "../Game/AntiExtrusionManager";
import Building from "./Building";
import GameDirector from "../Game/GameDirector";
import GameStuffManager from "../Game/GameStuffManager";
import Hero from "./Hero";
import NPC from "./NPC";
import { ResourceType } from "./Resource";
import StatueUnlockManager from "../Game/StatueUnlockManager";
import Stuff from "./Stuff";

const {ccclass, property} = cc._decorator;

@ccclass
export default class Tower extends Building {
    // 解锁后生成塔
    // 塔上生成 NPC
    // 升级塔可以增加 NPC

    @property(cc.Integer)
    tower_id: number = 0;

    static npcPositionsConfig = [
        // cc.v2(-15, 10),
        // cc.v2(20, 0),
        cc.v2(0, 0),
        cc.v2(70, -22),
        cc.v2(7, -40),
        cc.v2(28, 30),
        cc.v2(-53, -10),
        // cc.v2(60, 15),
        cc.v2(114, -2),
    ]

    unitList: UnitInfo[] = [];

    towerLevel = 0;
    towerBaseHeight = 170;
    // towerBaseHeight = 260;
    towerBaseUnlockDistance = 150;
    towerHeightPerLevel = 20;

    entityNode: cc.Node = null;
    mountParent: cc.Node = null;

    upgradeProgress = 0;
    upgradeMaxProgress = 50;

    isCanUpgrade = false;
    isUpgradeFinish = false;

    isUnitStandOn = false;

    private _isUpgraded = false;

    private _isRedSidelineShowing = false;

    // LIFE-CYCLE CALLBACKS:
    // onLoad () {}
    // start () {}
    // update (dt) {}
    
    protected override InitOnLoad() {
        super.InitOnLoad();
        this.entityNode = this.node.getChildByName('entityNode');
        this.mountParent = this.node.getChildByName('mountParent');
        this._rootPosition = cc.v2(this.entityNode.getPosition());

        this.gameUpdateCallbackId = GameManager.instance.AddGameUpdate('Tower', (dt: number)=>{
            this.gameUpdate(dt);
        }, false, 2050);

        this.isCanUnlock = true;

        this.towerBaseUnlockDistance = this.unlockDistance;

        // GameManager.instance.LateTimeCallOnce(()=>{
        //     this.towerLevel += 1;
        //     // if(this.tower_id == 0 || this.tower_id == 1) {
        //     //     this.CreateNewNPC();
        //     // } else {
        //         GameUtils.OpenHeroChoosePanelOnTower(this.tower_id - 1);
        //     // }
        //     // this.CreateNewHero(3);
        // }, 0.5);
    }

    override Init() {
        this._rootPosition = cc.v2(this.entityNode.getPosition());
    }
    
    protected override gameUpdate(dt: number) {
        if(this.isStatue) {
            if(this.isCanUnlock && !this.isUnlockFinished) {
                if(!this._isCanUseCoinUnlock && this.JudgDistance()) {
                    if(!this._isFirstUnlock) {
                        this._isFirstUnlock = true;
                        this._isCanUseCoinUnlock = true;
                    }
                }
                if(this._isCanUseCoinUnlock) {
                    let coinTime = this.GetCoinTime();
                    if(this._isUsingNextCoin) {
                        this._nextCoinTime += dt;
                        if(this._nextCoinTime >= coinTime) {
                            this._isUsingNextCoin = false;
                        }
                    }
                    this._isUseNextCoin = true;
                    while(this._isUseNextCoin && !this._isUsingNextCoin) {
                        this._isUseNextCoin = this.TryUseCoinUnlock();
                        if(this._isUseNextCoin) {
                            coinTime = this.GetCoinTime();
                            // let coinTime = 0.03;
                            this._nextCoinTime -= coinTime;
                            if(this._nextCoinTime >= coinTime) {
                                this._isUsingNextCoin = false;
                            } else {
                                this._isUsingNextCoin = true;
                            }
                        }
                    }
                }
            }
        }
        this.ResortChildren();
        // this.RefreshRedSideline();
    }
/*
    TryUseResource() {
        if(this.JudgDistance()) {
            if(GameUtils.woodNum - 1 >= 0 && this.upgradeProgress < this.upgradeMaxProgress) {
                GameUtils.ReduceWood(1);
                this.upgradeProgress += 1;
                // if(!this._isFirstUnlock) {
                //     this._isFirstUnlock = true;
                // }
                this.UseResource();
                return true;
            }
        }
        return false;
    }

    UseResource() {
        GameUtils.rootGameWorldUI.PlayWoodUnlock(GameUtils.mainHero.script.centerPosition, this.rootPosition.add(cc.v2(0, -40)));
        GameManager.instance.LateTimeCallOnce(()=>{
            LocalUtils.PlaySound('coin');
            GameUtils.rootGameWorldUI.AddTowerUpgradeProgress(this.tower_id, 1);
        }, 0.5);
    }
*/
    override TryUseCoinUnlock() {
        if(this.JudgDistance()) {
            if(this.unlockProgress < this.unlockMaxProgress) {
                // console.log(`progress1: ${this.unlockProgress} / ${this.unlockMaxProgress}`);
                let pos = this.rootPosition;
                let isSucceed = GameUtils.mainHeroBackpack.ResourceCost(ResourceType.coin, pos, 50, ()=>{
                    LocalUtils.PlaySound('coin');
                    // cc.Tween.stopAllByTarget(this.node);
                    // cc.tween(this.node).set({scale: 1.05}).to(0.08, {scale: 1.1}).to(0.1, {scale: 1}).start();
                    StatueUnlockManager.instance.AddTowerUnlockProgress(this.tower_id, 1);
                });
                if(isSucceed) {
                    this.unlockProgress += 1;
                    if(this.unlockProgress >= this.unlockMaxProgress) {
                        this.OnUnlockProgressFull();
                    }
                }
                return true;
            }
        }
        return false;
    }

    override OnUnlock(): void {
        super.OnUnlock();
        this.isUnlockFinished = true;
        this.towerLevel += 1;
        GameManager.instance.LateTimeCallOnce(()=>{
            if(!this.isUnitStandOn) {
                this.StandOnUnit();
            }
        }, 0.5);
        GameStuffManager.instance.isTowerUnlocked[this.tower_id - 1] = true;
    }

    Unlock() {
        this.node.active = true;
        this.entityNode.scale = 0.3;
        let rb = this.node.getChildByName('rb');
        rb.scale = 0.3;
        cc.tween(this.entityNode).to(0.3, {scale: 1}).start();
        cc.tween(rb).to(0.3, {scale: 1}).start();

        // let mask = this.entityNode.getChildByName('mask').getComponent(cc.Mask);
        // let img = this.entityNode.getChildByName('mask').getChildByName('img');
        // img.y = -380;
        // cc.tween(img).to(0.5, {y: -50}).call(()=>{
        //     mask.enabled = false;
        // }).start();
        // GameUtils.CameraShake(0.4);
        // LocalUtils.PlaySound('door');
        GameManager.instance.LateTimeCallOnce(()=>{
            // this.isCanUpgrade = true;
            this.OnUnlock();
        }, 0.2);
        GameManager.instance.LateTimeCallOnce(()=>{
            let polygonCollider = rb.getComponent(cc.PhysicsPolygonCollider);
            AntiExtrusionManager.instance.CreateAPolygonFromCollider(polygonCollider, true);
        }, 0.35);
    }

    Upgrade() {
        this.towerLevel += 1;
        if(this.towerLevel <= 6) {
            // this.RefreshTowerHeight();
            // this.CreateNewNPC();
        } else {
            this.towerLevel = 6;
        }
        this.isCanUpgrade = false;
        GameManager.instance.LateTimeCallOnce(()=>{
            // this.isCanUpgrade = true;
        }, 1);
    }

    ShowRedSideline() {
        if(this.entityNode) {
            let redSideline = this.entityNode.getChildByName('red_sideline');
            redSideline.active = true;
            cc.Tween.stopAllByTarget(redSideline);
            redSideline.opacity = 0;
            cc.tween(redSideline).to(0.2, {opacity: 255}).call(()=>{
                cc.tween(redSideline).delay(0.2).to(0.5, {opacity: 50}, {easing: 'sineIn'}).to(0.5, {opacity: 255}, {easing: 'sineOut'}
                ).union().repeatForever().start();
            }).start();
            // if(GameUtils.rootGameWorldUI) {
            //     let resourcesNeedBubble = GameUtils.rootGameWorldUI.resourcesNeedBubbles[this.tower_id - 1];
            //     if(resourcesNeedBubble) {
            //         resourcesNeedBubble.active = true;
            //         let anim = resourcesNeedBubble.getComponent(cc.Animation);
            //         if(anim) { anim.setCurrentTime(0); }
            //     }
            // }
            this._isRedSidelineShowing = true;
        }
    }

    HideRedSideline() {
        if(this.entityNode) {
            let redSideline = this.entityNode.getChildByName('red_sideline');
            redSideline.active = false;
            // if(GameUtils.rootGameWorldUI) {
            //     let resourcesNeedBubble = GameUtils.rootGameWorldUI.resourcesNeedBubbles[this.tower_id - 1];
            //     if(resourcesNeedBubble) {
            //         resourcesNeedBubble.active = false;
            //     }
            // }
            this._isRedSidelineShowing = false;
        }
    }

    GetCheckingPushingStuff() {
        let checkingPushingStuff = null;
        if(this.tower_id == 1) {
            checkingPushingStuff = GameStuffManager.instance.mainStripLog;
        } else if(this.tower_id == 2) {
            checkingPushingStuff = GameStuffManager.instance.mainStripStone;
            if(checkingPushingStuff && checkingPushingStuff.isPushingInHoleContinue) {
            } else {
                let inHoleStone = GameStuffManager.instance.stripStones.find((e)=>{
                    return e.isPushingInHoleContinue;
                });
                if(inHoleStone) {
                    checkingPushingStuff = inHoleStone;
                }
            }
        }
        return checkingPushingStuff;
    }

    OnPushingStuffPushComplite() {
        let hero = (this.GetTowerHero() as Hero);
        if(hero) hero.TrySkill();
    }
    
/*
    RefreshTowerHeight() {
        let height = this.towerBaseHeight + this.towerHeightPerLevel * (this.towerLevel - 1);
        let scale = height / this.towerBaseHeight;
        this.entityNode.scale = scale;
        let rb = this.node.getChildByName('rb');
        rb.scale = scale;
        this.unlockDistance = this.towerBaseUnlockDistance * scale;

        this.npcList.forEach((e)=>{
            e.script.SetHeight(height);
        });
        let floorNode = GameUtils.rootGameWorldUI.floorDimians[this.tower_id];
        if(floorNode) {
            let scaleNode = floorNode.getChildByName('scaleNode');
            scaleNode.scale = scale;
        }
    }
*/

    StandOnUnit(isCreateNew: boolean = true, unit: UnitInfo = null) {
        if(isCreateNew) {
            // 解锁英雄
            // GameUtils.OpenHeroChoosePanelOnTower(this.tower_id - 1);

            // 解锁小兵
            this.CreateNewNPC();
        } else {
            let height = this.towerBaseHeight + this.towerHeightPerLevel * (this.towerLevel - 1);
            this.unitList.push(unit);
            unit.script.node.opacity = 255;
            unit.script.SetHeight(height);
            unit.script.node.parent = this.mountParent;
            unit.script.TP(this.rootPosition.add(Tower.npcPositionsConfig[this.towerLevel - 1]));
        }
        this.isUnitStandOn = true;
    }

    StandUnitGoDown() {
        let unit = this.unitList.pop();
        unit.script.node.parent = GameUtils.rootGameWorld.MiddleNode;
        unit.script.SetHeight(0);
        unit.script.TP(this.rootPosition.add(cc.v2(120, 80)));
        this.isUnitStandOn = false;
    }

    CreateNewNPC() {
        // let npc = this.GenerateNPC(this.rootPosition.add(cc.v2(0, 0)));
        let npc = this.GenerateNPC(this.rootPosition.add(Tower.npcPositionsConfig[this.towerLevel - 1]));
        let height = this.towerBaseHeight + this.towerHeightPerLevel * (this.towerLevel - 1);
        npc.script.SetHeight(height);
        (npc.script as NPC).isSoldierGotedWeapon = true;

        this.unitList.push(npc);
        LocalUtils.PlaySound('level_up');
        GameUtils.rootGameWorldUI.PlayLevelUp(false, npc.script, cc.v2(0, 260));
    }

    GenerateNPC(pos: cc.Vec2) {
        let prefab = GameUtils.rootNPCCreator.npcSoldierPrefab;

        let npcInfo: UnitInfo = null;
        let npcScript: NPC = null;

        let node = LocalUtils.GenerateNode(this.mountParent, prefab, cc.v3());
        npcScript = node.getComponent(NPC);
        npcInfo = new UnitInfo(node);
        npcInfo.script = npcScript;
        npcScript.info = npcInfo;
        npcScript.OnBorn();
        npcScript.isCanMove = false;
        GameManager.instance.LateFrameCall(()=>{
            npcScript.rootPosition = pos;
        });
        return npcInfo;
    }

    CreateNewHero(index: number) {
        let prefab = GameUtils.rootGameWorld.heroPrefabs[index];
        if(prefab) {
            // let node = LocalUtils.GenerateNode(this.mountParent, prefab, cc.v3());
            let node = LocalUtils.GenerateNode(GameUtils.rootGameWorld.MiddleNode, prefab, cc.v3());
            let heroInfo = new UnitInfo(node);
            let script = node.getComponent(Hero);
            
            heroInfo.script = script;
            script.info = heroInfo;
            // script.EquipSkillReleaser();
            script.OnBorn(()=>{
            });
            script.isCanMove = false;
            script.rbNode.active = false;

            GameStuffManager.instance.heroList.push(heroInfo);
            this.unitList.push(heroInfo);
            script.heroIndex = GameUtils.heroList.findIndex((e)=>{ return e.script.stuff_id == heroInfo.script.stuff_id; });

            script.standTowerId = this.tower_id;
            
            // script.TP(GameUtils.rootPathPoints.heroPathPointParent.children[0].getPosition());
            // script.MoveToTower(this.tower_id);
            // script.SetOnArrivePathEndCallback(()=>{
                GameManager.instance.LateFrameCall(()=>{
                    script.node.opacity = 255;
                    script.node.parent = this.mountParent;
                    script.TP(this.rootPosition.add(Tower.npcPositionsConfig[this.towerLevel - 1]));
                    let height = this.towerBaseHeight + this.towerHeightPerLevel * (this.towerLevel - 1);
                    script.SetHeight(height);
                    script.TurnFaceDir(false);
                    // script.EquipWeapon();
                    // GameUtils.rootGameWorldUI.GenerateNewHeroHPUI(script.heroIndex);
                    // GameUtils.rootGameWorldUI.GenerateNewSkillCDBarUI(script.heroIndex);
                    LocalUtils.PlaySound('level_up');
                    GameUtils.rootGameWorldUI.PlayLevelUp(false, script, cc.v2(0, 260));
                });
            // });
        }
    }

    GetTowerHero() {
        return GameUtils.heroList.find((e)=>{ return (e.script as Hero).standTowerId == this.tower_id; }).script;
    }
    
    JudgDistance() {
        let distance = GameUtils.mainHero.script.rootPosition.sub(this.rootPosition).len();
        return distance < this.unlockDistance;
    }

    ResortChildren() {
        let childs = this.mountParent.children;
    
        let stuffs = [];
        childs.forEach((e)=>{
            let stuff = e.getComponent(Stuff);
            if(e.isValid && stuff) {
                stuffs.push(stuff);
            }
        });
    
        stuffs.sort((a, b)=>{
            return b.rootPosition.y - a.rootPosition.y;
        });
    
        stuffs.forEach((e, index)=>{
            this.mountParent.insertChild(e.node, index);
            e.nodeSortIndex = index;
        });
    }

    DestroyAllUnit() {
        this.unitList.forEach((e)=>{
            e.script.DestroySelf();
        });
        this.unitList = [];
    }

    GetUseTime() {
        return 0.06;
    }
}
